<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;

class CleanSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settings:clean';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean duplicate settings and keep only website-used ones';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Current settings in database:');
        $currentSettings = Setting::all();
        foreach ($currentSettings as $setting) {
            $this->line("- {$setting->key} = {$setting->value}");
        }

        $this->info("\nCleaning settings table...");

        // Clear all settings first
        Setting::truncate();

        // Keep only the settings that will be used in the website
        $websiteSettings = [
            // Basic website info (for header/footer)
            [
                'key' => 'site_name',
                'value' => 'NGO Portal',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Website name displayed in header'
            ],
            [
                'key' => 'site_tagline',
                'value' => 'समाज सेवा में अग्रणी',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Website tagline/slogan'
            ],
            [
                'key' => 'site_logo',
                'value' => 'images/logo.png',
                'type' => 'image',
                'group' => 'general',
                'description' => 'Website logo'
            ],

            // Contact information (for footer)
            [
                'key' => 'contact_phone',
                'value' => '+91 7812991993',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Contact phone number'
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Contact email address'
            ],
            [
                'key' => 'contact_address',
                'value' => 'Mathri Nagar, Raipur, Chattisgarh, India',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Contact address'
            ],

            // Social media links (for footer)
            [
                'key' => 'facebook_url',
                'value' => 'https://facebook.com/ngoportal',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Facebook page URL'
            ],
            [
                'key' => 'twitter_url',
                'value' => 'https://twitter.com/ngoportal',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Twitter profile URL'
            ],
            [
                'key' => 'instagram_url',
                'value' => 'https://instagram.com/ngoportal',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Instagram profile URL'
            ],
            [
                'key' => 'youtube_url',
                'value' => 'https://youtube.com/ngoportal',
                'type' => 'text',
                'group' => 'social',
                'description' => 'YouTube channel URL'
            ],

            // SEO settings (for meta tags)
            [
                'key' => 'seo_title',
                'value' => 'NGO Portal - समाज सेवा में अग्रणी',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default page title for SEO'
            ],
            [
                'key' => 'seo_description',
                'value' => 'हमारा NGO शिक्षा, स्वास्थ्य और सामुदायिक विकास के क्षेत्र में काम करता है। दान करें और समाज की भलाई में योगदान दें।',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default meta description for SEO'
            ],
            [
                'key' => 'seo_keywords',
                'value' => 'NGO, दान, charity, donation, समाज सेवा, social service, शिक्षा, education',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'SEO keywords'
            ]
        ];

        foreach ($websiteSettings as $setting) {
            Setting::create($setting);
            $this->line("✓ Created: {$setting['key']}");
        }

        $this->info("\n✅ Settings cleaned! Only website-used settings remain.");
        $this->info("Total settings: " . Setting::count());
    }
}
