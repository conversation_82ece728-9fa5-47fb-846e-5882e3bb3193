<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Str;
use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\Cause;
use App\Models\Page;
use App\Helpers\SlugHelper;

class UpdateSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update all slugs to be generated from titles with hyphens instead of spaces';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting slug updates...');

        // Update Blog slugs
        $this->updateBlogSlugs();

        // Update BlogCategory slugs
        $this->updateBlogCategorySlugs();

        // Update Cause slugs
        $this->updateCauseSlugs();

        // Update Page slugs
        $this->updatePageSlugs();

        $this->info('All slugs updated successfully!');
    }

    private function updateBlogSlugs()
    {
        $this->info('Updating Blog slugs...');
        $blogs = Blog::all();

        foreach ($blogs as $blog) {
            $newSlug = Str::slug($blog->title);
            if ($blog->slug !== $newSlug) {
                $blog->slug = $this->ensureUniqueSlug($newSlug, Blog::class, $blog->id);
                $blog->save();
                $this->line("Updated blog: {$blog->title} -> {$blog->slug}");
            }
        }

        $this->info("Updated {$blogs->count()} blog slugs");
    }

    private function updateBlogCategorySlugs()
    {
        $this->info('Updating BlogCategory slugs...');
        $categories = BlogCategory::all();

        foreach ($categories as $category) {
            $newSlug = Str::slug($category->name);
            if ($category->slug !== $newSlug) {
                $category->slug = $this->ensureUniqueSlug($newSlug, BlogCategory::class, $category->id);
                $category->save();
                $this->line("Updated category: {$category->name} -> {$category->slug}");
            }
        }

        $this->info("Updated {$categories->count()} category slugs");
    }

    private function updateCauseSlugs()
    {
        $this->info('Updating Cause slugs...');
        $causes = Cause::all();

        foreach ($causes as $cause) {
            $newSlug = SlugHelper::generateSlug($cause->title);
            if ($cause->slug !== $newSlug) {
                $cause->slug = $this->ensureUniqueSlug($newSlug, Cause::class, $cause->id);
                $cause->save();
                $this->line("Updated cause: {$cause->title} -> {$cause->slug}");
            }
        }

        $this->info("Updated {$causes->count()} cause slugs");
    }

    private function updatePageSlugs()
    {
        $this->info('Updating Page slugs...');
        $pages = Page::all();

        foreach ($pages as $page) {
            $newSlug = SlugHelper::generateSlug($page->title);
            if ($page->slug !== $newSlug) {
                $page->slug = $this->ensureUniqueSlug($newSlug, Page::class, $page->id);
                $page->save();
                $this->line("Updated page: {$page->title} -> {$page->slug}");
            }
        }

        $this->info("Updated {$pages->count()} page slugs");
    }

    private function ensureUniqueSlug($slug, $modelClass, $excludeId = null)
    {
        $originalSlug = $slug;
        $counter = 1;

        while (true) {
            $query = $modelClass::where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
