<?php

namespace App\Helpers;

use Illuminate\Support\Str;

class SlugHelper
{
    /**
     * Hindi to English transliteration mapping
     */
    private static array $hindiToEnglish = [
        // Vowels
        'अ' => 'a', 'आ' => 'aa', 'इ' => 'i', 'ई' => 'ii', 'उ' => 'u', 'ऊ' => 'uu', 
        'ए' => 'e', 'ऐ' => 'ai', 'ओ' => 'o', 'औ' => 'au',
        
        // Consonants
        'क' => 'ka', 'ख' => 'kha', 'ग' => 'ga', 'घ' => 'gha', 'ङ' => 'nga',
        'च' => 'cha', 'छ' => 'chha', 'ज' => 'ja', 'झ' => 'jha', 'ञ' => 'nya',
        'ट' => 'ta', 'ठ' => 'tha', 'ड' => 'da', 'ढ' => 'dha', 'ण' => 'na',
        'त' => 'ta', 'थ' => 'tha', 'द' => 'da', 'ध' => 'dha', 'न' => 'na',
        'प' => 'pa', 'फ' => 'pha', 'ब' => 'ba', 'भ' => 'bha', 'म' => 'ma',
        'य' => 'ya', 'र' => 'ra', 'ल' => 'la', 'व' => 'va',
        'श' => 'sha', 'ष' => 'sha', 'स' => 'sa', 'ह' => 'ha',
        
        // Compound consonants
        'क्ष' => 'ksha', 'त्र' => 'tra', 'ज्ञ' => 'gya',
        
        // Vowel signs (matras)
        'ा' => 'aa', 'ि' => 'i', 'ी' => 'ii', 'ु' => 'u', 'ू' => 'uu',
        'े' => 'e', 'ै' => 'ai', 'ो' => 'o', 'ौ' => 'au',
        
        // Special characters
        '्' => '', // Halant (virama) - removes inherent vowel
        'ं' => 'n', // Anusvara
        'ः' => 'h', // Visarga
        
        // Numbers
        '०' => '0', '१' => '1', '२' => '2', '३' => '3', '४' => '4',
        '५' => '5', '६' => '6', '७' => '7', '८' => '8', '९' => '9',
        
        // Common punctuation
        '।' => '.', // Devanagari danda
        '॥' => '.', // Double danda
    ];

    /**
     * Generate a URL-friendly slug from Hindi or English text
     *
     * @param string $title
     * @param string $separator
     * @return string
     */
    public static function generateSlug(string $title, string $separator = '-'): string
    {
        if (empty($title)) {
            return '';
        }

        // First, try to transliterate Hindi characters
        $transliterated = self::transliterateHindi($title);
        
        // Generate slug
        $slug = strtolower($transliterated);
        
        // Remove special characters except letters, numbers, spaces, and hyphens
        $slug = preg_replace('/[^a-z0-9\s\-]/u', '', $slug);
        
        // Replace multiple spaces with single space
        $slug = preg_replace('/\s+/', ' ', $slug);
        
        // Replace spaces with separator
        $slug = str_replace(' ', $separator, $slug);
        
        // Replace multiple separators with single separator
        $slug = preg_replace('/[' . preg_quote($separator) . ']+/', $separator, $slug);
        
        // Remove leading and trailing separators
        $slug = trim($slug, $separator);
        
        return $slug;
    }

    /**
     * Transliterate Hindi text to English
     *
     * @param string $text
     * @return string
     */
    private static function transliterateHindi(string $text): string
    {
        $transliterated = $text;
        
        // Apply transliteration mapping
        foreach (self::$hindiToEnglish as $hindi => $english) {
            $transliterated = str_replace($hindi, $english, $transliterated);
        }
        
        return $transliterated;
    }

    /**
     * Generate a unique slug for a given model
     *
     * @param string $title
     * @param string $modelClass
     * @param int|null $excludeId
     * @param string $separator
     * @return string
     */
    public static function generateUniqueSlug(
        string $title, 
        string $modelClass, 
        ?int $excludeId = null, 
        string $separator = '-'
    ): string {
        $baseSlug = self::generateSlug($title, $separator);
        $slug = $baseSlug;
        $counter = 1;

        while (true) {
            $query = $modelClass::where('slug', $slug);
            
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $baseSlug . $separator . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if text contains Hindi characters
     *
     * @param string $text
     * @return bool
     */
    public static function containsHindi(string $text): bool
    {
        // Check for Devanagari Unicode range (U+0900 to U+097F)
        return preg_match('/[\x{0900}-\x{097F}]/u', $text) === 1;
    }
}
