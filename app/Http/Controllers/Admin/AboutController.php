<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AboutSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AboutController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $page = $request->get('page', 1);
        $perPage = 10;
        $sections = AboutSection::latest()->paginate($perPage, ['*'], 'page', $page);

        if ($request->ajax()) {
            return response()->json([
                'data' => $sections->items(),
                'has_more' => $sections->hasMorePages(),
                'current_page' => $sections->currentPage(),
                'total' => $sections->total()
            ]);
        }

        return view('admin.about.index', compact('sections'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $sectionTypes = [
            'hero' => 'Hero Section',
            'mission' => 'Mission',
            'vision' => 'Vision',
            'values' => 'Values',
            'team' => 'Team Member',
            'history' => 'History',
            'achievements' => 'Achievements',
            'custom' => 'Custom Section'
        ];
        
        return view('admin.about.create', compact('sectionTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'section_type' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'content' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|string|max:255',
            'button_text' => 'nullable|string|max:255',
            'button_url' => 'nullable|url|max:255',
            'status' => 'required|in:active,inactive',
        ]);

        $data = $request->only([
            'section_type', 'title', 'subtitle', 'content', 
            'icon', 'button_text', 'button_url', 'status'
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image_path'] = $request->file('image')->store('about', 'public');
        }

        // Handle extra data for team members
        if ($request->section_type === 'team') {
            $data['extra_data'] = [
                'position' => $request->input('position'),
                'experience' => $request->input('experience'),
                'email' => $request->input('email'),
                'phone' => $request->input('phone'),
            ];
        }

        // Auto-generate sort order (next highest number)
        $data['sort_order'] = AboutSection::max('sort_order') + 1;

        AboutSection::create($data);

        return redirect()->route('admin.about.index')
            ->with('success', 'About section created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(AboutSection $about)
    {
        return view('admin.about.show', compact('about'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AboutSection $about)
    {
        $sectionTypes = [
            'hero' => 'Hero Section',
            'mission' => 'Mission',
            'vision' => 'Vision',
            'values' => 'Values',
            'team' => 'Team Member',
            'history' => 'History',
            'achievements' => 'Achievements',
            'custom' => 'Custom Section'
        ];
        
        return view('admin.about.edit', compact('about', 'sectionTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AboutSection $about)
    {
        $request->validate([
            'section_type' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:500',
            'content' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|string|max:255',
            'button_text' => 'nullable|string|max:255',
            'button_url' => 'nullable|url|max:255',
            'status' => 'required|in:active,inactive',
        ]);

        $data = $request->only([
            'section_type', 'title', 'subtitle', 'content', 
            'icon', 'button_text', 'button_url', 'status'
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($about->image_path && Storage::disk('public')->exists($about->image_path)) {
                Storage::disk('public')->delete($about->image_path);
            }
            $data['image_path'] = $request->file('image')->store('about', 'public');
        }

        // Handle extra data for team members
        if ($request->section_type === 'team') {
            $data['extra_data'] = [
                'position' => $request->input('position'),
                'experience' => $request->input('experience'),
                'email' => $request->input('email'),
                'phone' => $request->input('phone'),
            ];
        }

        $about->update($data);

        return redirect()->route('admin.about.index')
            ->with('success', 'About section updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AboutSection $about)
    {
        // Delete associated image
        if ($about->image_path && Storage::disk('public')->exists($about->image_path)) {
            Storage::disk('public')->delete($about->image_path);
        }

        $about->delete();

        return redirect()->route('admin.about.index')
            ->with('success', 'About section deleted successfully!');
    }
}
