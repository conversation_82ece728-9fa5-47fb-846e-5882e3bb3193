<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Cause;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Helpers\SlugHelper;

class CauseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $page = $request->get('page', 1);
        $perPage = 10;
        $causes = Cause::orderBy('created_at', 'desc')->paginate($perPage, ['*'], 'page', $page);

        if ($request->ajax()) {
            return response()->json([
                'data' => $causes->items(),
                'has_more' => $causes->hasMorePages(),
                'current_page' => $causes->currentPage(),
                'total' => $causes->total()
            ]);
        }

        return view('admin.causes.index', compact('causes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.causes.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'short_description' => 'required|string|max:500',
            'description' => 'required|string',
            'featured_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'goal_amount' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'status' => 'required|in:active,completed,paused',
            'is_featured' => 'boolean',
        ]);

        $data = $request->all();
        $data['slug'] = SlugHelper::generateUniqueSlug($request->title, Cause::class);
        $data['is_featured'] = $request->has('is_featured');

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $request->file('featured_image')->store('causes', 'public');
        }

        Cause::create($data);

        return redirect()->route('admin.causes.index')
            ->with('success', 'Cause created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $cause = Cause::findOrFail($id);
        return view('admin.causes.show', compact('cause'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $cause = Cause::findOrFail($id);
        return view('admin.causes.edit', compact('cause'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $cause = Cause::findOrFail($id);
        $request->validate([
            'title' => 'required|string|max:255',
            'short_description' => 'required|string|max:500',
            'description' => 'nullable|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'goal_amount' => 'required|numeric|min:0',
            'raised_amount' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,completed',
            'is_featured' => 'boolean',
        ]);

        $data = $request->all();
        $data['is_featured'] = $request->has('is_featured');

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($cause->featured_image && Storage::disk('public')->exists($cause->featured_image)) {
                Storage::disk('public')->delete($cause->featured_image);
            }
            $data['featured_image'] = $request->file('featured_image')->store('causes', 'public');
        }

        $cause->update($data);

        return redirect()->route('admin.causes.index')
            ->with('success', 'Cause updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $cause = Cause::findOrFail($id);
        // Delete featured image
        if ($cause->featured_image && Storage::disk('public')->exists($cause->featured_image)) {
            Storage::disk('public')->delete($cause->featured_image);
        }

        $cause->delete();

        return redirect()->route('admin.causes.index')
            ->with('success', 'Cause deleted successfully!');
    }
}
