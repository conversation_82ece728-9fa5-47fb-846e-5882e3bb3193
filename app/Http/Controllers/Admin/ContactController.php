<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $page = $request->get('page', 1);
        $perPage = 10;
        $contacts = Contact::latest()->paginate($perPage, ['*'], 'page', $page);

        if ($request->ajax()) {
            // Transform the data to include is_read field for JavaScript compatibility
            $transformedData = $contacts->items();
            foreach ($transformedData as $contact) {
                $contact->is_read = $contact->status !== 'new';
            }

            return response()->json([
                'data' => $transformedData,
                'has_more' => $contacts->hasMorePages(),
                'current_page' => $contacts->currentPage(),
                'total' => $contacts->total()
            ]);
        }

        return view('admin.contacts.index', compact('contacts'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Contact $contact)
    {
        // Mark as read when viewed
        if ($contact->status === 'new') {
            $contact->markAsRead();
        }

        return view('admin.contacts.show', compact('contact'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact)
    {
        $contact->delete();

        return redirect()->route('admin.contacts.index')
            ->with('success', 'Contact message deleted successfully!');
    }
}
