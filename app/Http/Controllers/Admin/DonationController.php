<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Donation;
use App\Models\Cause;
use Illuminate\Http\Request;

class DonationController extends Controller
{
    /**
     * Display a listing of donations
     */
    public function index(Request $request)
    {
        $query = Donation::with('cause')->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by cause
        if ($request->filled('cause_id')) {
            $query->where('cause_id', $request->cause_id);
        }

        // Search by donor name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('donor_name', 'like', "%{$search}%")
                  ->orWhere('donor_email', 'like', "%{$search}%")
                  ->orWhere('transaction_id', 'like', "%{$search}%");
            });
        }

        $donations = $query->paginate(20);
        $causes = Cause::orderBy('title')->get();

        return view('admin.donations.index', compact('donations', 'causes'));
    }

    /**
     * Display the specified donation
     */
    public function show(Donation $donation)
    {
        return view('admin.donations.show', compact('donation'));
    }

    /**
     * Update donation status
     */
    public function updateStatus(Request $request, Donation $donation)
    {
        $request->validate([
            'status' => 'required|in:pending,completed,failed,refunded',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $oldStatus = $donation->status;
        $donation->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ]);

        // If donation is approved (completed) and was pending, update cause amount
        if ($request->status === 'completed' && $oldStatus === 'pending' && $donation->cause_id) {
            $cause = Cause::find($donation->cause_id);
            $cause->increment('raised_amount', $donation->amount);
        }

        // If donation is rejected/failed and was completed, decrease cause amount
        if (in_array($request->status, ['failed', 'refunded']) && $oldStatus === 'completed' && $donation->cause_id) {
            $cause = Cause::find($donation->cause_id);
            $cause->decrement('raised_amount', $donation->amount);
        }

        return redirect()->route('admin.donations.show', $donation)
            ->with('success', 'Donation status updated successfully!');
    }

    /**
     * Delete donation
     */
    public function destroy(Donation $donation)
    {
        // If donation was completed, decrease cause amount
        if ($donation->status === 'completed' && $donation->cause_id) {
            $cause = Cause::find($donation->cause_id);
            $cause->decrement('raised_amount', $donation->amount);
        }

        $donation->delete();

        return redirect()->route('admin.donations.index')
            ->with('success', 'Donation deleted successfully!');
    }

    /**
     * Dashboard statistics
     */
    public function dashboard()
    {
        $stats = [
            'total_donations' => Donation::count(),
            'completed_donations' => Donation::where('status', 'completed')->count(),
            'pending_donations' => Donation::where('status', 'pending')->count(),
            'total_amount' => Donation::where('status', 'completed')->sum('amount'),
            'qr_donations' => Donation::where('payment_method', 'qr_code')->count(),
            'gateway_donations' => Donation::where('payment_method', 'gateway')->count(),
        ];

        $recent_donations = Donation::with('cause')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $monthly_stats = Donation::selectRaw('MONTH(created_at) as month, COUNT(*) as count, SUM(amount) as total')
            ->where('status', 'completed')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return view('admin.donations.dashboard', compact('stats', 'recent_donations', 'monthly_stats'));
    }
}
