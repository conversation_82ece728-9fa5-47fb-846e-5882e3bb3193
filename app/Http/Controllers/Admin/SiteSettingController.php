<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SiteSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $settingGroups = [
            'general' => Setting::getByGroup('general'),
            'theme' => Setting::getByGroup('theme'),
            'contact' => Setting::getByGroup('contact'),
            'social' => Setting::getByGroup('social'),
            'payment' => Setting::getByGroup('payment'),
            'seo' => Setting::getByGroup('seo'),
        ];

        return view('admin.settings.index', compact('settingGroups'));
    }

    /**
     * Update site settings.
     */
    public function update(Request $request)
    {
        $request->validate([
            'settings' => 'required|array',
        ]);

        // Handle file uploads first
        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $key => $file) {
                $setting = Setting::where('key', $key)->first();

                if ($setting && $setting->type === 'image') {
                    // Delete old file if exists
                    if ($setting->value && Storage::disk('public')->exists($setting->value)) {
                        Storage::disk('public')->delete($setting->value);
                    }

                    // Store new file
                    $path = $file->store('settings', 'public');
                    $setting->update(['value' => $path]);
                }
            }
        }

        // Handle other settings
        foreach ($request->settings as $key => $value) {
            $setting = Setting::where('key', $key)->first();

            if ($setting && $setting->type !== 'image') {
                $setting->update(['value' => $value]);
            }
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully!');
    }

    /**
     * Reset settings to default.
     */
    public function reset()
    {
        // This will be implemented to reset to default values
        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings reset to default values!');
    }
}
