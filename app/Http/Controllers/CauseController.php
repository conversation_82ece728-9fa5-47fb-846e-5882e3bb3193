<?php

namespace App\Http\Controllers;

use App\Models\Cause;
use Illuminate\Http\Request;

class CauseController extends Controller
{
    /**
     * Display a listing of causes
     */
    public function index(Request $request)
    {
        // Start with base query - remove withTranslations() for now
        $query = Cause::query();

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('short_description', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // Filter by status - default to active
        $status = $request->get('status', 'active');
        if ($status) {
            $query->where('status', $status);
        }

        // Sort options
        $sortBy = $request->get('sort', 'newest');
        switch ($sortBy) {
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'goal_high':
                $query->orderBy('goal_amount', 'desc');
                break;
            case 'goal_low':
                $query->orderBy('goal_amount', 'asc');
                break;
            case 'progress':
                $query->orderByRaw('(raised_amount / goal_amount) DESC');
                break;
            case 'featured':
                $query->where('is_featured', true)->orderBy('sort_order')->orderBy('created_at', 'desc');
                break;
            default:
                $query->orderBy('is_featured', 'desc')->orderBy('sort_order')->orderBy('created_at', 'desc');
                break;
        }

        $causes = $query->paginate(12);

        // Get statistics - handle case when no causes exist
        $totalCauses = Cause::where('status', 'active')->count();
        $totalRaised = Cause::where('status', 'active')->sum('raised_amount') ?? 0;
        $totalGoal = Cause::where('status', 'active')->sum('goal_amount') ?? 0;

        return view('causes', compact(
            'causes',
            'totalCauses',
            'totalRaised',
            'totalGoal'
        ));
    }

    /**
     * Display the specified cause
     */
    public function show(Cause $cause)
    {
        // Check if cause is active
        if ($cause->status !== 'active') {
            abort(404);
        }

        // Load relationships
        $cause->load(['donations' => function ($query) {
            $query->where('status', 'completed')->latest()->take(10);
        }]);

        // Get related causes
        $relatedCauses = Cause::where('status', 'active')
            ->where('id', '!=', $cause->id)
            ->where('is_featured', true)
            ->take(3)
            ->get();

        // Get donation statistics for this cause
        $totalDonations = $cause->donations()->where('status', 'completed')->count();
        $averageDonation = $cause->donations()->where('status', 'completed')->avg('amount') ?? 0;
        $recentDonations = $cause->donations()
            ->where('status', 'completed')
            ->where('is_anonymous', false)
            ->latest()
            ->take(5)
            ->get();

        return view('cause-show', compact(
            'cause',
            'relatedCauses',
            'totalDonations',
            'averageDonation',
            'recentDonations'
        ));
    }


}
