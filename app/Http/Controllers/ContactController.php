<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Display the contact form
     */
    public function index()
    {
        // Get contact and social media settings
        $settings = [
            'contact_address' => Setting::get('contact_address', '123 Main Street, City, State 12345, India'),
            'contact_phone' => Setting::get('contact_phone', '+91 12345 67890'),
            'contact_email' => Setting::get('contact_email', '<EMAIL>'),
            'office_hours_weekdays' => Setting::get('office_hours_weekdays', 'Monday - Friday: 9:00 AM - 6:00 PM'),
            'office_hours_saturday' => Setting::get('office_hours_saturday', 'Saturday: 9:00 AM - 2:00 PM'),
            'facebook_url' => Setting::get('facebook_url', '#'),
            'twitter_url' => Setting::get('twitter_url', '#'),
            'instagram_url' => Setting::get('instagram_url', '#'),
            'youtube_url' => Setting::get('youtube_url', '#'),
        ];

        return view('contact', compact('settings'));
    }

    /**
     * Store a new contact message
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            Contact::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'subject' => $request->subject,
                'message' => $request->message,
                'status' => 'new',
                'submitted_at' => now(),
            ]);

            return back()->with('success', 
                app()->getLocale() === 'hi' 
                    ? 'आपका संदेश सफलतापूर्वक भेजा गया है। हम जल्द ही आपसे संपर्क करेंगे।'
                    : 'Your message has been sent successfully. We will contact you soon.'
            );

        } catch (\Exception $e) {
            return back()->with('error', 
                app()->getLocale() === 'hi' 
                    ? 'संदेश भेजने में त्रुटि हुई है। कृपया पुनः प्रयास करें।'
                    : 'There was an error sending your message. Please try again.'
            );
        }
    }
}
