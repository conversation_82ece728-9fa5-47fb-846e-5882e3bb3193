<?php

namespace App\Http\Controllers;

use App\Models\Cause;
use App\Models\Donation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class DonationController extends Controller
{
    /**
     * Display the general donation page
     */
    public function index()
    {
        $featuredCauses = Cause::where('status', 'active')
            ->where('is_featured', true)
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        return view('donate', compact('featuredCauses'));
    }

    /**
     * Display donation page for a specific cause
     */
    public function cause(Cause $cause)
    {
        // Check if cause is active
        if ($cause->status !== 'active') {
            abort(404);
        }

        return view('donate-cause', compact('cause'));
    }

    /**
     * Process donation
     */
    public function process(Request $request)
    {
        // Determine validation rules based on payment method
        $rules = [
            'donor_name' => 'required|string|max:255',
            'donor_email' => 'required|email|max:255',
            'donor_phone' => 'nullable|string|max:20',
            'amount' => 'required|numeric|min:1',
            'cause_id' => 'nullable|exists:causes,id',
            'payment_method' => 'required|in:gateway,qr_code,bank_transfer,cash',
            'is_anonymous' => 'boolean',
        ];

        // Add specific validation for payment methods
        if ($request->payment_method === 'qr_code') {
            $rules['payment_screenshot'] = 'required|image|mimes:jpeg,png,jpg,gif|max:2048';
        } elseif ($request->payment_method === 'gateway') {
            $rules['payment_gateway'] = 'required|in:razorpay,stripe,paypal';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $paymentScreenshotPath = null;

            // Handle payment screenshot upload for QR payments
            if ($request->payment_method === 'qr_code' && $request->hasFile('payment_screenshot')) {
                $paymentScreenshotPath = $request->file('payment_screenshot')->store('donations/screenshots', 'public');
            }

            // Generate unique identifiers
            $timestamp = time();
            $randomId = rand(1000, 9999);
            $transactionId = 'TXN_' . $timestamp . '_' . $randomId;
            $receiptNumber = 'RCP_' . $timestamp . '_' . $randomId . '_' . date('Ymd');

            // Create donation record
            $donationData = [
                'donor_name' => $request->donor_name,
                'donor_email' => $request->donor_email,
                'donor_phone' => $request->donor_phone,
                'amount' => $request->amount,
                'currency' => 'INR',
                'cause_id' => $request->cause_id,
                'payment_method' => $request->payment_method,
                'status' => 'pending',
                'is_anonymous' => $request->boolean('is_anonymous'),
                'transaction_id' => $transactionId,
                'receipt_number' => $receiptNumber,
                'message' => $request->message,
                'payment_screenshot' => $paymentScreenshotPath,
            ];

            // Set payment gateway based on payment method
            if ($request->payment_method === 'gateway') {
                $donationData['payment_gateway'] = $request->payment_gateway;
            } else {
                // For QR code and other non-gateway payments, set a default value
                $donationData['payment_gateway'] = 'manual';
            }

            $donation = Donation::create($donationData);

            // For QR payments, set status to pending for admin verification
            // For gateway payments, you would integrate with actual payment gateways
            if ($request->payment_method === 'qr_code') {
                $donation->update([
                    'donated_at' => now(),
                ]);
            } else {
                // Here you would integrate with actual payment gateways
                // For now, we'll just simulate success
                $donation->update([
                    'status' => 'completed',
                    'donated_at' => now(),
                ]);

                // Update cause raised amount only for completed donations
                if ($donation->cause_id) {
                    $cause = Cause::find($donation->cause_id);
                    $cause->increment('raised_amount', $donation->amount);
                }
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => app()->getLocale() === 'hi'
                        ? 'आपका दान सफलतापूर्वक जमा हो गया है। धन्यवाद!'
                        : 'Your donation has been submitted successfully. Thank you!',
                    'donation_id' => $donation->id,
                    'redirect' => route('donate.success', $donation)
                ]);
            }

            return redirect()->route('donate.success', $donation);

        } catch (\Exception $e) {
            $errorMessage = app()->getLocale() === 'hi'
                ? 'दान प्रक्रिया में त्रुटि हुई है। कृपया पुनः प्रयास करें।'
                : 'There was an error processing your donation. Please try again.';

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $errorMessage
                ], 500);
            }

            return back()->with('error', $errorMessage);
        }
    }

    /**
     * Display donation success page
     */
    public function success(Donation $donation)
    {
        return view('donate-success', compact('donation'));
    }

    /**
     * Display donation cancel page
     */
    public function cancel()
    {
        return view('donate-cancel');
    }

    /**
     * Handle Razorpay webhook
     */
    public function razorpayWebhook(Request $request)
    {
        // Handle Razorpay webhook
        return response()->json(['status' => 'success']);
    }

    /**
     * Handle Stripe webhook
     */
    public function stripeWebhook(Request $request)
    {
        // Handle Stripe webhook
        return response()->json(['status' => 'success']);
    }

    /**
     * Handle PayPal webhook
     */
    public function paypalWebhook(Request $request)
    {
        // Handle PayPal webhook
        return response()->json(['status' => 'success']);
    }
}
