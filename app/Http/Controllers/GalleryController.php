<?php

namespace App\Http\Controllers;

use App\Models\Gallery;
use Illuminate\Http\Request;

class GalleryController extends Controller
{
    /**
     * Display a listing of gallery albums
     */
    public function index(Request $request)
    {
        $query = Gallery::where('status', 'active');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'latest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        $albums = $query->withCount('items')->paginate(12);

        return view('gallery', compact('albums'));
    }

    /**
     * Display the specified gallery album
     */
    public function show(Gallery $album)
    {
        // Check if album is active
        if ($album->status !== 'active') {
            abort(404);
        }

        // Load gallery items ordered by sort_order
        $album->load(['items' => function($query) {
            $query->orderBy('sort_order')->orderBy('created_at');
        }]);

        return view('gallery-show', compact('album'));
    }
}
