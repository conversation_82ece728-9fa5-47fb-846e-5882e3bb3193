<?php

namespace App\Http\Controllers;

use App\Models\Cause;
use App\Models\Blog;
use App\Models\Donation;
use App\Models\Slider;
use App\Models\Gallery;
use App\Models\AboutSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class HomeController extends Controller
{
    /**
     * Display the home page
     */
    public function index()
    {
        // Get slider images
        $sliders = Slider::active()
            ->ordered()
            ->get();

        // Get featured causes
        $featuredCauses = Cause::active()
            ->featured()
            ->ordered()
            ->take(3)
            ->get();

        // Get recent blogs
        $recentBlogs = Blog::published()
            ->with(['category'])
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // Get recent gallery albums
        $recentGalleries = Gallery::active()
            ->withCount('items')
            ->ordered()
            ->take(3)
            ->get();

        // Get donation statistics
        $totalDonations = Donation::completed()->sum('amount');
        $totalDonors = Donation::completed()->distinct('donor_email')->count();
        $activeCauses = Cause::active()->count();

        // SEO Meta
        $seoMeta = (object) [
            'meta_title' => App::getLocale() === 'hi' 
                ? 'एनजीओ पोर्टल - समाज में बदलाव लाना' 
                : 'NGO Portal - Making a Difference',
            'meta_description' => App::getLocale() === 'hi'
                ? 'हमारे विभिन्न पहलों और कार्यक्रमों के माध्यम से समाज पर सकारात्मक प्रभाव डालने में हमारे साथ जुड़ें।'
                : 'Join us in making a positive impact on society through our various initiatives and programs.',
            'og_type' => 'website',
        ];

        return view('home', compact(
            'sliders',
            'featuredCauses',
            'recentBlogs',
            'recentGalleries',
            'totalDonations',
            'totalDonors',
            'activeCauses',
            'seoMeta'
        ));
    }

    /**
     * Display the about page
     */
    public function about()
    {
        // Get dynamic about sections
        $heroSection = AboutSection::getHeroSection();
        $missionSection = AboutSection::getMissionSection();
        $visionSection = AboutSection::getVisionSection();
        $valuesSections = AboutSection::getValuesSections();
        $teamSections = AboutSection::getTeamSections();
        $historySections = AboutSection::getSectionsByType('history');
        $achievementsSections = AboutSection::getSectionsByType('achievements');
        $customSections = AboutSection::getSectionsByType('custom');

        // SEO Meta
        $seoMeta = (object) [
            'meta_title' => App::getLocale() === 'hi'
                ? 'हमारे बारे में - एनजीओ पोर्टल'
                : 'About Us - NGO Portal',
            'meta_description' => App::getLocale() === 'hi'
                ? 'हमारे मिशन, विजन और समाज सेवा के प्रति हमारी प्रतिबद्धता के बारे में जानें।'
                : 'Learn about our mission, vision and commitment to social service.',
            'og_type' => 'website',
        ];

        return view('about', compact(
            'heroSection',
            'missionSection',
            'visionSection',
            'valuesSections',
            'teamSections',
            'historySections',
            'achievementsSections',
            'customSections',
            'seoMeta'
        ));
    }

    /**
     * Generate sitemap
     */
    public function sitemap()
    {
        $causes = Cause::active()->get();
        $blogs = Blog::published()->get();

        return response()->view('sitemap', compact('causes', 'blogs'))
            ->header('Content-Type', 'text/xml');
    }

    /**
     * Generate robots.txt
     */
    public function robots()
    {
        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Sitemap: " . url('/sitemap.xml') . "\n";

        return response($content)->header('Content-Type', 'text/plain');
    }
}
