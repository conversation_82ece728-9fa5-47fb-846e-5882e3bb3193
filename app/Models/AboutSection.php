<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AboutSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'section_type',
        'title',
        'subtitle',
        'content',
        'image_path',
        'icon',
        'button_text',
        'button_url',
        'extra_data',
        'sort_order',
        'status',
    ];

    protected $casts = [
        'extra_data' => 'array',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('section_type', $type);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'asc');
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        if ($this->image_path) {
            // Check if file exists in storage
            $storagePath = 'storage/' . $this->image_path;
            if (file_exists(public_path($storagePath))) {
                return asset($storagePath);
            }

            // Check if file exists in public directory (for seeded data)
            if (file_exists(public_path($this->image_path))) {
                return asset($this->image_path);
            }
        }

        return null;
    }

    // Helper methods
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    // Static methods for common section types
    public static function getHeroSection()
    {
        return static::active()->byType('hero')->first();
    }

    public static function getMissionSection()
    {
        return static::active()->byType('mission')->first();
    }

    public static function getVisionSection()
    {
        return static::active()->byType('vision')->first();
    }

    public static function getValuesSections()
    {
        return static::active()->byType('values')->ordered()->get();
    }

    public static function getTeamSections()
    {
        return static::active()->byType('team')->ordered()->get();
    }

    public static function getSectionsByType($type)
    {
        return static::active()->byType($type)->ordered()->get();
    }
}
