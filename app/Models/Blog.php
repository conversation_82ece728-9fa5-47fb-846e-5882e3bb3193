<?php

namespace App\Models;

use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Helpers\SlugHelper;

class Blog extends Model
{
    use HasFactory, Translatable;

    protected $fillable = [
        'title',
        'slug',
        'excerpt',
        'content',
        'featured_image',
        'tags',
        'category_id',
        'author_id',
        'status',
        'published_at',
        'views',
        'is_featured',
    ];

    protected $casts = [
        'tags' => 'array',
        'published_at' => 'datetime',
        'is_featured' => 'boolean',
    ];

    // Translatable fields
    protected $translatable = [
        'title',
        'excerpt',
        'content',
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(BlogCategory::class);
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function seoMeta()
    {
        return $this->hasMany(SeoMeta::class, 'page_id')->where('page_type', 'blog');
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('status', 'published')
                    ->where('published_at', '<=', now());
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeByTag($query, $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    // Accessors
    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            // Check if file exists in storage
            $storagePath = 'storage/' . $this->featured_image;
            if (file_exists(public_path($storagePath))) {
                return asset($storagePath);
            }

            // Check if file exists in public directory (for seeded data)
            if (file_exists(public_path($this->featured_image))) {
                return asset($this->featured_image);
            }
        }

        // Use site logo as fallback
        $siteLogo = \App\Models\Setting::getImageUrl('logo');
        return $siteLogo ?: asset('images/default-blog.jpg');
    }

    public function getReadingTimeAttribute()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $readingTime = ceil($wordCount / 200); // Average reading speed
        return $readingTime . ' मिनट पढ़ने का समय';
    }

    public function getFormattedPublishedDateAttribute()
    {
        return $this->published_at ? $this->published_at->format('d M Y') : '';
    }

    // Mutators
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;
        $this->attributes['slug'] = SlugHelper::generateSlug($value);
    }

    // Methods
    public function incrementViews()
    {
        $this->increment('views');
    }

    // Boot method
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($blog) {
            if (empty($blog->slug)) {
                $blog->slug = SlugHelper::generateUniqueSlug($blog->title, self::class);
            }
            
            if (empty($blog->excerpt)) {
                $blog->excerpt = Str::limit(strip_tags($blog->content), 150);
            }
        });
    }
}


