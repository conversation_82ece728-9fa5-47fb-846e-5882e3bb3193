<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Helpers\SlugHelper;

class Cause extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'short_description',
        'description',
        'featured_image',
        'gallery',
        'goal_amount',
        'raised_amount',
        'start_date',
        'end_date',
        'status',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'gallery' => 'array',
        'goal_amount' => 'decimal:2',
        'raised_amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'is_featured' => 'boolean',
    ];



    // Relationships
    public function donations()
    {
        return $this->hasMany(Donation::class);
    }

    public function seoMeta()
    {
        return $this->hasMany(SeoMeta::class, 'page_id')->where('page_type', 'cause');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            // Check if file exists in storage
            $storagePath = 'storage/' . $this->featured_image;
            if (file_exists(public_path($storagePath))) {
                return asset($storagePath);
            }

            // Check if file exists in public directory (for seeded data)
            if (file_exists(public_path($this->featured_image))) {
                return asset($this->featured_image);
            }
        }

        // Use site logo as fallback
        $siteLogo = \App\Models\Setting::getImageUrl('logo');
        return $siteLogo ?: asset('images/default-cause.jpg');
    }

    public function getProgressPercentageAttribute()
    {
        if ($this->goal_amount <= 0) {
            return 0;
        }
        
        return min(100, ($this->raised_amount / $this->goal_amount) * 100);
    }

    public function getRemainingAmountAttribute()
    {
        return max(0, $this->goal_amount - $this->raised_amount);
    }

    public function getFormattedGoalAmountAttribute()
    {
        return '₹' . number_format($this->goal_amount, 0);
    }

    public function getFormattedRaisedAmountAttribute()
    {
        return '₹' . number_format($this->raised_amount, 0);
    }

    // Route key name
    public function getRouteKeyName()
    {
        return 'slug';
    }

    // Mutators
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;
        $this->attributes['slug'] = Str::slug($value);
    }

    // Boot method
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($cause) {
            if (empty($cause->slug)) {
                $cause->slug = SlugHelper::generateUniqueSlug($cause->title, self::class);
            }
        });
    }
}
