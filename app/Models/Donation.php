<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Donation extends Model
{
    protected $fillable = [
        'donor_name',
        'donor_email',
        'donor_phone',
        'donor_address',
        'amount',
        'currency',
        'cause_id',
        'payment_gateway',
        'transaction_id',
        'gateway_transaction_id',
        'gateway_response',
        'status',
        'message',
        'is_anonymous',
        'receipt_number',
        'payment_screenshot',
        'payment_method',
        'donated_at',
    ];

    protected $casts = [
        'gateway_response' => 'array',
        'is_anonymous' => 'boolean',
        'donated_at' => 'datetime',
        'amount' => 'decimal:2',
    ];

    // Relationships
    public function cause(): BelongsTo
    {
        return $this->belongsTo(Cause::class);
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeRefunded($query)
    {
        return $query->where('status', 'refunded');
    }

    // Helper methods
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    public function isRefunded(): bool
    {
        return $this->status === 'refunded';
    }

    public function getFormattedAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->amount, 2);
    }

    public function isQrPayment(): bool
    {
        return $this->payment_method === 'qr_code';
    }

    public function isGatewayPayment(): bool
    {
        return $this->payment_method === 'gateway';
    }

    public function getPaymentScreenshotUrlAttribute(): ?string
    {
        if ($this->payment_screenshot) {
            return asset('storage/' . $this->payment_screenshot);
        }
        return null;
    }
}
