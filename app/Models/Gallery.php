<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Gallery extends Model
{
    protected $table = 'gallery_albums';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'cover_image',
        'event_date',
        'sort_order',
        'status',
    ];

    protected $casts = [
        'event_date' => 'date',
    ];

    public function items()
    {
        return $this->hasMany(GalleryItem::class, 'album_id');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getCoverImageUrlAttribute()
    {
        if ($this->cover_image) {
            // Check if file exists in storage
            $storagePath = 'storage/' . $this->cover_image;
            if (file_exists(public_path($storagePath))) {
                return asset($storagePath);
            }

            // Check if file exists in public directory (for seeded data)
            if (file_exists(public_path($this->cover_image))) {
                return asset($this->cover_image);
            }
        }

        // Use a default gallery placeholder image as fallback
        // This creates a simple placeholder with the site logo centered
        return $this->getGalleryPlaceholderUrl();
    }

    /**
     * Generate a placeholder URL for gallery covers
     */
    private function getGalleryPlaceholderUrl()
    {
        // Check if we have a default gallery image
        if (file_exists(public_path('images/default-gallery.jpg'))) {
            return asset('images/default-gallery.jpg');
        }

        // Use site logo as fallback but indicate it's a placeholder
        $siteLogo = \App\Models\Setting::getImageUrl('logo');
        return $siteLogo ?: asset('images/logo.png');
    }

    public function getFormattedEventDateAttribute()
    {
        return $this->event_date ? $this->event_date->format('M d, Y') : null;
    }
}
