<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GalleryItem extends Model
{
    protected $table = 'gallery_items';

    protected $fillable = [
        'title',
        'description',
        'file_path',
        'file_type',
        'mime_type',
        'file_size',
        'dimensions',
        'album_id',
        'sort_order',
    ];

    protected $casts = [
        'dimensions' => 'array',
    ];

    public function album()
    {
        return $this->belongsTo(Gallery::class, 'album_id');
    }
}
