<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Page extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'featured_image',
        'sections',
        'status',
        'is_homepage',
        'sort_order',
    ];

    protected $casts = [
        'sections' => 'array',
        'is_homepage' => 'boolean',
    ];

    // Relationships
    public function seoMeta()
    {
        return $this->hasMany(SeoMeta::class, 'page_id')->where('page_type', 'page');
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeHomepage($query)
    {
        return $query->where('is_homepage', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    // Helper methods
    public function isPublished(): bool
    {
        return $this->status === 'published';
    }

    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    public function isHomepage(): bool
    {
        return $this->is_homepage;
    }

    // Accessors
    public function getFeaturedImageUrlAttribute()
    {
        return $this->featured_image ? asset('storage/' . $this->featured_image) : null;
    }

    public function getExcerptAttribute()
    {
        return \Str::limit(strip_tags($this->content), 150);
    }

    // Static methods
    public static function getHomepage()
    {
        return static::homepage()->published()->first();
    }

    public static function findBySlug($slug)
    {
        return static::where('slug', $slug)->published()->first();
    }
}
