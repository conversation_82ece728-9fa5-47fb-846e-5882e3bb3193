<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SeoMeta extends Model
{
    use HasFactory;

    protected $table = 'seo_meta';

    protected $fillable = [
        'page_type',
        'page_id',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'canonical_url',
        'og_title',
        'og_description',
        'og_image',
        'og_type',
        'twitter_card',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'structured_data',
    ];

    protected $casts = [
        'structured_data' => 'array',
    ];

    // Relationships
    public function page()
    {
        return $this->morphTo('page', 'page_type', 'page_id');
    }

    // Static methods
    public static function getForPage($pageType, $pageId = null)
    {
        return static::where('page_type', $pageType)
                    ->where('page_id', $pageId)
                    ->first();
    }

    public static function setForPage($pageType, $pageId, $data)
    {
        return static::updateOrCreate(
            [
                'page_type' => $pageType,
                'page_id' => $pageId,
            ],
            $data
        );
    }

    // Accessors
    public function getOgImageUrlAttribute()
    {
        return $this->og_image ? asset('storage/' . $this->og_image) : null;
    }

    public function getTwitterImageUrlAttribute()
    {
        return $this->twitter_image ? asset('storage/' . $this->twitter_image) : null;
    }

    // Methods
    public function generateStructuredData($type = 'WebPage')
    {
        $baseData = [
            '@context' => 'https://schema.org',
            '@type' => $type,
            'name' => $this->meta_title,
            'description' => $this->meta_description,
            'url' => $this->canonical_url ?: url()->current(),
        ];

        if ($this->og_image) {
            $baseData['image'] = $this->og_image_url;
        }

        return array_merge($baseData, $this->structured_data ?? []);
    }
}
