<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description',
    ];

    protected $casts = [
        'value' => 'string',
    ];

    // Helper method to get setting value
    public static function get($key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        return $setting ? $setting->value : $default;
    }

    // Helper method to get image URL
    public static function getImageUrl($key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        if (!$setting || !$setting->value) {
            return $default;
        }

        // If it's an uploaded file (starts with settings/), use storage path
        if (str_starts_with($setting->value, 'settings/')) {
            return asset('storage/' . $setting->value);
        }

        // Otherwise, use regular asset path
        return asset($setting->value);
    }

    // Helper method to set setting value
    public static function set($key, $value)
    {
        return static::updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );
    }

    // Get all settings as key-value pairs
    public static function getAllSettings()
    {
        return static::pluck('value', 'key')->toArray();
    }

    // Get settings by group
    public static function getByGroup($group)
    {
        return static::where('group', $group)->orderBy('id')->get();
    }

    // Accessor for image URLs
    public function getImageUrlAttribute()
    {
        if ($this->type === 'image' && $this->value) {
            return asset('storage/' . $this->value);
        }
        return $this->value;
    }

    // Accessor for boolean values
    public function getBooleanValueAttribute()
    {
        if ($this->type === 'boolean') {
            return filter_var($this->value, FILTER_VALIDATE_BOOLEAN);
        }
        return $this->value;
    }

    // Accessor for JSON values
    public function getJsonValueAttribute()
    {
        if ($this->type === 'json' && $this->value) {
            return json_decode($this->value, true);
        }
        return $this->value;
    }
}
