<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'avatar',
        'preferred_language',
        'status',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    // Relationships
    public function blogs()
    {
        return $this->hasMany(Blog::class, 'author_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Accessors
    public function getAvatarUrlAttribute()
    {
        if ($this->avatar) {
            // Check if file exists in storage
            $storagePath = 'storage/' . $this->avatar;
            if (file_exists(public_path($storagePath))) {
                return asset($storagePath);
            }

            // Check if file exists in public directory
            if (file_exists(public_path($this->avatar))) {
                return asset($this->avatar);
            }
        }

        // Use site logo as fallback for user avatars too
        $siteLogo = \App\Models\Setting::getImageUrl('logo');
        return $siteLogo ?: asset('images/default-avatar.png');
    }

    public function getPreferredLanguageNameAttribute()
    {
        $languages = config('app.supported_locales', []);
        return $languages[$this->preferred_language]['name'] ?? 'हिंदी';
    }
}
