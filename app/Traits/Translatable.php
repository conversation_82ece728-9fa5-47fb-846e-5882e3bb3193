<?php

namespace App\Traits;

use App\Models\Translation;
use Illuminate\Support\Facades\App;

trait Translatable
{
    /**
     * Get translation for a specific field and locale
     */
    public function getTranslation(string $field, string $locale = null): ?string
    {
        $locale = $locale ?: App::getLocale();
        
        // If requesting default locale, return original field
        if ($locale === config('app.locale', 'hi')) {
            return $this->getAttribute($field);
        }

        $translation = $this->translations()
            ->where('field', $field)
            ->where('locale', $locale)
            ->first();

        return $translation ? $translation->value : $this->getAttribute($field);
    }

    /**
     * Set translation for a specific field and locale
     */
    public function setTranslation(string $field, string $value, string $locale): void
    {
        // If setting default locale, update the original field
        if ($locale === config('app.locale', 'hi')) {
            $this->setAttribute($field, $value);
            $this->save();
            return;
        }

        $this->translations()->updateOrCreate(
            [
                'field' => $field,
                'locale' => $locale,
            ],
            [
                'value' => $value,
            ]
        );
    }

    /**
     * Get all translations for a field
     */
    public function getAllTranslations(string $field): array
    {
        $translations = [];
        
        // Add default language
        $translations[config('app.locale', 'hi')] = $this->getAttribute($field);
        
        // Add other translations
        $this->translations()
            ->where('field', $field)
            ->get()
            ->each(function ($translation) use (&$translations) {
                $translations[$translation->locale] = $translation->value;
            });

        return $translations;
    }

    /**
     * Relationship to translations
     */
    public function translations()
    {
        return $this->morphMany(Translation::class, 'translatable');
    }

    /**
     * Magic method to get translated attributes
     */
    public function __get($key)
    {
        // Check if this is a translatable field
        if (in_array($key, $this->getTranslatableFields())) {
            return $this->getTranslation($key);
        }

        return parent::__get($key);
    }

    /**
     * Get translatable fields for this model
     */
    protected function getTranslatableFields(): array
    {
        return property_exists($this, 'translatable') ? $this->translatable : [];
    }

    /**
     * Scope to get models with translations
     */
    public function scopeWithTranslations($query, string $locale = null)
    {
        $locale = $locale ?: App::getLocale();
        
        return $query->with(['translations' => function ($q) use ($locale) {
            $q->where('locale', $locale);
        }]);
    }
}
