<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->longText('value')->nullable();
            $table->string('type')->default('text'); // text, image, json, boolean
            $table->string('group')->default('general'); // general, theme, payment, seo, translation
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Multilingual content table
        Schema::create('translations', function (Blueprint $table) {
            $table->id();
            $table->string('translatable_type'); // Model class name
            $table->unsignedBigInteger('translatable_id'); // Model ID
            $table->string('locale', 5); // Language code (hi, en, etc.)
            $table->string('field'); // Field name (title, description, etc.)
            $table->longText('value'); // Translated content
            $table->timestamps();

            $table->index(['translatable_type', 'translatable_id']);
            $table->unique(['translatable_type', 'translatable_id', 'locale', 'field'], 'translations_unique');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('settings');
        Schema::dropIfExists('translations');
    }
};
