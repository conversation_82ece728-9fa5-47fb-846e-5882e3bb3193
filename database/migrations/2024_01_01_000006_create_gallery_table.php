<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('gallery_albums', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Default language (Hindi)
            $table->string('slug')->unique();
            $table->text('description')->nullable(); // Default language
            $table->string('cover_image')->nullable();
            $table->date('event_date')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        Schema::create('gallery_items', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // Default language (Hindi)
            $table->text('description')->nullable(); // Default language
            $table->string('file_path');
            $table->string('file_type'); // image, video
            $table->string('mime_type');
            $table->integer('file_size');
            $table->json('dimensions')->nullable(); // width, height for images
            $table->foreignId('album_id')->constrained('gallery_albums')->onDelete('cascade');
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('gallery_items');
        Schema::dropIfExists('gallery_albums');
    }
};
