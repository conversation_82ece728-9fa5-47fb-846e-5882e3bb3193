<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('seo_meta', function (Blueprint $table) {
            $table->id();
            $table->string('page_type'); // page, blog, cause, home, about, contact, etc.
            $table->unsignedBigInteger('page_id')->nullable(); // ID of the specific page/blog/cause
            $table->string('locale', 5)->default('hi'); // Language code
            $table->string('meta_title');
            $table->text('meta_description');
            $table->text('meta_keywords')->nullable();
            $table->string('canonical_url')->nullable();
            $table->string('og_title')->nullable();
            $table->text('og_description')->nullable();
            $table->string('og_image')->nullable();
            $table->string('og_type')->default('website');
            $table->string('twitter_card')->default('summary_large_image');
            $table->string('twitter_title')->nullable();
            $table->text('twitter_description')->nullable();
            $table->string('twitter_image')->nullable();
            $table->json('structured_data')->nullable(); // JSON-LD schema
            $table->timestamps();

            $table->unique(['page_type', 'page_id', 'locale']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('seo_meta');
    }
};
