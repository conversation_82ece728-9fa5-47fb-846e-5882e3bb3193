<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('donations', function (Blueprint $table) {
            // Add missing columns if they don't exist
            if (!Schema::hasColumn('donations', 'payment_screenshot')) {
                $table->string('payment_screenshot')->nullable()->after('receipt_number');
            }
            if (!Schema::hasColumn('donations', 'payment_method')) {
                $table->enum('payment_method', ['gateway', 'qr_code'])->default('gateway')->after('payment_screenshot');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('donations', function (Blueprint $table) {
            if (Schema::hasColumn('donations', 'payment_method')) {
                $table->dropColumn('payment_method');
            }
            if (Schema::hasColumn('donations', 'payment_screenshot')) {
                $table->dropColumn('payment_screenshot');
            }
        });
    }
};
