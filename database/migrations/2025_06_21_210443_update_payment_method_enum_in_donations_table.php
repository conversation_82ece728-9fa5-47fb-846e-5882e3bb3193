<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('donations', function (Blueprint $table) {
            // Update payment_method enum to include more options
            $table->enum('payment_method', ['gateway', 'qr_code', 'bank_transfer', 'cash'])->default('gateway')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('donations', function (Blueprint $table) {
            // Revert to original enum values
            $table->enum('payment_method', ['gateway', 'qr_code'])->default('gateway')->change();
        });
    }
};
