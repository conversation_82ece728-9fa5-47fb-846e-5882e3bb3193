<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AboutSection;

class AboutSectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sections = [
            [
                'section_type' => 'hero',
                'title' => 'हमारे बारे में',
                'subtitle' => 'समाज सेवा और मानवता के लिए समर्पित संगठन',
                'content' => 'हम एक गैर-लाभकारी संगठन हैं जो समाज के वंचित वर्गों के कल्याण के लिए काम करता है। हमारा उद्देश्य शिक्षा, स्वास्थ्य और सामाजिक न्याय के क्षेत्र में सकारात्मक बदलाव लाना है।',
                'icon' => 'fas fa-heart',
                'button_text' => 'हमसे जुड़ें',
                'button_url' => '/contact',
                'sort_order' => 1,
                'status' => 'active',
            ],
            [
                'section_type' => 'mission',
                'title' => 'हमारा मिशन',
                'subtitle' => 'समाज के उत्थान के लिए प्रतिबद्ध',
                'content' => 'समाज के वंचित वर्गों के उत्थान के लिए शिक्षा, स्वास्थ्य, और आर्थिक सहायता प्रदान करना। हमारा लक्ष्य एक न्यायसंगत और समान समाज का निर्माण करना है जहाँ हर व्यक्ति को विकास के समान अवसर मिलें।',
                'icon' => 'fas fa-bullseye',
                'sort_order' => 2,
                'status' => 'active',
            ],
            [
                'section_type' => 'vision',
                'title' => 'हमारा विजन',
                'subtitle' => 'एक बेहतर कल का सपना',
                'content' => 'एक ऐसे भारत का निर्माण जहाँ गरीबी, अशिक्षा और बीमारी का अंत हो। हम चाहते हैं कि हर बच्चा शिक्षा प्राप्त करे, हर परिवार को स्वास्थ्य सेवा मिले, और हर व्यक्ति सम्मान के साथ जीवन जी सके।',
                'icon' => 'fas fa-eye',
                'sort_order' => 3,
                'status' => 'active',
            ],
            [
                'section_type' => 'values',
                'title' => 'पारदर्शिता',
                'subtitle' => 'खुलेपन की नीति',
                'content' => 'हमारे सभी कार्य और वित्तीय लेन-देन पूर्णतः पारदर्शी हैं। हम अपने दानदाताओं और समुदाय के प्रति जवाबदेह हैं।',
                'icon' => 'fas fa-search',
                'sort_order' => 4,
                'status' => 'active',
            ],
            [
                'section_type' => 'values',
                'title' => 'ईमानदारी',
                'subtitle' => 'नैतिकता का पालन',
                'content' => 'हम अपने सभी कार्यों में ईमानदारी और नैतिकता का पालन करते हैं। हमारे लिए सत्यनिष्ठा सबसे महत्वपूर्ण है।',
                'icon' => 'fas fa-shield-alt',
                'sort_order' => 5,
                'status' => 'active',
            ],
            [
                'section_type' => 'values',
                'title' => 'करुणा',
                'subtitle' => 'दया और सहानुभूति',
                'content' => 'हम दूसरों के दुःख को समझते हैं और उनकी मदद के लिए तत्पर रहते हैं। करुणा हमारे काम की आत्मा है।',
                'icon' => 'fas fa-hands-helping',
                'sort_order' => 6,
                'status' => 'active',
            ],
            [
                'section_type' => 'team',
                'title' => 'राज कुमार शर्मा',
                'subtitle' => 'संस्थापक एवं अध्यक्ष',
                'content' => 'समाज सेवा के क्षेत्र में 25 वर्षों का अनुभव। शिक्षा और स्वास्थ्य के क्षेत्र में विशेषज्ञता।',
                'extra_data' => [
                    'position' => 'संस्थापक एवं अध्यक्ष',
                    'experience' => '25 वर्षों का सामाजिक कार्य का अनुभव',
                    'email' => '<EMAIL>',
                    'phone' => '+91 98765 43210',
                ],
                'sort_order' => 7,
                'status' => 'active',
            ],
            [
                'section_type' => 'team',
                'title' => 'प्रिया गुप्ता',
                'subtitle' => 'कार्यक्रम निदेशक',
                'content' => 'शिक्षा और महिला सशक्तिकरण के क्षेत्र में विशेषज्ञता। 15 वर्षों का अनुभव।',
                'extra_data' => [
                    'position' => 'कार्यक्रम निदेशक',
                    'experience' => '15 वर्षों का अनुभव',
                    'email' => '<EMAIL>',
                    'phone' => '+91 98765 43211',
                ],
                'sort_order' => 8,
                'status' => 'active',
            ],
            [
                'section_type' => 'team',
                'title' => 'डॉ. अमित वर्मा',
                'subtitle' => 'स्वास्थ्य सलाहकार',
                'content' => 'सामुदायिक स्वास्थ्य विशेषज्ञ। ग्रामीण स्वास्थ्य सेवाओं में 20 वर्षों का अनुभव।',
                'extra_data' => [
                    'position' => 'स्वास्थ्य सलाहकार',
                    'experience' => '20 वर्षों का अनुभव',
                    'email' => '<EMAIL>',
                    'phone' => '+91 98765 43212',
                ],
                'sort_order' => 9,
                'status' => 'active',
            ],
            [
                'section_type' => 'history',
                'title' => 'हमारा इतिहास',
                'subtitle' => '2000 से सेवा में',
                'content' => 'हमारी संस्था की स्थापना 2000 में हुई थी। तब से लेकर अब तक हमने हजारों लोगों की जिंदगी में सकारात्मक बदलाव लाया है। हमने शिक्षा, स्वास्थ्य और रोजगार के क्षेत्र में अनेक सफल परियोजनाएं चलाई हैं।',
                'icon' => 'fas fa-history',
                'sort_order' => 10,
                'status' => 'active',
            ],
            [
                'section_type' => 'achievements',
                'title' => 'हमारी उपलब्धियां',
                'subtitle' => 'गर्व की बात',
                'content' => '• 50,000+ बच्चों को शिक्षा प्रदान की\n• 100+ स्कूल स्थापित किए\n• 25,000+ परिवारों को स्वास्थ्य सेवा दी\n• 500+ महिलाओं को रोजगार दिलाया\n• 200+ गांवों में स्वच्छ पानी की व्यवस्था की',
                'icon' => 'fas fa-trophy',
                'sort_order' => 11,
                'status' => 'active',
            ],
        ];

        foreach ($sections as $section) {
            AboutSection::create($section);
        }
    }
}
