<?php

namespace Database\Seeders;

use App\Models\AboutSection;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AboutSectionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sections = [
            [
                'section_type' => 'hero',
                'title' => 'हमारे बारे में',
                'subtitle' => 'समाज सेवा में अग्रणी',
                'content' => 'हम एक गैर-लाभकारी संगठन हैं जो समाज के कल्याण के लिए काम करता है। हमारा मिशन है गरीबों और जरूरतमंदों की मदद करना और समाज में सकारात्मक बदलाव लाना।',
                'image_path' => 'images/logo.png',
                'button_text' => 'हमसे जुड़ें',
                'button_url' => '/contact',
                'sort_order' => 1,
                'status' => 'active'
            ],
            [
                'section_type' => 'mission',
                'title' => 'हमारा मिशन',
                'subtitle' => 'समाज में सकारात्मक बदलाव',
                'content' => 'हमारा मिशन है शिक्षा, स्वास्थ्य और सामाजिक कल्याण के क्षेत्र में काम करके समाज में सकारात्मक बदलाव लाना। हम गरीब और जरूरतमंद लोगों की मदद करके उनके जीवन में सुधार लाने का प्रयास करते हैं।',
                'icon' => 'fas fa-bullseye',
                'sort_order' => 2,
                'status' => 'active'
            ],
            [
                'section_type' => 'vision',
                'title' => 'हमारा विजन',
                'subtitle' => 'एक बेहतर समाज का निर्माण',
                'content' => 'हमारा विजन है एक ऐसे समाज का निर्माण करना जहां हर व्यक्ति को शिक्षा, स्वास्थ्य और सम्मान के साथ जीने का अधिकार हो। हम चाहते हैं कि समाज में कोई भी व्यक्ति भूखा न रहे और सभी को बेहतर जीवन मिले।',
                'icon' => 'fas fa-eye',
                'sort_order' => 3,
                'status' => 'active'
            ],
            [
                'section_type' => 'values',
                'title' => 'सत्यनिष्ठा',
                'subtitle' => 'ईमानदारी और पारदर्शिता',
                'content' => 'हम अपने सभी कार्यों में ईमानदारी और पारदर्शिता बनाए रखते हैं। हमारा हर कदम सत्यनिष्ठा पर आधारित है।',
                'icon' => 'fas fa-heart',
                'sort_order' => 4,
                'status' => 'active'
            ],
            [
                'section_type' => 'values',
                'title' => 'सेवा भावना',
                'subtitle' => 'निस्वार्थ सेवा',
                'content' => 'हमारी टीम निस्वार्थ भाव से समाज की सेवा करती है। हमारा उद्देश्य केवल मानवता की सेवा करना है।',
                'icon' => 'fas fa-hands-helping',
                'sort_order' => 5,
                'status' => 'active'
            ],
            [
                'section_type' => 'values',
                'title' => 'सामाजिक जिम्मेदारी',
                'subtitle' => 'समाज के प्रति दायित्व',
                'content' => 'हम अपनी सामाजिक जिम्मेदारी को समझते हैं और समाज के कल्याण के लिए निरंतर प्रयासरत रहते हैं।',
                'icon' => 'fas fa-users',
                'sort_order' => 6,
                'status' => 'active'
            ],
            [
                'section_type' => 'team',
                'title' => 'राज कुमार शर्मा',
                'subtitle' => 'संस्थापक एवं अध्यक्ष',
                'content' => '25 वर्षों से समाज सेवा में लगे हुए हैं और संगठन के मुख्य प्रेरणास्रोत हैं।',
                'sort_order' => 7,
                'status' => 'active'
            ],
            [
                'section_type' => 'team',
                'title' => 'प्रिया गुप्ता',
                'subtitle' => 'कार्यक्रम निदेशक',
                'content' => 'शिक्षा और स्वास्थ्य कार्यक्रमों की देखरेख करती हैं और 15 वर्षों का अनुभव रखती हैं।',
                'sort_order' => 8,
                'status' => 'active'
            ],
            [
                'section_type' => 'team',
                'title' => 'डॉ. अमित वर्मा',
                'subtitle' => 'स्वास्थ्य सलाहकार',
                'content' => 'चिकित्सा क्षेत्र में विशेषज्ञता रखते हैं और मुफ्त स्वास्थ्य शिविरों का आयोजन करते हैं।',
                'sort_order' => 9,
                'status' => 'active'
            ]
        ];

        foreach ($sections as $section) {
            AboutSection::updateOrCreate(
                [
                    'section_type' => $section['section_type']
                ],
                $section
            );
        }
    }
}
