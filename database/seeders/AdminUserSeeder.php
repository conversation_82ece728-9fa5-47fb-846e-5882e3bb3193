<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // This seeder is deprecated. Use UserSeeder instead which creates comprehensive admin users.
        $this->command->info('AdminUserSeeder is deprecated. Admin users are created in UserSeeder.');
        $this->command->info('Use the following admin credentials:');
        $this->command->info('Email: <EMAIL> | Password: admin123');
        $this->command->info('Email: <EMAIL> | Password: password123');
        $this->command->info('Email: <EMAIL> | Password: editor123');
    }
}
