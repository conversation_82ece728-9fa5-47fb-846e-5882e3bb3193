<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\BlogCategory;
use Illuminate\Support\Str;

class BlogCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'समुदायिक विकास',
                'slug' => 'community-development',
                'description' => 'समुदायिक विकास और सामाजिक कल्याण से संबंधित लेख',
                'color' => '#3B82F6',
            ],
            [
                'name' => 'शिक्षा',
                'slug' => 'education',
                'description' => 'शिक्षा और साक्षरता कार्यक्रमों के बारे में',
                'color' => '#10B981',
            ],
            [
                'name' => 'स्वास्थ्य',
                'slug' => 'health',
                'description' => 'स्वास्थ्य सेवा और जागरूकता कार्यक्रम',
                'color' => '#EF4444',
            ],
            [
                'name' => 'महिला सशक्तिकरण',
                'slug' => 'women-empowerment',
                'description' => 'महिला सशक्तिकरण और लैंगिक समानता',
                'color' => '#F59E0B',
            ],
            [
                'name' => 'पर्यावरण',
                'slug' => 'environment',
                'description' => 'पर्यावरण संरक्षण और जलवायु परिवर्तन',
                'color' => '#059669',
            ],
            [
                'name' => 'बाल कल्याण',
                'slug' => 'child-welfare',
                'description' => 'बच्चों के अधिकार और कल्याण कार्यक्रम',
                'color' => '#8B5CF6',
            ],
            [
                'name' => 'आपदा राहत',
                'slug' => 'disaster-relief',
                'description' => 'आपदा प्रबंधन और राहत कार्य',
                'color' => '#DC2626',
            ],
            [
                'name' => 'कृषि विकास',
                'slug' => 'agriculture-development',
                'description' => 'कृषि और ग्रामीण विकास कार्यक्रम',
                'color' => '#16A34A',
            ],
            [
                'name' => 'समाचार और घटनाएं',
                'slug' => 'news-events',
                'description' => 'संगठन की नवीनतम समाचार और घटनाएं',
                'color' => '#6366F1',
            ],
            [
                'name' => 'सफलता की कहानियां',
                'slug' => 'success-stories',
                'description' => 'प्रेरणादायक सफलता की कहानियां',
                'color' => '#F97316',
            ],
        ];

        foreach ($categories as $category) {
            // Check if category already exists
            $existingCategory = BlogCategory::where('slug', $category['slug'])->first();

            if (!$existingCategory) {
                BlogCategory::create($category);
            }
        }
    }
}
