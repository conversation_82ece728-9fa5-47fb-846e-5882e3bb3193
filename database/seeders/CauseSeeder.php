<?php

namespace Database\Seeders;

use App\Models\Cause;
use Illuminate\Database\Seeder;

class CauseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $causes = [
            [
                'title' => 'शिक्षा के लिए दान',
                'short_description' => 'गरीब बच्चों को गुणवत्तापूर्ण शिक्षा प्रदान करने में हमारी मदद करें।',
                'description' => 'शिक्षा हर बच्चे का मौलिक अधिकार है। हमारा मिशन है कि हम गरीब और वंचित बच्चों को गुणवत्तापूर्ण शिक्षा प्रदान करें। आपके दान से हम स्कूल की फीस, किताबें, यूनिफॉर्म और अन्य शैक्षणिक सामग्री प्रदान कर सकते हैं। एक बच्चे की शिक्षा में निवेश करना पूरे समाज के भविष्य में निवेश करना है।',
                'featured_image' => 'images/causes/education.jpg',
                'goal_amount' => 500000,
                'raised_amount' => 275000,
                'start_date' => now()->subDays(30),
                'end_date' => now()->addDays(60),
                'status' => 'active',
                'is_featured' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'स्वास्थ्य सेवा सहायता',
                'short_description' => 'जरूरतमंद लोगों को मुफ्त चिकित्सा सेवा प्रदान करने में योगदान दें।',
                'description' => 'स्वास्थ्य सेवा एक बुनियादी आवश्यकता है जो हर व्यक्ति को मिलनी चाहिए। हमारे स्वास्थ्य कार्यक्रम के तहत हम गरीब और जरूरतमंद लोगों को मुफ्त चिकित्सा सेवा, दवाइयां और स्वास्थ्य जांच प्रदान करते हैं। आपका दान हमें अधिक लोगों तक पहुंचने और उनके जीवन में सकारात्मक बदलाव लाने में मदद करेगा।',
                'featured_image' => 'images/causes/healthcare.jpg',
                'goal_amount' => 750000,
                'raised_amount' => 425000,
                'start_date' => now()->subDays(45),
                'end_date' => now()->addDays(45),
                'status' => 'active',
                'is_featured' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'भोजन वितरण कार्यक्रम',
                'short_description' => 'भूखे लोगों को पौष्टिक भोजन प्रदान करने में हमारा साथ दें।',
                'description' => 'भूख एक गंभीर समस्या है जो आज भी हमारे समाज में मौजूद है। हमारे भोजन वितरण कार्यक्रम के तहत हम दैनिक आधार पर सैकड़ों जरूरतमंद लोगों को पौष्टिक भोजन प्रदान करते हैं। आपका दान हमें अधिक लोगों तक भोजन पहुंचाने और भूख की समस्या से लड़ने में मदद करेगा।',
                'featured_image' => 'images/causes/food.jpg',
                'goal_amount' => 300000,
                'raised_amount' => 180000,
                'start_date' => now()->subDays(20),
                'end_date' => now()->addDays(70),
                'status' => 'active',
                'is_featured' => true,
                'sort_order' => 3,
            ],
            [
                'title' => 'महिला सशक्तिकरण',
                'short_description' => 'महिलाओं को आत्मनिर्भर बनाने के लिए कौशल विकास कार्यक्रम।',
                'description' => 'महिला सशक्तिकरण हमारे समाज की प्रगति के लिए अत्यंत महत्वपूर्ण है। हमारे कार्यक्रम के तहत हम महिलाओं को विभिन्न कौशल सिखाते हैं जैसे सिलाई, कढ़ाई, कंप्यूटर, और अन्य व्यावसायिक प्रशिक्षण। इससे वे आर्थिक रूप से स्वतंत्र बन सकती हैं और अपने परिवार का बेहतर भरण-पोषण कर सकती हैं।',
                'featured_image' => 'images/causes/women-empowerment.jpg',
                'goal_amount' => 400000,
                'raised_amount' => 150000,
                'start_date' => now()->subDays(15),
                'end_date' => now()->addDays(85),
                'status' => 'active',
                'is_featured' => false,
                'sort_order' => 4,
            ],
            [
                'title' => 'पर्यावरण संरक्षण',
                'short_description' => 'पेड़ लगाने और पर्यावरण की सुरक्षा के लिए हमारे साथ जुड़ें।',
                'description' => 'पर्यावरण संरक्षण आज के समय की सबसे बड़ी आवश्यकता है। हमारे पर्यावरण कार्यक्रम के तहत हम वृक्षारोपण, प्लास्टिक मुक्त अभियान, और जल संरक्षण के कार्य करते हैं। आपका योगदान हमें एक स्वच्छ और हरित भविष्य बनाने में मदद करेगा।',
                'featured_image' => 'images/causes/environment.jpg',
                'goal_amount' => 250000,
                'raised_amount' => 95000,
                'start_date' => now()->subDays(10),
                'end_date' => now()->addDays(90),
                'status' => 'active',
                'is_featured' => false,
                'sort_order' => 5,
            ],
        ];

        foreach ($causes as $causeData) {
            Cause::updateOrCreate(
                ['title' => $causeData['title']],
                $causeData
            );
        }
    }
}
