<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Contact;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $contacts = [
            [
                'name' => 'राहुल शर्मा',
                'email' => '<EMAIL>',
                'phone' => '+91 98765 43210',
                'subject' => 'शिक्षा कार्यक्रम के बारे में जानकारी',
                'message' => 'मैं आपके शिक्षा कार्यक्रम के बारे में जानना चाहता हूं। कृपया मुझे विस्तृत जानकारी भेजें कि मैं कैसे इसमें योगदान दे सकता हूं।',
                'language' => 'hi',
                'status' => 'new',
                'admin_notes' => null,
                'replied_at' => null,
                'created_at' => now()->subDays(2),
            ],
            [
                'name' => 'प्रिया गुप्ता',
                'email' => '<EMAIL>',
                'phone' => '+91 87654 32109',
                'subject' => 'स्वयंसेवक के रूप में काम करना चाहती हूं',
                'message' => 'मैं आपके संगठन के साथ स्वयंसेवक के रूप में काम करना चाहती हूं। मेरे पास शिक्षा के क्षेत्र में 5 साल का अनुभव है।',
                'language' => 'hi',
                'status' => 'read',
                'admin_notes' => 'अच्छी प्रोफाइल है, इंटरव्यू के लिए बुलाना है',
                'replied_at' => null,
                'created_at' => now()->subDays(5),
            ],
            [
                'name' => 'अमित कुमार',
                'email' => '<EMAIL>',
                'phone' => '+91 76543 21098',
                'subject' => 'दान रसीद की जानकारी',
                'message' => 'मैंने पिछले महीने ₹5000 का दान किया था। कृपया मुझे दान की रसीद भेजें।',
                'language' => 'hi',
                'status' => 'replied',
                'admin_notes' => 'रसीद भेज दी गई है',
                'replied_at' => now()->subDays(1),
                'created_at' => now()->subDays(3),
            ],
            [
                'name' => 'सुनीता देवी',
                'email' => '<EMAIL>',
                'phone' => '+91 65432 10987',
                'subject' => 'स्वास्थ्य शिविर की जानकारी',
                'message' => 'हमारे गांव में स्वास्थ्य शिविर लगवाना चाहते हैं। कृपया बताएं कि इसके लिए क्या प्रक्रिया है।',
                'language' => 'hi',
                'status' => 'new',
                'admin_notes' => null,
                'replied_at' => null,
                'created_at' => now()->subDays(1),
            ],
            [
                'name' => 'विकास अग्रवाल',
                'email' => '<EMAIL>',
                'phone' => '+91 54321 09876',
                'subject' => 'कॉर्पोरेट पार्टनरशिप',
                'message' => 'हमारी कंपनी आपके साथ CSR के तहत पार्टनरशिप करना चाहती है। कृपया मीटिंग का समय दें।',
                'language' => 'hi',
                'status' => 'read',
                'admin_notes' => 'अच्छा अवसर है, डायरेक्टर से बात करनी है',
                'replied_at' => null,
                'created_at' => now()->subDays(4),
            ],
            [
                'name' => 'मीरा जोशी',
                'email' => '<EMAIL>',
                'phone' => '+91 43210 98765',
                'subject' => 'महिला सशक्तिकरण कार्यक्रम',
                'message' => 'मैं आपके महिला सशक्तिकरण कार्यक्रम में भाग लेना चाहती हूं। कृपया बताएं कि रजिस्ट्रेशन कैसे करें।',
                'language' => 'hi',
                'status' => 'new',
                'admin_notes' => null,
                'replied_at' => null,
                'created_at' => now()->subHours(6),
            ],
            [
                'name' => 'रमेश चंद्र',
                'email' => '<EMAIL>',
                'phone' => '+91 32109 87654',
                'subject' => 'बुजुर्गों की देखभाल सेवा',
                'message' => 'मेरे पिताजी की उम्र 75 साल है और उन्हें देखभाल की जरूरत है। क्या आप घर पर सेवा प्रदान करते हैं?',
                'language' => 'hi',
                'status' => 'replied',
                'admin_notes' => 'होम केयर सर्विस की जानकारी दी गई',
                'replied_at' => now()->subHours(12),
                'created_at' => now()->subDays(2),
            ],
            [
                'name' => 'अंजली सिंह',
                'email' => '<EMAIL>',
                'phone' => '+91 21098 76543',
                'subject' => 'फंडरेजिंग इवेंट में सहयोग',
                'message' => 'मैं आपके अगले फंडरेजिंग इवेंट में सहयोग करना चाहती हूं। कृपया बताएं कि मैं कैसे मदद कर सकती हूं।',
                'language' => 'hi',
                'status' => 'read',
                'admin_notes' => 'इवेंट टीम से संपर्क कराना है',
                'replied_at' => null,
                'created_at' => now()->subDays(6),
            ],
        ];

        foreach ($contacts as $contact) {
            Contact::create($contact);
        }
    }
}
