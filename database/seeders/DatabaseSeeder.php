<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // Core settings and users first
            SettingsSeeder::class,
            UserSeeder::class,

            // Content seeders
            PageSeeder::class,
            BlogCategorySeeder::class,
            CauseSeeder::class,
            BlogSeeder::class,
            SliderSeeder::class,
            GallerySeeder::class,

            // Data that depends on other models
            DonationSeeder::class,
            ContactSeeder::class,

            // SEO meta should be last to ensure all pages exist
            SeoMetaSeeder::class,
        ]);
    }
}
