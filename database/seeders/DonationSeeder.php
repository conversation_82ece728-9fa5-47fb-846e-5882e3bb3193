<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Donation;
use App\Models\Cause;

class DonationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing causes
        $causes = Cause::all();
        
        if ($causes->isEmpty()) {
            $this->command->warn('No causes found. Please run CauseSeeder first.');
            return;
        }

        $donations = [
            [
                'donor_name' => 'राजेश कुमार',
                'donor_email' => '<EMAIL>',
                'donor_phone' => '+91 98765 43210',
                'donor_address' => 'A-123, सेक्टर 15, नोएडा, उत्तर प्रदेश',
                'amount' => 5000.00,
                'currency' => 'INR',
                'cause_id' => $causes->first()->id,
                'payment_gateway' => 'razorpay',
                'transaction_id' => 'TXN_' . time() . '_1001',
                'gateway_transaction_id' => 'pay_' . \Str::random(14),
                'gateway_response' => [
                    'razorpay_payment_id' => 'pay_' . \Str::random(14),
                    'razorpay_order_id' => 'order_' . \Str::random(14),
                    'razorpay_signature' => \Str::random(64),
                ],
                'status' => 'completed',
                'message' => 'शिक्षा के लिए दान',
                'is_anonymous' => false,
                'receipt_number' => 'RCP_' . time() . '_1_' . date('Ymd'),
                'donated_at' => now()->subDays(10),
                'created_at' => now()->subDays(10),
            ],
            [
                'donor_name' => 'प्रिया शर्मा',
                'donor_email' => '<EMAIL>',
                'donor_phone' => '+91 87654 32109',
                'donor_address' => 'B-456, दिल्ली',
                'amount' => 2500.00,
                'currency' => 'INR',
                'cause_id' => $causes->count() > 1 ? $causes->skip(1)->first()->id : $causes->first()->id,
                'payment_gateway' => 'razorpay',
                'transaction_id' => 'TXN_' . time() . '_1002',
                'gateway_transaction_id' => 'pay_' . \Str::random(14),
                'gateway_response' => [
                    'razorpay_payment_id' => 'pay_' . \Str::random(14),
                    'razorpay_order_id' => 'order_' . \Str::random(14),
                    'razorpay_signature' => \Str::random(64),
                ],
                'status' => 'completed',
                'message' => 'स्वास्थ्य सेवा के लिए योगदान',
                'is_anonymous' => false,
                'receipt_number' => 'RCP_' . (time() + 1) . '_2_' . date('Ymd'),
                'donated_at' => now()->subDays(8),
                'created_at' => now()->subDays(8),
            ],
            [
                'donor_name' => 'Anonymous Donor',
                'donor_email' => '<EMAIL>',
                'donor_phone' => null,
                'donor_address' => null,
                'amount' => 10000.00,
                'currency' => 'INR',
                'cause_id' => $causes->first()->id,
                'payment_gateway' => 'razorpay',
                'transaction_id' => 'TXN_' . time() . '_1003',
                'gateway_transaction_id' => 'pay_' . \Str::random(14),
                'gateway_response' => [
                    'razorpay_payment_id' => 'pay_' . \Str::random(14),
                    'razorpay_order_id' => 'order_' . \Str::random(14),
                    'razorpay_signature' => \Str::random(64),
                ],
                'status' => 'completed',
                'message' => 'गुमनाम दान',
                'is_anonymous' => true,
                'receipt_number' => 'RCP_' . (time() + 2) . '_3_' . date('Ymd'),
                'donated_at' => now()->subDays(15),
                'created_at' => now()->subDays(15),
            ],
            [
                'donor_name' => 'अमित गुप्ता',
                'donor_email' => '<EMAIL>',
                'donor_phone' => '+91 76543 21098',
                'donor_address' => 'C-789, गुड़गांव, हरियाणा',
                'amount' => 1500.00,
                'currency' => 'INR',
                'cause_id' => $causes->count() > 2 ? $causes->skip(2)->first()->id : $causes->first()->id,
                'payment_gateway' => 'razorpay',
                'transaction_id' => 'TXN_' . time() . '_1004',
                'gateway_transaction_id' => 'pay_' . \Str::random(14),
                'gateway_response' => [
                    'razorpay_payment_id' => 'pay_' . \Str::random(14),
                    'razorpay_order_id' => 'order_' . \Str::random(14),
                    'razorpay_signature' => \Str::random(64),
                ],
                'status' => 'completed',
                'message' => 'महिला सशक्तिकरण के लिए',
                'is_anonymous' => false,
                'receipt_number' => 'RCP_' . (time() + 3) . '_4_' . date('Ymd'),
                'donated_at' => now()->subDays(5),
                'created_at' => now()->subDays(5),
            ],
            [
                'donor_name' => 'सुनीता देवी',
                'donor_email' => '<EMAIL>',
                'donor_phone' => '+91 65432 10987',
                'donor_address' => 'D-321, जयपुर, राजस्थान',
                'amount' => 3000.00,
                'currency' => 'INR',
                'cause_id' => $causes->first()->id,
                'payment_gateway' => 'razorpay',
                'transaction_id' => 'TXN_' . time() . '_1005',
                'gateway_transaction_id' => 'pay_' . \Str::random(14),
                'gateway_response' => [
                    'razorpay_payment_id' => 'pay_' . \Str::random(14),
                    'razorpay_order_id' => 'order_' . \Str::random(14),
                    'razorpay_signature' => \Str::random(64),
                ],
                'status' => 'completed',
                'message' => 'बच्चों की शिक्षा के लिए दान',
                'is_anonymous' => false,
                'receipt_number' => 'RCP_' . (time() + 4) . '_5_' . date('Ymd'),
                'donated_at' => now()->subDays(3),
                'created_at' => now()->subDays(3),
            ],
            [
                'donor_name' => 'विकास अग्रवाल',
                'donor_email' => '<EMAIL>',
                'donor_phone' => '+91 54321 09876',
                'donor_address' => 'E-654, पुणे, महाराष्ट्र',
                'amount' => 7500.00,
                'currency' => 'INR',
                'cause_id' => $causes->count() > 1 ? $causes->skip(1)->first()->id : $causes->first()->id,
                'payment_gateway' => 'razorpay',
                'transaction_id' => 'TXN_' . time() . '_1006',
                'gateway_transaction_id' => 'pay_' . \Str::random(14),
                'gateway_response' => [
                    'razorpay_payment_id' => 'pay_' . \Str::random(14),
                    'razorpay_order_id' => 'order_' . \Str::random(14),
                    'razorpay_signature' => \Str::random(64),
                ],
                'status' => 'completed',
                'message' => 'स्वास्थ्य शिविर के लिए योगदान',
                'is_anonymous' => false,
                'receipt_number' => 'RCP_' . (time() + 5) . '_6_' . date('Ymd'),
                'donated_at' => now()->subDays(1),
                'created_at' => now()->subDays(1),
            ],
            [
                'donor_name' => 'मीरा जोशी',
                'donor_email' => '<EMAIL>',
                'donor_phone' => '+91 43210 98765',
                'donor_address' => 'F-987, चेन्नई, तमिलनाडु',
                'amount' => 500.00,
                'currency' => 'INR',
                'cause_id' => $causes->first()->id,
                'payment_gateway' => 'razorpay',
                'transaction_id' => 'TXN_' . time() . '_1007',
                'gateway_transaction_id' => null,
                'gateway_response' => null,
                'status' => 'pending',
                'message' => 'छोटा योगदान',
                'is_anonymous' => false,
                'receipt_number' => 'PENDING_' . (time() + 6) . '_7_' . date('Ymd'),
                'donated_at' => null,
                'created_at' => now()->subHours(2),
            ],
        ];

        foreach ($donations as $donation) {
            Donation::create($donation);
        }

        // Update cause raised amounts
        foreach ($causes as $cause) {
            $totalRaised = Donation::where('cause_id', $cause->id)
                ->where('status', 'completed')
                ->sum('amount');
            
            $cause->update(['raised_amount' => $totalRaised]);
        }
    }
}
