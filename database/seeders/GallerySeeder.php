<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Gallery;
use App\Models\GalleryItem;

class GallerySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $albums = [
            [
                'name' => 'शिक्षा कार्यक्रम 2024',
                'slug' => 'education-program-2024',
                'description' => 'हमारे शिक्षा कार्यक्रम की तस्वीरें जहाँ बच्चों को मुफ्त शिक्षा और किताबें प्रदान की गईं।',
                'cover_image' => 'images/gallery/education-program/cover.jpg',
                'event_date' => now()->subDays(30),
                'sort_order' => 1,
                'status' => 'active',
                'items' => [
                    [
                        'title' => 'बच्चों को किताबें वितरण',
                        'description' => 'गरीब बच्चों को मुफ्त किताबें और स्टेशनरी वितरित करते हुए',
                        'file_path' => 'images/gallery/education-program/books-distribution.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 1024000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 1,
                    ],
                    [
                        'title' => 'कक्षा में पढ़ाई',
                        'description' => 'बच्चे खुशी से पढ़ाई करते हुए',
                        'file_path' => 'images/gallery/education-program/classroom.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 956000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 2,
                    ],
                    [
                        'title' => 'स्कूल यूनिफॉर्म वितरण',
                        'description' => 'नए स्कूल यूनिफॉर्म पहने खुश बच्चे',
                        'file_path' => 'images/gallery/education-program/uniform-distribution.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 1156000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 3,
                    ],
                ],
            ],
            [
                'name' => 'स्वास्थ्य शिविर 2024',
                'slug' => 'health-camp-2024',
                'description' => 'मुफ्त स्वास्थ्य जांच और दवाइयों के वितरण का कार्यक्रम।',
                'cover_image' => 'images/gallery/health-camp/cover.jpg',
                'event_date' => now()->subDays(20),
                'sort_order' => 2,
                'status' => 'active',
                'items' => [
                    [
                        'title' => 'डॉक्टर द्वारा जांच',
                        'description' => 'मुफ्त स्वास्थ्य जांच करते डॉक्टर',
                        'file_path' => 'images/gallery/health-camp/doctor-checkup.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 1200000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 1,
                    ],
                    [
                        'title' => 'दवाइयों का वितरण',
                        'description' => 'मरीजों को मुफ्त दवाइयां देते हुए',
                        'file_path' => 'images/gallery/health-camp/medicine-distribution.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 1100000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 2,
                    ],
                    [
                        'title' => 'स्वास्थ्य जागरूकता',
                        'description' => 'स्वास्थ्य के बारे में जागरूकता फैलाते हुए',
                        'file_path' => 'images/gallery/health-camp/health-awareness.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 980000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 3,
                    ],
                ],
            ],
            [
                'name' => 'महिला सशक्तिकरण कार्यशाला',
                'slug' => 'women-empowerment-workshop',
                'description' => 'महिलाओं के लिए कौशल विकास और स्वरोजगार की कार्यशाला।',
                'cover_image' => 'images/gallery/women-empowerment/cover.jpg',
                'event_date' => now()->subDays(15),
                'sort_order' => 3,
                'status' => 'active',
                'items' => [
                    [
                        'title' => 'सिलाई कार्यशाला',
                        'description' => 'महिलाएं सिलाई सीखते हुए',
                        'file_path' => 'images/gallery/women-empowerment/sewing-workshop.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 1050000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 1,
                    ],
                    [
                        'title' => 'कंप्यूटर प्रशिक्षण',
                        'description' => 'महिलाओं को कंप्यूटर का प्रशिक्षण देते हुए',
                        'file_path' => 'images/gallery/women-empowerment/computer-training.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 1150000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 2,
                    ],
                ],
            ],
            [
                'name' => 'बुजुर्ग देखभाल कार्यक्रम',
                'slug' => 'elderly-care-program',
                'description' => 'बुजुर्गों की देखभाल और सेवा के कार्यक्रम की तस्वीरें।',
                'cover_image' => 'images/gallery/elderly-care/cover.jpg',
                'event_date' => now()->subDays(10),
                'sort_order' => 4,
                'status' => 'active',
                'items' => [
                    [
                        'title' => 'बुजुर्गों के साथ समय',
                        'description' => 'स्वयंसेवक बुजुर्गों के साथ समय बिताते हुए',
                        'file_path' => 'images/gallery/elderly-care/spending-time.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 950000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 1,
                    ],
                    [
                        'title' => 'स्वास्थ्य जांच',
                        'description' => 'बुजुर्गों की नियमित स्वास्थ्य जांच',
                        'file_path' => 'images/gallery/elderly-care/health-checkup.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 1080000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 2,
                    ],
                ],
            ],
            [
                'name' => 'पर्यावरण संरक्षण अभियान',
                'slug' => 'environment-conservation-campaign',
                'description' => 'पेड़ लगाने और पर्यावरण संरक्षण के अभियान की तस्वीरें।',
                'cover_image' => 'images/gallery/environment/cover.jpg',
                'event_date' => now()->subDays(5),
                'sort_order' => 5,
                'status' => 'active',
                'items' => [
                    [
                        'title' => 'पेड़ लगाना',
                        'description' => 'स्वयंसेवक और बच्चे मिलकर पेड़ लगाते हुए',
                        'file_path' => 'images/gallery/environment/tree-plantation.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 1250000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 1,
                    ],
                    [
                        'title' => 'सफाई अभियान',
                        'description' => 'समुदायिक सफाई अभियान में भाग लेते लोग',
                        'file_path' => 'images/gallery/environment/cleanliness-drive.jpg',
                        'file_type' => 'image',
                        'mime_type' => 'image/jpeg',
                        'file_size' => 1180000,
                        'dimensions' => ['width' => 1200, 'height' => 800],
                        'sort_order' => 2,
                    ],
                ],
            ],
        ];

        foreach ($albums as $albumData) {
            $items = $albumData['items'];
            unset($albumData['items']);

            $album = Gallery::updateOrCreate(
                ['slug' => $albumData['slug']],
                $albumData
            );

            foreach ($items as $itemData) {
                $itemData['album_id'] = $album->id;
                GalleryItem::updateOrCreate(
                    [
                        'album_id' => $album->id,
                        'title' => $itemData['title']
                    ],
                    $itemData
                );
            }
        }
    }
}
