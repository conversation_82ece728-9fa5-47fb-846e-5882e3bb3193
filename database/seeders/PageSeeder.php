<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Page;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'title' => 'हमारे बारे में',
                'slug' => 'about-us',
                'content' => '<div class="about-content">
                    <h2>हमारा मिशन</h2>
                    <p>हमारा मिशन है समाज के वंचित वर्गों के लिए शिक्षा, स्वास्थ्य और सामाजिक कल्याण के क्षेत्र में काम करना। हम मानते हैं कि हर व्यक्ति को बुनियादी सुविधाओं का अधिकार है।</p>
                    
                    <h2>हमारा विजन</h2>
                    <p>एक ऐसा समाज बनाना जहाँ हर व्यक्ति को समान अवसर मिले और कोई भी व्यक्ति बुनियादी जरूरतों से वंचित न रहे।</p>
                    
                    <h2>हमारे मूल्य</h2>
                    <ul>
                        <li><strong>पारदर्शिता:</strong> हमारे सभी कार्य पूर्ण पारदर्शिता के साथ किए जाते हैं</li>
                        <li><strong>जवाबदेही:</strong> हम अपने दानदाताओं और समुदाय के प्रति जवाबदेह हैं</li>
                        <li><strong>समानता:</strong> हम सभी के साथ समान व्यवहार में विश्वास करते हैं</li>
                        <li><strong>सेवा:</strong> निस्वार्थ सेवा हमारा मूल सिद्धांत है</li>
                    </ul>
                </div>',
                'featured_image' => 'images/pages/about-us.jpg',
                'status' => 'published',
                'is_homepage' => false,
                'sort_order' => 1,
            ],
            [
                'title' => 'हमारी सेवाएं',
                'slug' => 'our-services',
                'content' => '<div class="services-content">
                    <h2>शिक्षा कार्यक्रम</h2>
                    <p>हम गरीब बच्चों के लिए निःशुल्क शिक्षा, छात्रवृत्ति और शैक्षणिक सामग्री प्रदान करते हैं।</p>
                    
                    <h2>स्वास्थ्य सेवाएं</h2>
                    <p>मुफ्त स्वास्थ्य जांच, दवाइयां और स्वास्थ्य जागरूकता कार्यक्रम चलाते हैं।</p>
                    
                    <h2>महिला सशक्तिकरण</h2>
                    <p>महिलाओं के लिए कौशल विकास, स्वरोजगार और जागरूकता कार्यक्रम संचालित करते हैं।</p>
                    
                    <h2>बुजुर्ग देखभाल</h2>
                    <p>बुजुर्गों के लिए विशेष देखभाल और सहायता कार्यक्रम चलाते हैं।</p>
                </div>',
                'featured_image' => 'images/pages/services.jpg',
                'status' => 'published',
                'is_homepage' => false,
                'sort_order' => 2,
            ],
            [
                'title' => 'गोपनीयता नीति',
                'slug' => 'privacy-policy',
                'content' => '<div class="privacy-policy">
                    <h2>व्यक्तिगत जानकारी का संग्रह</h2>
                    <p>हम केवल आवश्यक व्यक्तिगत जानकारी एकत्र करते हैं जो हमारी सेवाओं के लिए आवश्यक है।</p>
                    
                    <h2>जानकारी का उपयोग</h2>
                    <p>आपकी जानकारी का उपयोग केवल दान प्रक्रिया, संपर्क और सेवा प्रदान करने के लिए किया जाता है।</p>
                    
                    <h2>जानकारी की सुरक्षा</h2>
                    <p>हम आपकी व्यक्तिगत जानकारी की सुरक्षा के लिए उचित तकनीकी और संगठनात्मक उपाय करते हैं।</p>
                    
                    <h2>तृतीय पक्ष के साथ साझाकरण</h2>
                    <p>हम आपकी व्यक्तिगत जानकारी को किसी तृतीय पक्ष के साथ साझा नहीं करते हैं।</p>
                </div>',
                'featured_image' => null,
                'status' => 'published',
                'is_homepage' => false,
                'sort_order' => 3,
            ],
            [
                'title' => 'नियम और शर्तें',
                'slug' => 'terms-conditions',
                'content' => '<div class="terms-conditions">
                    <h2>सामान्य नियम</h2>
                    <p>इस वेबसाइट का उपयोग करके आप इन नियमों और शर्तों से सहमत होते हैं।</p>
                    
                    <h2>दान नीति</h2>
                    <p>सभी दान स्वैच्छिक हैं और दान की गई राशि वापस नहीं की जाती है।</p>
                    
                    <h2>वेबसाइट का उपयोग</h2>
                    <p>इस वेबसाइट का उपयोग केवल वैध उद्देश्यों के लिए करें।</p>
                    
                    <h2>बौद्धिक संपदा</h2>
                    <p>इस वेबसाइट की सभी सामग्री हमारी बौद्धिक संपदा है।</p>
                </div>',
                'featured_image' => null,
                'status' => 'published',
                'is_homepage' => false,
                'sort_order' => 4,
            ],
            [
                'title' => 'संपर्क करें',
                'slug' => 'contact-info',
                'content' => '<div class="contact-info">
                    <h2>हमसे संपर्क करें</h2>
                    <div class="contact-details">
                        <h3>पता</h3>
                        <p>123, मुख्य मार्ग<br>नई दिल्ली - 110001<br>भारत</p>
                        
                        <h3>फोन</h3>
                        <p>+91 98765 43210</p>
                        
                        <h3>ईमेल</h3>
                        <p><EMAIL></p>
                        
                        <h3>कार्यालय समय</h3>
                        <p>सोमवार से शुक्रवार: 9:00 AM - 6:00 PM<br>शनिवार: 9:00 AM - 2:00 PM<br>रविवार: बंद</p>
                    </div>
                </div>',
                'featured_image' => 'images/pages/contact.jpg',
                'status' => 'published',
                'is_homepage' => false,
                'sort_order' => 5,
            ],
        ];

        foreach ($pages as $page) {
            Page::updateOrCreate(
                ['slug' => $page['slug']],
                $page
            );
        }
    }
}
