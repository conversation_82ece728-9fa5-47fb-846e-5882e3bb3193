<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class PaymentSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $setting = [
            'key' => 'payment_qr_code',
            'value' => '',
            'type' => 'image',
            'group' => 'payment',
            'description' => 'Payment QR Code image for UPI/direct payments',
        ];

        Setting::updateOrCreate(
            ['key' => $setting['key']],
            $setting
        );
    }
}
