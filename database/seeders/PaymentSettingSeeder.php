<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class PaymentSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'payment_qr_code',
                'value' => '',
                'type' => 'image',
                'group' => 'payment',
                'description' => 'Payment QR Code image for UPI/direct payments',
            ],
            [
                'key' => 'qr_payment_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'payment',
                'description' => 'Enable QR code payments',
            ],
            [
                'key' => 'payment_instructions',
                'value' => 'कृपया ऊपर दिए गए QR कोड को स्कैन करके भुगतान करें और स्क्रीनशॉट अपलोड करें।',
                'type' => 'text',
                'group' => 'payment',
                'description' => 'Payment instructions for QR code payments',
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
