<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SeoMeta;
use App\Models\Page;
use App\Models\Cause;
use App\Models\Blog;

class SeoMetaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Homepage SEO
        SeoMeta::updateOrCreate(
            ['page_type' => 'homepage', 'page_id' => null],
            [
                'meta_title' => 'NGO Portal - समाज सेवा और दान | शिक्षा, स्वास्थ्य और सामाजिक कल्याण',
                'meta_description' => 'हमारे साथ जुड़ें और समाज में बदलाव लाएं। शिक्षा, स्वास्थ्य, महिला सशक्तिकरण और बुजुर्ग देखभाल के लिए दान करें। पारदर्शी और विश्वसनीय NGO।',
                'meta_keywords' => 'NGO, दान, शिक्षा, स्वास्थ्य, महिला सशक्तिकरण, बुजुर्ग देखभाल, समाज सेवा, चैरिटी, donation, charity, education, health',
                'canonical_url' => url('/'),
                'og_title' => 'NGO Portal - समाज में बदलाव लाने में हमारे साथ जुड़ें',
                'og_description' => 'शिक्षा, स्वास्थ्य और सामाजिक कल्याण के लिए काम करने वाली विश्वसनीय संस्था। आपका छोटा सा योगदान भी किसी के जीवन में बड़ा बदलाव ला सकता है।',
                'og_image' => 'images/og-homepage.jpg',
                'og_type' => 'website',
                'twitter_card' => 'summary_large_image',
                'twitter_title' => 'NGO Portal - समाज सेवा और दान',
                'twitter_description' => 'शिक्षा, स्वास्थ्य और सामाजिक कल्याण के लिए दान करें। पारदर्शी और विश्वसनीय NGO।',
                'twitter_image' => 'images/twitter-homepage.jpg',
                'structured_data' => [
                    '@context' => 'https://schema.org',
                    '@type' => 'Organization',
                    'name' => 'NGO Portal',
                    'description' => 'शिक्षा, स्वास्थ्य और सामाजिक कल्याण के लिए काम करने वाली संस्था',
                    'url' => url('/'),
                    'logo' => asset('images/logo.png'),
                    'sameAs' => [
                        'https://facebook.com/ngo',
                        'https://twitter.com/ngo',
                        'https://instagram.com/ngo',
                    ],
                ],
            ]
        );

        // About page SEO
        SeoMeta::updateOrCreate(
            ['page_type' => 'about', 'page_id' => null],
            [
                'meta_title' => 'हमारे बारे में - NGO Portal | मिशन, विजन और मूल्य',
                'meta_description' => 'NGO Portal के बारे में जानें। हमारा मिशन, विजन और मूल्य। समाज के वंचित वर्गों के लिए शिक्षा, स्वास्थ्य और सामाजिक कल्याण का काम।',
                'meta_keywords' => 'about us, हमारे बारे में, मिशन, विजन, NGO, समाज सेवा, charity mission, organization',
                'canonical_url' => url('/about'),
                'og_title' => 'हमारे बारे में - NGO Portal',
                'og_description' => 'समाज के वंचित वर्गों के लिए काम करने वाली संस्था के बारे में जानें।',
                'og_image' => 'images/og-about.jpg',
                'og_type' => 'website',
                'twitter_card' => 'summary',
                'twitter_title' => 'हमारे बारे में - NGO Portal',
                'twitter_description' => 'हमारा मिशन, विजन और मूल्य जानें।',
                'twitter_image' => 'images/twitter-about.jpg',
            ]
        );

        // Contact page SEO
        SeoMeta::updateOrCreate(
            ['page_type' => 'contact', 'page_id' => null],
            [
                'meta_title' => 'संपर्क करें - NGO Portal | हमसे जुड़ें',
                'meta_description' => 'NGO Portal से संपर्क करें। हमारा पता, फोन नंबर और ईमेल। स्वयंसेवक बनने या दान करने के लिए हमसे संपर्क करें।',
                'meta_keywords' => 'contact, संपर्क, address, phone, email, volunteer, स्वयंसेवक, get in touch',
                'canonical_url' => url('/contact'),
                'og_title' => 'संपर्क करें - NGO Portal',
                'og_description' => 'हमसे संपर्क करें और समाज सेवा में योगदान दें।',
                'og_image' => 'images/og-contact.jpg',
                'og_type' => 'website',
                'twitter_card' => 'summary',
                'twitter_title' => 'संपर्क करें - NGO Portal',
                'twitter_description' => 'हमसे जुड़ें और समाज सेवा में योगदान दें।',
                'twitter_image' => 'images/twitter-contact.jpg',
            ]
        );

        // Causes page SEO
        SeoMeta::updateOrCreate(
            ['page_type' => 'causes', 'page_id' => null],
            [
                'meta_title' => 'हमारे कारण - NGO Portal | दान के लिए कारण',
                'meta_description' => 'शिक्षा, स्वास्थ्य, महिला सशक्तिकरण और बुजुर्ग देखभाल के लिए दान करें। हमारे सभी कारणों को देखें और योगदान दें।',
                'meta_keywords' => 'causes, कारण, donation causes, शिक्षा, स्वास्थ्य, महिला सशक्तिकरण, बुजुर्ग देखभाल',
                'canonical_url' => url('/causes'),
                'og_title' => 'हमारे कारण - NGO Portal',
                'og_description' => 'विभिन्न सामाजिक कारणों के लिए दान करें और समाज में बदलाव लाएं।',
                'og_image' => 'images/og-causes.jpg',
                'og_type' => 'website',
                'twitter_card' => 'summary_large_image',
                'twitter_title' => 'हमारे कारण - NGO Portal',
                'twitter_description' => 'सामाजिक कारणों के लिए दान करें।',
                'twitter_image' => 'images/twitter-causes.jpg',
            ]
        );

        // Blog page SEO
        SeoMeta::updateOrCreate(
            ['page_type' => 'blogs', 'page_id' => null],
            [
                'meta_title' => 'ब्लॉग - NGO Portal | समाज सेवा की कहानियां',
                'meta_description' => 'समाज सेवा, दान और सामाजिक कल्याण से जुड़ी कहानियां और लेख पढ़ें। हमारे काम की जानकारी और प्रेरणादायक कहानियां।',
                'meta_keywords' => 'blog, ब्लॉग, stories, कहानियां, social work, समाज सेवा, articles, लेख',
                'canonical_url' => url('/blogs'),
                'og_title' => 'ब्लॉग - NGO Portal',
                'og_description' => 'समाज सेवा की प्रेरणादायक कहानियां और लेख पढ़ें।',
                'og_image' => 'images/og-blog.jpg',
                'og_type' => 'website',
                'twitter_card' => 'summary',
                'twitter_title' => 'ब्लॉग - NGO Portal',
                'twitter_description' => 'समाज सेवा की कहानियां पढ़ें।',
                'twitter_image' => 'images/twitter-blog.jpg',
            ]
        );

        // Gallery page SEO
        SeoMeta::updateOrCreate(
            ['page_type' => 'gallery', 'page_id' => null],
            [
                'meta_title' => 'गैलरी - NGO Portal | हमारे काम की तस्वीरें',
                'meta_description' => 'हमारे सामाजिक कार्यों की तस्वीरें देखें। शिक्षा कार्यक्रम, स्वास्थ्य शिविर और अन्य गतिविधियों की फोटो गैलरी।',
                'meta_keywords' => 'gallery, गैलरी, photos, तस्वीरें, pictures, events, कार्यक्रम, activities',
                'canonical_url' => url('/gallery'),
                'og_title' => 'गैलरी - NGO Portal',
                'og_description' => 'हमारे सामाजिक कार्यों की तस्वीरें देखें।',
                'og_image' => 'images/og-gallery.jpg',
                'og_type' => 'website',
                'twitter_card' => 'summary_large_image',
                'twitter_title' => 'गैलरी - NGO Portal',
                'twitter_description' => 'हमारे काम की तस्वीरें देखें।',
                'twitter_image' => 'images/twitter-gallery.jpg',
            ]
        );

        // Add SEO for individual pages if they exist
        $pages = Page::all();
        foreach ($pages as $page) {
            SeoMeta::updateOrCreate(
                ['page_type' => 'page', 'page_id' => $page->id],
                [
                    'meta_title' => $page->title . ' - NGO Portal',
                    'meta_description' => $page->excerpt ?? \Str::limit(strip_tags($page->content), 160),
                    'meta_keywords' => 'NGO, ' . $page->title . ', समाज सेवा, charity',
                    'canonical_url' => url('/page/' . $page->slug),
                    'og_title' => $page->title,
                    'og_description' => $page->excerpt ?? \Str::limit(strip_tags($page->content), 160),
                    'og_image' => $page->featured_image ? asset('storage/' . $page->featured_image) : 'images/og-default.jpg',
                    'og_type' => 'article',
                    'twitter_card' => 'summary',
                    'twitter_title' => $page->title,
                    'twitter_description' => $page->excerpt ?? \Str::limit(strip_tags($page->content), 160),
                    'twitter_image' => $page->featured_image ? asset('storage/' . $page->featured_image) : 'images/twitter-default.jpg',
                ]
            );
        }
    }
}
