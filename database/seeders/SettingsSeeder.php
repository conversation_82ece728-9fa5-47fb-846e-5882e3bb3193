<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'एनजीओ पोर्टल',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Website name'
            ],
            [
                'key' => 'site_tagline',
                'value' => 'समाज सेवा में अग्रणी',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Website tagline'
            ],
            [
                'key' => 'site_description',
                'value' => 'हमारे विभिन्न पहलों और कार्यक्रमों के माध्यम से समाज पर सकारात्मक प्रभाव डालने में हमारे साथ जुड़ें।',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Website description'
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Contact email'
            ],
            [
                'key' => 'contact_phone',
                'value' => '+91 98765 43210',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Contact phone'
            ],
            [
                'key' => 'contact_address',
                'value' => '123 मुख्य मार्ग, नई दिल्ली, भारत - 110001',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Contact address'
            ],

            // Theme Settings
            [
                'key' => 'primary_color',
                'value' => '#1e3a8a',
                'type' => 'text',
                'group' => 'theme',
                'description' => 'Primary theme color'
            ],
            [
                'key' => 'secondary_color',
                'value' => '#ea580c',
                'type' => 'text',
                'group' => 'theme',
                'description' => 'Secondary theme color'
            ],
            [
                'key' => 'logo',
                'value' => 'images/logo.png',
                'type' => 'image',
                'group' => 'theme',
                'description' => 'Website logo'
            ],

            // Payment Settings
            [
                'key' => 'razorpay_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'payment',
                'description' => 'Enable Razorpay payments'
            ],
            [
                'key' => 'stripe_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'payment',
                'description' => 'Enable Stripe payments'
            ],
            [
                'key' => 'paypal_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'payment',
                'description' => 'Enable PayPal payments'
            ],

            // SEO Settings
            [
                'key' => 'meta_title',
                'value' => 'एनजीओ पोर्टल - समाज में बदलाव लाना',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default meta title'
            ],
            [
                'key' => 'meta_description',
                'value' => 'हमारे विभिन्न पहलों और कार्यक्रमों के माध्यम से समाज पर सकारात्मक प्रभाव डालने में हमारे साथ जुड़ें।',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default meta description'
            ],
            [
                'key' => 'meta_keywords',
                'value' => 'एनजीओ, चैरिटी, दान, सामाजिक कार्य, सामुदायिक सेवा',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default meta keywords'
            ],

            // Social Media
            [
                'key' => 'facebook_url',
                'value' => 'https://facebook.com/ngo',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Facebook page URL'
            ],
            [
                'key' => 'twitter_url',
                'value' => 'https://twitter.com/ngo',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Twitter profile URL'
            ],
            [
                'key' => 'instagram_url',
                'value' => 'https://instagram.com/ngo',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Instagram profile URL'
            ],
            [
                'key' => 'youtube_url',
                'value' => 'https://youtube.com/ngo',
                'type' => 'text',
                'group' => 'social',
                'description' => 'YouTube channel URL'
            ],


        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
