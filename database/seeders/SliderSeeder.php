<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Slider;

class SliderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sliders = [
            [
                'title' => 'समाज में बदलाव लाने में हमारे साथ जुड़ें',
                'description' => 'हमारे साथ मिलकर एक बेहतर कल का निर्माण करें। आपका छोटा सा योगदान भी किसी के जीवन में बड़ा बदलाव ला सकता है।',
                'image_path' => 'images/hero-bg.jpg',
                'sort_order' => 1,
                'status' => 'active',
            ],
            [
                'title' => 'शिक्षा के माध्यम से सशक्तिकरण',
                'description' => 'हर बच्चे को गुणवत्तापूर्ण शिक्षा का अधिकार है। हमारे शिक्षा कार्यक्रमों में योगदान दें और भविष्य को उज्जवल बनाएं।',
                'image_path' => 'images/hero-bg-2.jpg',
                'sort_order' => 2,
                'status' => 'active',
            ],
            [
                'title' => 'स्वास्थ्य सेवा सभी के लिए',
                'description' => 'बुनियादी स्वास्थ्य सेवा हर व्यक्ति का मौलिक अधिकार है। हमारे स्वास्थ्य कार्यक्रमों का समर्थन करें।',
                'image_path' => 'images/hero-bg-3.jpg',
                'sort_order' => 3,
                'status' => 'active',
            ],
        ];

        foreach ($sliders as $slider) {
            Slider::updateOrCreate(
                ['title' => $slider['title']],
                $slider
            );
        }
    }
}
