<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $editorRole = Role::firstOrCreate(['name' => 'editor']);
        $userRole = Role::firstOrCreate(['name' => 'user']);

        // Create permissions if they don't exist
        $permissions = [
            'manage-users',
            'manage-settings',
            'manage-causes',
            'manage-blogs',
            'manage-gallery',
            'manage-contacts',
            'manage-donations',
            'manage-sliders',
            'manage-pages',
            'view-admin-dashboard',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign all permissions to admin role
        $adminRole->syncPermissions($permissions);

        // Assign limited permissions to editor role
        $editorRole->syncPermissions([
            'manage-causes',
            'manage-blogs',
            'manage-gallery',
            'manage-contacts',
            'view-admin-dashboard',
        ]);

        // Create admin users
        $users = [
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'phone' => '+91 98765 43210',
                'address' => 'NGO Headquarters, New Delhi',
                'avatar' => null,
                'preferred_language' => 'hi',
                'status' => 'active',
                'role' => 'admin',
            ],
            [
                'name' => 'राम कुमार (Admin)',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+91 87654 32109',
                'address' => 'दिल्ली, भारत',
                'avatar' => null,
                'preferred_language' => 'hi',
                'status' => 'active',
                'role' => 'admin',
            ],
            [
                'name' => 'Content Editor',
                'email' => '<EMAIL>',
                'password' => Hash::make('editor123'),
                'phone' => '+91 76543 21098',
                'address' => 'Mumbai, Maharashtra',
                'avatar' => null,
                'preferred_language' => 'hi',
                'status' => 'active',
                'role' => 'editor',
            ],
            [
                'name' => 'प्रिया शर्मा (Editor)',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+91 65432 10987',
                'address' => 'जयपुर, राजस्थान',
                'avatar' => null,
                'preferred_language' => 'hi',
                'status' => 'active',
                'role' => 'editor',
            ],
            [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('user123'),
                'phone' => '+91 54321 09876',
                'address' => 'Bangalore, Karnataka',
                'avatar' => null,
                'preferred_language' => 'hi',
                'status' => 'active',
                'role' => 'user',
            ],
        ];

        foreach ($users as $userData) {
            $role = $userData['role'];
            unset($userData['role']);

            $user = User::updateOrCreate(
                ['email' => $userData['email']],
                $userData
            );

            // Assign role to user
            $user->assignRole($role);
        }

        $this->command->info('Users created successfully!');
        $this->command->info('Admin Login Credentials:');
        $this->command->info('Email: <EMAIL> | Password: admin123');
        $this->command->info('Email: <EMAIL> | Password: password123');
        $this->command->info('Editor Login Credentials:');
        $this->command->info('Email: <EMAIL> | Password: editor123');
        $this->command->info('Email: <EMAIL> | Password: password123');
    }
}
