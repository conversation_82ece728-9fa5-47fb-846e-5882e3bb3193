/**
 * Infinite Scroll Component for Admin Tables
 * Handles loading more data when user scrolls to bottom
 */
class InfiniteScroll {
    constructor(options) {
        this.container = options.container;
        this.tableBody = options.tableBody;
        this.loadingIndicator = options.loadingIndicator;
        this.noMoreDataIndicator = options.noMoreDataIndicator;
        this.url = options.url;
        this.renderRow = options.renderRow;
        this.currentPage = 1;
        this.loading = false;
        this.hasMore = true;
        this.totalRecords = 0;
        
        this.init();
    }
    
    init() {
        this.bindScrollEvent();
        this.createLoadingIndicator();
        this.createNoMoreDataIndicator();

        // Check if we have initial data
        if (this.tableBody.children.length === 0) {
            this.hasMore = false;
        }
    }
    
    bindScrollEvent() {
        // Add a small delay to prevent immediate triggering
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                if (this.isNearBottom() && !this.loading && this.hasMore) {
                    this.loadMore();
                }
            }, 100);
        });
    }
    
    isNearBottom() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        // Increase threshold to 300px and ensure we have enough content to scroll
        return documentHeight > windowHeight &&
               scrollTop + windowHeight >= documentHeight - 300;
    }
    
    createLoadingIndicator() {
        if (!this.loadingIndicator) {
            this.loadingIndicator = document.createElement('div');
            this.loadingIndicator.className = 'text-center py-4 d-none';
            this.loadingIndicator.id = 'loading-indicator';
            this.loadingIndicator.style.backgroundColor = '#f8f9fa';
            this.loadingIndicator.style.borderRadius = '5px';
            this.loadingIndicator.style.margin = '10px 0';
            this.loadingIndicator.innerHTML = `
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <span class="ml-2 text-muted">Loading more data...</span>
            `;
            this.container.appendChild(this.loadingIndicator);
        }
    }
    
    createNoMoreDataIndicator() {
        if (!this.noMoreDataIndicator) {
            this.noMoreDataIndicator = document.createElement('div');
            this.noMoreDataIndicator.className = 'text-center py-3 text-muted d-none';
            this.noMoreDataIndicator.id = 'no-more-data';
            this.noMoreDataIndicator.style.backgroundColor = '#e9ecef';
            this.noMoreDataIndicator.style.borderRadius = '5px';
            this.noMoreDataIndicator.style.margin = '10px 0';
            this.noMoreDataIndicator.innerHTML = `
                <i class="fas fa-check-circle text-success"></i>
                <span class="ml-2">All data loaded</span>
            `;
            this.container.appendChild(this.noMoreDataIndicator);
        }
    }
    
    showLoading() {
        this.loadingIndicator.classList.remove('d-none');
        this.noMoreDataIndicator.classList.add('d-none');
    }
    
    hideLoading() {
        this.loadingIndicator.classList.add('d-none');
    }
    
    showNoMoreData() {
        this.noMoreDataIndicator.classList.remove('d-none');
        this.loadingIndicator.classList.add('d-none');
    }
    
    async loadMore() {
        if (this.loading || !this.hasMore) return;
        
        this.loading = true;
        this.showLoading();
        
        try {
            // Build URL with existing parameters
            const url = new URL(this.url, window.location.origin);
            url.searchParams.set('page', this.currentPage + 1);

            const response = await fetch(url.toString(), {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            
            const data = await response.json();
            
            if (data.data && data.data.length > 0) {
                this.appendRows(data.data);
                this.currentPage++;
                this.hasMore = data.has_more;
                this.totalRecords = data.total;
                
                if (!this.hasMore) {
                    this.showNoMoreData();
                }
            } else {
                this.hasMore = false;
                this.showNoMoreData();
            }
            
        } catch (error) {
            console.error('Error loading more data:', error);
            this.showError();
        } finally {
            this.loading = false;
            this.hideLoading();
        }
    }
    
    appendRows(items) {
        items.forEach((item, index) => {
            const row = this.renderRow(item, this.getRowNumber(index));
            this.tableBody.appendChild(row);
        });
    }
    
    getRowNumber(index) {
        return ((this.currentPage) * 10) + index + 1;
    }
    
    showError() {
        // Remove any existing error messages first
        const existingErrors = this.container.querySelectorAll('.alert-danger');
        existingErrors.forEach(error => error.remove());

        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger text-center';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span class="ml-2">Error loading data. Please try again.</span>
            <button class="btn btn-sm btn-outline-danger ml-2" onclick="location.reload()">Refresh</button>
        `;
        this.container.appendChild(errorDiv);

        // Disable further loading attempts on error
        this.hasMore = false;

        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }
    
    updateRecordCount() {
        const countElement = document.getElementById('record-count');
        if (countElement) {
            const currentCount = this.tableBody.children.length;
            countElement.innerHTML = `Showing ${currentCount} of ${this.totalRecords} records`;
        }
    }
}

// Export for use in other files
window.InfiniteScroll = InfiniteScroll;
