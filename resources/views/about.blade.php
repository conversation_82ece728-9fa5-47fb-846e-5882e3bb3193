<x-app-layout>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-16"
             @if($heroSection && $heroSection->image_url)
             style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('{{ $heroSection->image_url }}'); background-size: cover; background-position: center;"
             @endif>
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4 font-hindi">
                    @if($heroSection)
                        {{ $heroSection->title }}
                    @else
                        {{ app()->getLocale() === 'hi' ? 'हमारे बारे में' : 'About Us' }}
                    @endif
                </h1>
                <p class="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
                    @if($heroSection && $heroSection->subtitle)
                        {{ $heroSection->subtitle }}
                    @else
                        {{ app()->getLocale() === 'hi'
                            ? 'समाज सेवा और मानवता के लिए समर्पित संगठन'
                            : 'An organization dedicated to social service and humanity' }}
                    @endif
                </p>
                @if($heroSection && $heroSection->content)
                    <div class="mt-4 text-lg text-gray-300 max-w-4xl mx-auto">
                        {!! nl2br(e($heroSection->content)) !!}
                    </div>
                @endif
                @if($heroSection && $heroSection->button_text && $heroSection->button_url)
                    <div class="mt-6">
                        <a href="{{ $heroSection->button_url }}" class="bg-gov-orange hover:bg-orange-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
                            {{ $heroSection->button_text }}
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Mission & Vision Section -->
    @if($missionSection || $visionSection)
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-2 gap-12">
                <!-- Mission -->
                @if($missionSection)
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-orange rounded-full flex items-center justify-center mx-auto mb-6">
                        @if($missionSection->icon)
                            <i class="{{ $missionSection->icon }} text-white text-3xl"></i>
                        @else
                            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        @endif
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        {{ $missionSection->title }}
                    </h2>
                    @if($missionSection->subtitle)
                        <h3 class="text-xl text-gov-orange mb-4">{{ $missionSection->subtitle }}</h3>
                    @endif
                    <p class="text-gray-600 leading-relaxed">
                        {!! nl2br(e($missionSection->content)) !!}
                    </p>
                    @if($missionSection->button_text && $missionSection->button_url)
                        <div class="mt-4">
                            <a href="{{ $missionSection->button_url }}" class="bg-gov-orange hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                {{ $missionSection->button_text }}
                            </a>
                        </div>
                    @endif
                </div>
                @else
                <!-- Fallback Mission -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-orange rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'हमारा मिशन' : 'Our Mission' }}
                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        {{ app()->getLocale() === 'hi'
                            ? 'समाज के वंचित वर्गों के उत्थान के लिए शिक्षा, स्वास्थ्य, और आर्थिक सहायता प्रदान करना।'
                            : 'To provide education, healthcare, and economic assistance for the upliftment of underprivileged sections of society.' }}
                    </p>
                </div>
                @endif

                <!-- Vision -->
                @if($visionSection)
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-green rounded-full flex items-center justify-center mx-auto mb-6">
                        @if($visionSection->icon)
                            <i class="{{ $visionSection->icon }} text-white text-3xl"></i>
                        @else
                            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                            </svg>
                        @endif
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        {{ $visionSection->title }}
                    </h2>
                    @if($visionSection->subtitle)
                        <h3 class="text-xl text-gov-orange mb-4">{{ $visionSection->subtitle }}</h3>
                    @endif
                    <p class="text-gray-600 leading-relaxed">
                        {!! nl2br(e($visionSection->content)) !!}
                    </p>
                    @if($visionSection->button_text && $visionSection->button_url)
                        <div class="mt-4">
                            <a href="{{ $visionSection->button_url }}" class="bg-gov-green hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                {{ $visionSection->button_text }}
                            </a>
                        </div>
                    @endif
                </div>
                @else
                <!-- Fallback Vision -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-green rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'हमारा विजन' : 'Our Vision' }}
                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        {{ app()->getLocale() === 'hi'
                            ? 'एक ऐसे भारत का निर्माण जहाँ गरीबी, अशिक्षा और बीमारी का अंत हो।'
                            : 'Building an India where poverty, illiteracy and disease are eradicated.' }}
                    </p>
                </div>
                @endif
            </div>
        </div>
    </section>
    @endif

    <!-- Values Section -->
    @if($valuesSections->count() > 0)
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gov-blue mb-4 font-hindi">
                    {{ app()->getLocale() === 'hi' ? 'हमारे मूल्य' : 'Our Values' }}
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    {{ app()->getLocale() === 'hi'
                        ? 'ये मूल्य हमारे काम की नींव हैं और हमारे हर निर्णय का आधार हैं।'
                        : 'These values form the foundation of our work and guide every decision we make.' }}
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                @foreach($valuesSections as $value)
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    @if($value->image_url)
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full overflow-hidden">
                            <img src="{{ $value->image_url }}" alt="{{ $value->title }}" class="w-full h-full object-cover">
                        </div>
                    @elseif($value->icon)
                        <div class="w-16 h-16 bg-gov-blue rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="{{ $value->icon }} text-white text-2xl"></i>
                        </div>
                    @else
                        <div class="w-16 h-16 bg-gov-blue rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    @endif
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        {{ $value->title }}
                    </h3>
                    @if($value->subtitle)
                        <h4 class="text-lg text-gov-orange mb-2">{{ $value->subtitle }}</h4>
                    @endif
                    <p class="text-gray-600">
                        {!! nl2br(e($value->content)) !!}
                    </p>
                    @if($value->button_text && $value->button_url)
                        <div class="mt-4">
                            <a href="{{ $value->button_url }}" class="text-gov-blue hover:text-gov-orange font-semibold">
                                {{ $value->button_text }}
                            </a>
                        </div>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Team Section -->
    @if($teamSections->count() > 0)
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gov-blue mb-4 font-hindi">
                    {{ app()->getLocale() === 'hi' ? 'हमारी टीम' : 'Our Team' }}
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    {{ app()->getLocale() === 'hi'
                        ? 'समर्पित व्यक्तियों का समूह जो समाज सेवा के लिए प्रतिबद्ध है।'
                        : 'A group of dedicated individuals committed to social service.' }}
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                @foreach($teamSections as $member)
                <div class="text-center">
                    @if($member->image_url)
                        <div class="w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden">
                            <img src="{{ $member->image_url }}" alt="{{ $member->title }}" class="w-full h-full object-cover">
                        </div>
                    @else
                        <div class="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <svg class="w-16 h-16 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    @endif
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        {{ $member->title }}
                    </h3>
                    @if($member->extra_data && isset($member->extra_data['position']))
                        <p class="text-gov-orange font-semibold mb-2">
                            {{ $member->extra_data['position'] }}
                        </p>
                    @elseif($member->subtitle)
                        <p class="text-gov-orange font-semibold mb-2">
                            {{ $member->subtitle }}
                        </p>
                    @endif
                    @if($member->content)
                        <p class="text-gray-600 text-sm mb-2">
                            {!! nl2br(e($member->content)) !!}
                        </p>
                    @endif
                    @if($member->extra_data && isset($member->extra_data['experience']))
                        <p class="text-gray-600 text-sm mb-2">
                            {{ $member->extra_data['experience'] }}
                        </p>
                    @endif
                    @if($member->extra_data && (isset($member->extra_data['email']) || isset($member->extra_data['phone'])))
                        <div class="mt-3 space-y-1">
                            @if(isset($member->extra_data['email']))
                                <p class="text-sm">
                                    <a href="mailto:{{ $member->extra_data['email'] }}" class="text-gov-blue hover:text-gov-orange">
                                        <i class="fas fa-envelope mr-1"></i>{{ $member->extra_data['email'] }}
                                    </a>
                                </p>
                            @endif
                            @if(isset($member->extra_data['phone']))
                                <p class="text-sm">
                                    <a href="tel:{{ $member->extra_data['phone'] }}" class="text-gov-blue hover:text-gov-orange">
                                        <i class="fas fa-phone mr-1"></i>{{ $member->extra_data['phone'] }}
                                    </a>
                                </p>
                            @endif
                        </div>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Additional Sections (History, Achievements, Custom) -->
    @if($historySections->count() > 0 || $achievementsSections->count() > 0 || $customSections->count() > 0)
        @foreach([$historySections, $achievementsSections, $customSections] as $sections)
            @if($sections->count() > 0)
                <section class="py-16 {{ $loop->even ? 'bg-gray-50' : 'bg-white' }}">
                    <div class="container mx-auto px-4">
                        @foreach($sections as $section)
                            <div class="mb-12 {{ !$loop->last ? 'border-b border-gray-200 pb-12' : '' }}">
                                <div class="text-center mb-8">
                                    @if($section->icon)
                                        <div class="w-16 h-16 bg-gov-blue rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i class="{{ $section->icon }} text-white text-2xl"></i>
                                        </div>
                                    @endif
                                    <h2 class="text-3xl md:text-4xl font-bold text-gov-blue mb-4 font-hindi">
                                        {{ $section->title }}
                                    </h2>
                                    @if($section->subtitle)
                                        <h3 class="text-xl text-gov-orange mb-4">{{ $section->subtitle }}</h3>
                                    @endif
                                </div>

                                @if($section->image_url)
                                    <div class="text-center mb-8">
                                        <img src="{{ $section->image_url }}" alt="{{ $section->title }}" class="mx-auto rounded-lg shadow-lg max-w-full h-auto">
                                    </div>
                                @endif

                                @if($section->content)
                                    <div class="prose prose-lg mx-auto text-gray-600">
                                        {!! nl2br(e($section->content)) !!}
                                    </div>
                                @endif

                                @if($section->button_text && $section->button_url)
                                    <div class="text-center mt-8">
                                        <a href="{{ $section->button_url }}" class="bg-gov-blue hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                                            {{ $section->button_text }}
                                        </a>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </section>
            @endif
        @endforeach
    @endif


</x-app-layout>
