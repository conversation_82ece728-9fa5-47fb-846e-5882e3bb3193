@extends('admin.layouts.app')

@section('title', 'About Page Management')

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <!-- <h1 class="h3 mb-0 text-gray-800">About Page Management</h1> -->
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item active">About Sections</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        <!-- Main Content Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">About Page Sections</h6>
                    <div>
                        <a href="{{ route('admin.about.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New Section
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if($sections->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="50">#</th>
                                    <th>Image</th>
                                    <th>Section Type</th>
                                    <th>Title</th>
                                    <th>Content</th>
                                    <th>Order</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="sections-table-body">
                                @foreach($sections as $index => $section)
                                    <tr>
                                        <td>{{ $sections->firstItem() + $index }}</td>
                                        <td>
                                            @if($section->image_path)
                                                <img src="{{ asset('storage/' . $section->image_path) }}"
                                                     alt="{{ $section->title }}"
                                                     class="img-thumbnail"
                                                     style="width: 80px; height: 50px; object-fit: cover;">
                                            @elseif($section->icon)
                                                <div class="text-center p-2">
                                                    <i class="{{ $section->icon }} fa-2x text-primary"></i>
                                                </div>
                                            @else
                                                <span class="text-muted">No Image</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ ucfirst($section->section_type) }}</span>
                                        </td>
                                        <td>{{ $section->title }}</td>
                                        <td>{{ Str::limit(strip_tags($section->content), 50) }}</td>
                                        <td>{{ $section->sort_order }}</td>
                                        <td>
                                            <span class="badge badge-{{ $section->status == 'active' ? 'success' : 'secondary' }}">
                                                {{ ucfirst($section->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.about.show', $section) }}"
                                                   class="btn btn-info btn-sm" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.about.edit', $section) }}"
                                                   class="btn btn-warning btn-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('admin.about.destroy', $section) }}"
                                                      method="POST"
                                                      style="display: inline-block;"
                                                      onsubmit="return confirm('Are you sure you want to delete this section?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Record Count -->
                    <div class="mt-3">
                        <p class="text-muted" id="record-count">
                            Showing {{ $sections->count() }} of {{ $sections->total() }} records
                        </p>
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No about sections found</h5>
                        <p class="text-muted">Start by creating your first about page section.</p>
                        <a href="{{ route('admin.about.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create First Section
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="{{ asset('js/infinite-scroll.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($sections->count() > 0)
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card-body'),
        tableBody: document.getElementById('sections-table-body'),
        url: '{{ route("admin.about.index") }}',
        renderRow: function(section, rowNumber) {
            const row = document.createElement('tr');

            // Image column
            let imageHtml = '<span class="text-muted">No Image</span>';
            if (section.image_path) {
                imageHtml = `<img src="{{ asset('storage/') }}/${section.image_path}"
                            alt="${section.title}"
                            class="img-thumbnail"
                            style="width: 80px; height: 50px; object-fit: cover;">`;
            } else if (section.icon) {
                imageHtml = `<div class="text-center p-2">
                            <i class="${section.icon} fa-2x text-primary"></i>
                            </div>`;
            }

            // Status badge
            const statusClass = section.status === 'active' ? 'success' : 'secondary';

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>${imageHtml}</td>
                <td><span class="badge badge-info">${section.section_type.charAt(0).toUpperCase() + section.section_type.slice(1)}</span></td>
                <td>${section.title}</td>
                <td>${section.content ? section.content.replace(/<[^>]*>/g, '').substring(0, 50) + (section.content.length > 50 ? '...' : '') : ''}</td>
                <td>${section.sort_order}</td>
                <td><span class="badge badge-${statusClass}">${section.status.charAt(0).toUpperCase() + section.status.slice(1)}</span></td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="{{ route('admin.about.show', '') }}/${section.id}"
                           class="btn btn-info btn-sm" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ route('admin.about.edit', '') }}/${section.id}"
                           class="btn btn-warning btn-sm" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="{{ route('admin.about.destroy', '') }}/${section.id}"
                              method="POST"
                              style="display: inline-block;"
                              onsubmit="return confirm('Are you sure you want to delete this section?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    @endif
});
</script>
@endpush
