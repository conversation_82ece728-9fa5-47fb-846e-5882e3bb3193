@extends('admin.layouts.app')

@section('title', 'View About Section')

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0 text-gray-800">View About Section</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('admin.about.index') }}">About Sections</a></li>
                            <li class="breadcrumb-item active">View</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{{ $about->title }}</h6>
                    <div>
                        <a href="{{ route('admin.about.edit', $about) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.about.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <th width="150">Section Type:</th>
                                <td><span class="badge badge-info">{{ ucfirst($about->section_type) }}</span></td>
                            </tr>
                            <tr>
                                <th>Title:</th>
                                <td>{{ $about->title }}</td>
                            </tr>
                            @if($about->subtitle)
                            <tr>
                                <th>Subtitle:</th>
                                <td>{{ $about->subtitle }}</td>
                            </tr>
                            @endif
                            <tr>
                                <th>Status:</th>
                                <td>
                                    <span class="badge badge-{{ $about->status == 'active' ? 'success' : 'secondary' }}">
                                        {{ ucfirst($about->status) }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>Sort Order:</th>
                                <td>{{ $about->sort_order }}</td>
                            </tr>
                            @if($about->icon)
                            <tr>
                                <th>Icon:</th>
                                <td>
                                    <i class="{{ $about->icon }} fa-2x text-primary"></i>
                                    <small class="text-muted ml-2">{{ $about->icon }}</small>
                                </td>
                            </tr>
                            @endif
                            @if($about->button_text && $about->button_url)
                            <tr>
                                <th>Button:</th>
                                <td>
                                    <a href="{{ $about->button_url }}" class="btn btn-primary btn-sm" target="_blank">
                                        {{ $about->button_text }}
                                    </a>
                                </td>
                            </tr>
                            @endif
                            <tr>
                                <th>Created:</th>
                                <td>{{ $about->created_at->format('M d, Y h:i A') }}</td>
                            </tr>
                            <tr>
                                <th>Updated:</th>
                                <td>{{ $about->updated_at->format('M d, Y h:i A') }}</td>
                            </tr>
                        </table>
                    </div>

                    <!-- Image -->
                    <div class="col-md-4">
                        @if($about->image_path)
                            <div class="text-center">
                                <h6 class="text-primary mb-3">Featured Image</h6>
                                <img src="{{ asset('storage/' . $about->image_path) }}" 
                                     alt="{{ $about->title }}" 
                                     class="img-fluid rounded shadow">
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Content -->
                @if($about->content)
                <div class="mt-4">
                    <h6 class="text-primary mb-3">Content</h6>
                    <div class="border rounded p-3 bg-light">
                        {!! nl2br(e($about->content)) !!}
                    </div>
                </div>
                @endif

                <!-- Team Member Details -->
                @if($about->section_type === 'team' && $about->extra_data)
                <div class="mt-4">
                    <h6 class="text-primary mb-3">Team Member Details</h6>
                    <div class="row">
                        @if(isset($about->extra_data['position']))
                        <div class="col-md-6 mb-3">
                            <strong>Position:</strong><br>
                            {{ $about->extra_data['position'] }}
                        </div>
                        @endif
                        @if(isset($about->extra_data['experience']))
                        <div class="col-md-6 mb-3">
                            <strong>Experience:</strong><br>
                            {{ $about->extra_data['experience'] }}
                        </div>
                        @endif
                        @if(isset($about->extra_data['email']))
                        <div class="col-md-6 mb-3">
                            <strong>Email:</strong><br>
                            <a href="mailto:{{ $about->extra_data['email'] }}">{{ $about->extra_data['email'] }}</a>
                        </div>
                        @endif
                        @if(isset($about->extra_data['phone']))
                        <div class="col-md-6 mb-3">
                            <strong>Phone:</strong><br>
                            <a href="tel:{{ $about->extra_data['phone'] }}">{{ $about->extra_data['phone'] }}</a>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="mt-4 pt-3 border-top">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.about.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <div>
                            <a href="{{ route('admin.about.edit', $about) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit Section
                            </a>
                            <form action="{{ route('admin.about.destroy', $about) }}" 
                                  method="POST" 
                                  style="display: inline-block;"
                                  onsubmit="return confirm('Are you sure you want to delete this section?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
