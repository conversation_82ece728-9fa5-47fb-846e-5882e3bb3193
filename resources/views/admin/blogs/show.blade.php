@extends('admin.layouts.app')

@section('title', 'View Blog Post')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ $blog->title }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.blogs.edit', $blog) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.blogs.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Main Content -->
                        <div class="col-md-8">
                            <!-- Featured Image -->
                            @if($blog->featured_image)
                                <div class="mb-4">
                                    <img src="{{ asset('storage/' . $blog->featured_image) }}" 
                                         alt="{{ $blog->title }}" 
                                         class="img-fluid rounded">
                                </div>
                            @endif

                            <!-- Excerpt -->
                            <div class="mb-4">
                                <h5>Excerpt</h5>
                                <p class="text-muted">{{ $blog->excerpt }}</p>
                            </div>

                            <!-- Content -->
                            <div class="mb-4">
                                <h5>Content</h5>
                                <div class="content-display">
                                    {!! nl2br(e($blog->content)) !!}
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Blog Details</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Status -->
                                    <div class="mb-3">
                                        <strong>Status:</strong><br>
                                        <span class="badge badge-{{ $blog->status == 'published' ? 'success' : 'warning' }} badge-lg">
                                            {{ ucfirst($blog->status) }}
                                        </span>
                                    </div>

                                    <!-- Category -->
                                    <div class="mb-3">
                                        <strong>Category:</strong><br>
                                        <span class="badge badge-info">{{ $blog->category->name ?? 'Uncategorized' }}</span>
                                    </div>

                                    <!-- Slug -->
                                    <div class="mb-3">
                                        <strong>Slug:</strong><br>
                                        <code>{{ $blog->slug }}</code>
                                    </div>

                                    <!-- Published Date -->
                                    <div class="mb-3">
                                        <strong>Published:</strong><br>
                                        {{ $blog->published_at ? $blog->published_at->format('M d, Y \a\t g:i A') : 'Not published' }}
                                    </div>

                                    <!-- Created Date -->
                                    <div class="mb-3">
                                        <strong>Created:</strong><br>
                                        {{ $blog->created_at->format('M d, Y \a\t g:i A') }}
                                    </div>

                                    <!-- Updated Date -->
                                    <div class="mb-3">
                                        <strong>Last Updated:</strong><br>
                                        {{ $blog->updated_at->format('M d, Y \a\t g:i A') }}
                                    </div>

                                    <!-- Views -->
                                    @if(isset($blog->views))
                                    <div class="mb-3">
                                        <strong>Views:</strong><br>
                                        {{ number_format($blog->views) }}
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('admin.blogs.edit', $blog) }}" class="btn btn-warning btn-block">
                                            <i class="fas fa-edit"></i> Edit Blog Post
                                        </a>
                                        
                                        @if($blog->status == 'published')
                                            <a href="{{ route('blog.show', $blog->slug) }}" 
                                               class="btn btn-info btn-block" target="_blank">
                                                <i class="fas fa-external-link-alt"></i> View on Site
                                            </a>
                                        @endif

                                        <form action="{{ route('admin.blogs.destroy', $blog) }}" 
                                              method="POST" 
                                              onsubmit="return confirm('Are you sure you want to delete this blog post?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-block">
                                                <i class="fas fa-trash"></i> Delete Blog Post
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.content-display {
    line-height: 1.6;
    font-size: 16px;
}

.badge-lg {
    font-size: 0.9em;
    padding: 0.5em 0.75em;
}

.d-grid .btn {
    margin-bottom: 0.5rem;
}
</style>
@endsection
