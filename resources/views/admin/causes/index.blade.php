@extends('admin.layouts.app')

@section('title', 'Causes')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Fundraising Causes</h3>
                    <div class="card-tools">
                        <!-- Per Page Selector -->
                        <div class="d-inline-block mr-2">
                            <select class="form-control form-control-sm" onchange="changePerPage(this.value)">
                                <option value="10" {{ request('per_page', 10) == 10 ? 'selected' : '' }}>10 per page</option>
                                <option value="20" {{ request('per_page', 10) == 20 ? 'selected' : '' }}>20 per page</option>
                                <option value="50" {{ request('per_page', 10) == 50 ? 'selected' : '' }}>50 per page</option>
                                <option value="100" {{ request('per_page', 10) == 100 ? 'selected' : '' }}>100 per page</option>
                            </select>
                        </div>
                        <a href="{{ route('admin.causes.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    @if($causes->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">#</th>
                                        <th>Image</th>
                                        <th>Title</th>
                                        <th>Goal Amount</th>
                                        <th>Raised</th>
                                        <th>Status</th>
                                        <th>Featured</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="causes-table-body">
                                    @foreach($causes as $index => $cause)
                                        <tr>
                                            <td>{{ $causes->firstItem() + $index }}</td>
                                            <td>
                                                @if($cause->featured_image)
                                                    <img src="{{ asset('storage/' . $cause->featured_image) }}"
                                                         alt="{{ $cause->title }}"
                                                         class="img-thumbnail"
                                                         style="width: 80px; height: 50px; object-fit: cover;">
                                                @else
                                                    <span class="text-muted">No Image</span>
                                                @endif
                                            </td>
                                            <td>
                                                <strong>{{ $cause->title }}</strong>
                                                <br>
                                                <small class="text-muted">{{ Str::limit($cause->short_description, 50) }}</small>
                                            </td>
                                            <td>₹{{ number_format($cause->goal_amount, 0) }}</td>
                                            <td>
                                                ₹{{ number_format($cause->raised_amount, 0) }}
                                                <br>
                                                <small class="text-muted">{{ number_format(($cause->raised_amount / $cause->goal_amount) * 100, 1) }}%</small>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $cause->status === 'active' ? 'success' : ($cause->status === 'completed' ? 'primary' : 'warning') }}">
                                                    {{ ucfirst($cause->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($cause->is_featured)
                                                    <span class="badge badge-info">Featured</span>
                                                @else
                                                    <span class="text-muted">No</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.causes.show', $cause->id) }}"
                                                       class="btn btn-info btn-sm" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.causes.edit', $cause->id) }}"
                                                       class="btn btn-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.causes.destroy', $cause->id) }}"
                                                          method="POST"
                                                          style="display: inline-block;"
                                                          onsubmit="return confirm('Are you sure you want to delete this cause?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Record Count -->
                        <div class="mt-3">
                            <p class="text-muted" id="record-count">
                                Showing {{ $causes->count() }} of {{ $causes->total() }} records
                            </p>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No causes found</h5>
                            <p class="text-muted">Create your first fundraising cause to get started.</p>
                            <a href="{{ route('admin.causes.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Cause
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/infinite-scroll.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($causes->count() > 0)
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card-body'),
        tableBody: document.getElementById('causes-table-body'),
        url: '{{ route("admin.causes.index") }}',
        renderRow: function(cause, rowNumber) {
            const row = document.createElement('tr');

            // Image column
            let imageHtml = '<span class="text-muted">No Image</span>';
            if (cause.featured_image) {
                imageHtml = `<img src="{{ asset('storage/') }}/${cause.featured_image}"
                            alt="${cause.title}"
                            class="img-thumbnail"
                            style="width: 80px; height: 50px; object-fit: cover;">`;
            }

            // Status badge
            let statusClass = 'warning';
            if (cause.status === 'active') statusClass = 'success';
            else if (cause.status === 'completed') statusClass = 'primary';

            // Featured badge
            const featuredHtml = cause.is_featured ?
                '<span class="badge badge-info">Featured</span>' :
                '<span class="text-muted">No</span>';

            // Progress calculation
            const progress = cause.goal_amount > 0 ? ((cause.raised_amount / cause.goal_amount) * 100).toFixed(1) : 0;

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>${imageHtml}</td>
                <td>
                    <strong>${cause.title}</strong>
                    <br>
                    <small class="text-muted">${cause.short_description ? cause.short_description.substring(0, 50) + (cause.short_description.length > 50 ? '...' : '') : ''}</small>
                </td>
                <td>₹${new Intl.NumberFormat('en-IN').format(cause.goal_amount)}</td>
                <td>
                    ₹${new Intl.NumberFormat('en-IN').format(cause.raised_amount)}
                    <br>
                    <small class="text-muted">${progress}%</small>
                </td>
                <td><span class="badge badge-${statusClass}">${cause.status.charAt(0).toUpperCase() + cause.status.slice(1)}</span></td>
                <td>${featuredHtml}</td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="{{ route('admin.causes.show', '') }}/${cause.id}"
                           class="btn btn-info btn-sm" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ route('admin.causes.edit', '') }}/${cause.id}"
                           class="btn btn-warning btn-sm" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="{{ route('admin.causes.destroy', '') }}/${cause.id}"
                              method="POST"
                              style="display: inline-block;"
                              onsubmit="return confirm('Are you sure you want to delete this cause?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    @endif
});
</script>
@endpush
