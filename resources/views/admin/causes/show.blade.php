@extends('admin.layouts.app')

@section('title', 'View Cause')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Cause Details</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.causes.edit', $cause->id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.causes.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-8">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="200">Title</th>
                                    <td>{{ $cause->title }}</td>
                                </tr>
                                <tr>
                                    <th>Slug</th>
                                    <td>{{ $cause->slug }}</td>
                                </tr>
                                <tr>
                                    <th>Short Description</th>
                                    <td>{{ $cause->short_description }}</td>
                                </tr>
                                <tr>
                                    <th>Description</th>
                                    <td>{!! nl2br(e($cause->description)) !!}</td>
                                </tr>
                                <tr>
                                    <th>Goal Amount</th>
                                    <td>{{ $cause->formatted_goal_amount }}</td>
                                </tr>
                                <tr>
                                    <th>Raised Amount</th>
                                    <td>{{ $cause->formatted_raised_amount }}</td>
                                </tr>
                                <tr>
                                    <th>Progress</th>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: {{ min(100, $cause->progress_percentage) }}%" 
                                                 aria-valuenow="{{ $cause->progress_percentage }}" 
                                                 aria-valuemin="0" aria-valuemax="100">
                                                {{ number_format($cause->progress_percentage, 1) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        @if($cause->status === 'active')
                                            <span class="badge badge-success">Active</span>
                                        @elseif($cause->status === 'inactive')
                                            <span class="badge badge-secondary">Inactive</span>
                                        @elseif($cause->status === 'completed')
                                            <span class="badge badge-primary">Completed</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Featured</th>
                                    <td>
                                        @if($cause->is_featured)
                                            <span class="badge badge-warning">Yes</span>
                                        @else
                                            <span class="badge badge-light">No</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $cause->created_at->format('M d, Y h:i A') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ $cause->updated_at->format('M d, Y h:i A') }}</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Featured Image -->
                        <div class="col-md-4">
                            @if($cause->featured_image)
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">Featured Image</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="{{ $cause->featured_image_url }}" 
                                             alt="{{ $cause->title }}" 
                                             class="img-fluid rounded">
                                    </div>
                                </div>
                            @else
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">Featured Image</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="text-muted">
                                            <i class="fas fa-image fa-3x mb-3"></i>
                                            <p>No image uploaded</p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <a href="{{ route('admin.causes.edit', $cause->id) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Cause
                    </a>
                    <a href="{{ route('admin.causes.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                    <a href="{{ route('causes.show', $cause->slug) }}"
                       class="btn btn-info" target="_blank">
                        <i class="fas fa-external-link-alt"></i> View on Website
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
