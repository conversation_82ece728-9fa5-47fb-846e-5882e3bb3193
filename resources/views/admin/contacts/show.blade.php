@extends('admin.layouts.app')

@section('title', 'View Contact Message')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Contact Message Details</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Main Content -->
                        <div class="col-md-8">
                            <!-- Contact Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-user mr-2"></i>
                                        Contact Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Name:</strong><br>
                                            <span class="text-muted">{{ $contact->name }}</span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Email:</strong><br>
                                            <a href="mailto:{{ $contact->email }}" class="text-primary">
                                                {{ $contact->email }}
                                            </a>
                                        </div>
                                    </div>
                                    
                                    @if($contact->phone)
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <strong>Phone:</strong><br>
                                                <a href="tel:{{ $contact->phone }}" class="text-primary">
                                                    {{ $contact->phone }}
                                                </a>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Message Content -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-envelope mr-2"></i>
                                        Message
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>Subject:</strong><br>
                                        <span class="text-muted">{{ $contact->subject }}</span>
                                    </div>
                                    
                                    <div>
                                        <strong>Message:</strong><br>
                                        <div class="border rounded p-3 bg-light mt-2">
                                            {!! nl2br(e($contact->message)) !!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar -->
                        <div class="col-md-4">
                            <!-- Status & Details -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Message Details</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Status -->
                                    <div class="mb-3">
                                        <strong>Status:</strong><br>
                                        <span class="badge badge-{{ $contact->is_read ? 'success' : 'warning' }} badge-lg">
                                            {{ $contact->is_read ? 'Read' : 'Unread' }}
                                        </span>
                                    </div>

                                    <!-- Submitted Date -->
                                    <div class="mb-3">
                                        <strong>Submitted:</strong><br>
                                        <span class="text-muted">
                                            {{ $contact->created_at->format('M d, Y \a\t g:i A') }}
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            ({{ $contact->created_at->diffForHumans() }})
                                        </small>
                                    </div>

                                    <!-- Last Updated -->
                                    @if($contact->updated_at != $contact->created_at)
                                        <div class="mb-3">
                                            <strong>Last Updated:</strong><br>
                                            <span class="text-muted">
                                                {{ $contact->updated_at->format('M d, Y \a\t g:i A') }}
                                            </span>
                                        </div>
                                    @endif

                                    <!-- Contact Method -->
                                    <div class="mb-3">
                                        <strong>Preferred Contact:</strong><br>
                                        <div class="mt-2">
                                            <a href="mailto:{{ $contact->email }}" class="btn btn-outline-primary btn-sm btn-block">
                                                <i class="fas fa-envelope mr-1"></i> Send Email
                                            </a>
                                            @if($contact->phone)
                                                <a href="tel:{{ $contact->phone }}" class="btn btn-outline-success btn-sm btn-block mt-1">
                                                    <i class="fas fa-phone mr-1"></i> Call Phone
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <!-- Reply via Email -->
                                        <a href="mailto:{{ $contact->email }}?subject=Re: {{ $contact->subject }}&body=Dear {{ $contact->name }},%0D%0A%0D%0AThank you for contacting us.%0D%0A%0D%0ABest regards,%0D%0A[Your Name]" 
                                           class="btn btn-primary btn-block">
                                            <i class="fas fa-reply mr-1"></i> Reply via Email
                                        </a>

                                        <!-- Mark as Unread (if read) -->
                                        @if($contact->is_read)
                                            <form action="{{ route('admin.contacts.show', $contact) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="mark_unread" value="1">
                                                <button type="submit" class="btn btn-warning btn-block">
                                                    <i class="fas fa-eye-slash mr-1"></i> Mark as Unread
                                                </button>
                                            </form>
                                        @endif

                                        <!-- Delete Message -->
                                        <form action="{{ route('admin.contacts.destroy', $contact) }}" 
                                              method="POST" 
                                              onsubmit="return confirm('Are you sure you want to delete this contact message? This action cannot be undone.')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-block">
                                                <i class="fas fa-trash mr-1"></i> Delete Message
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 0.9em;
    padding: 0.5em 0.75em;
}

.d-grid .btn {
    margin-bottom: 0.5rem;
}

.card-body .border {
    max-height: 300px;
    overflow-y: auto;
}
</style>
@endsection
