@extends('admin.layouts.app')

@section('title', 'Dashboard')

@section('breadcrumb')
    <li class="breadcrumb-item active">Dashboard</li>
@endsection

@push('styles')
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        color: white;
        border-radius: 12px;
        padding: 1.25rem 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .dashboard-header h1 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .dashboard-header p {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
    }

    .stats-grid {
        gap: 1rem;
    }

    /* Compact stat cards */
    .small-box {
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .small-box:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .small-box .inner {
        padding: 1rem 1.25rem;
    }

    .small-box .inner h3 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .small-box .inner p {
        font-size: 0.8rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .small-box .icon {
        position: absolute;
        top: 50%;
        right: 1rem;
        transform: translateY(-50%);
        font-size: 1.5rem;
        opacity: 0.3;
    }

    .small-box .icon i {
        font-size: 1.5rem;
    }

    .welcome-card {
        background: linear-gradient(135deg, #ffffff, #f8fafc);
        border: 1px solid #e2e8f0;
        border-radius: 20px;
        padding: 3rem 2rem;
        text-align: center;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .welcome-card h3 {
        color: #1e293b;
        font-size: 1.875rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .welcome-card p {
        color: #64748b;
        font-size: 1.125rem;
        margin-bottom: 2rem;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .quick-action-btn {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-action-btn:hover {
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
        transform: translateY(-2px);
        box-shadow: 0 8px 15px -3px rgba(59, 130, 246, 0.3);
        color: white;
        text-decoration: none;
    }


</style>
@endpush

@section('content')
    <div class="container-fluid">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1>Welcome back, {{ Auth::user()->name }}!</h1>
            <p>Here's what's happening with your NGO website today.</p>
        </div>

        <!-- Stats Cards Row -->
        <div class="row stats-grid mb-4">
            <!-- Registered Members -->
            <div class="col-lg-3 col-md-6 col-12 mb-3">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3>{{ \App\Models\User::count() }}</h3>
                        <p>Registered Members</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>

            <!-- Active Campaigns -->
            <div class="col-lg-3 col-md-6 col-12 mb-3">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3>{{ \App\Models\Cause::where('status', 'active')->count() }}</h3>
                        <p>Active Campaigns</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-hand-holding-heart"></i>
                    </div>
                </div>
            </div>

            <!-- Funds Raised -->
            <div class="col-lg-3 col-md-6 col-12 mb-3">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3>₹{{ number_format(\App\Models\Cause::sum('raised_amount'), 0) }}</h3>
                        <p>Funds Raised</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>

            <!-- Published Articles -->
            <div class="col-lg-3 col-md-6 col-12 mb-3">
                <div class="small-box bg-secondary">
                    <div class="inner">
                        <h3>{{ \App\Models\Blog::count() }}</h3>
                        <p>Published Articles</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Row -->
        <div class="row">
            <!-- Welcome Section -->
            <div class="col-12 mb-4">
                <div class="welcome-card">
                    <h3>Admin Control Center</h3>
                    <p>Manage your NGO website content, campaigns, and settings from this central dashboard.</p>

                    <div class="quick-actions">
                        <a href="{{ route('admin.settings.index') }}" class="quick-action-btn">
                            <i class="fas fa-cogs"></i>
                            Website Settings
                        </a>
                        <a href="{{ route('admin.causes.index') }}" class="quick-action-btn">
                            <i class="fas fa-heart"></i>
                            Manage Causes
                        </a>
                        <a href="{{ route('admin.blogs.index') }}" class="quick-action-btn">
                            <i class="fas fa-newspaper"></i>
                            Blog Posts
                        </a>
                        <a href="{{ route('admin.about.index') }}" class="quick-action-btn">
                            <i class="fas fa-info-circle"></i>
                            About Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
