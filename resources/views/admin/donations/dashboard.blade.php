@extends('admin.layouts.app')

@section('title', 'Donation Dashboard')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Donation Dashboard</h1>
        <a href="{{ route('admin.donations.index') }}" class="btn btn-primary">
            <i class="fas fa-list mr-2"></i>View All Donations
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Donations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_donations'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-heart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Amount Raised
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ number_format($stats['total_amount'], 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Completed Donations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['completed_donations'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Donations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_donations'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Statistics -->
    <div class="row mb-4">
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 text-center">
                            <div class="mb-2">
                                <i class="fas fa-qrcode fa-3x text-warning"></i>
                            </div>
                            <h4 class="font-weight-bold">{{ $stats['qr_donations'] }}</h4>
                            <p class="text-muted">QR Code Payments</p>
                        </div>
                        <div class="col-6 text-center">
                            <div class="mb-2">
                                <i class="fas fa-credit-card fa-3x text-primary"></i>
                            </div>
                            <h4 class="font-weight-bold">{{ $stats['gateway_donations'] }}</h4>
                            <p class="text-muted">Gateway Payments</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Donations ({{ date('Y') }})</h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Donations -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Recent Donations</h6>
                <a href="{{ route('admin.donations.index') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-list mr-1"></i>View All
                </a>
            </div>
        </div>
        <div class="card-body">
            @if($recent_donations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>Donor</th>
                                <th>Amount</th>
                                <th>Cause</th>
                                <th>Method</th>
                                <th>Transaction Photo</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="donations-table-body">
                            @foreach($recent_donations as $index => $donation)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>
                                        <div>
                                            <strong>{{ $donation->is_anonymous ? 'Anonymous' : $donation->donor_name }}</strong>
                                            @if(!$donation->is_anonymous && $donation->donor_email)
                                                <br><small class="text-muted">{{ $donation->donor_email }}</small>
                                            @endif
                                            @if($donation->donor_phone)
                                                <br><small class="text-muted"><i class="fas fa-phone"></i> {{ $donation->donor_phone }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <strong class="text-success">₹{{ number_format($donation->amount, 2) }}</strong>
                                    </td>
                                    <td>
                                        @if($donation->cause)
                                            <span class="badge badge-info">{{ Str::limit($donation->cause->title, 20) }}</span>
                                        @else
                                            <span class="text-muted">General</span>
                                        @endif
                                    </td>
                                    <td>
                                        @switch($donation->payment_method)
                                            @case('qr_code')
                                                <span class="badge badge-warning"><i class="fas fa-qrcode"></i> QR Code</span>
                                                @break
                                            @case('gateway')
                                                <span class="badge badge-primary"><i class="fas fa-credit-card"></i> Gateway</span>
                                                @break
                                            @case('bank_transfer')
                                                <span class="badge badge-info"><i class="fas fa-university"></i> Bank Transfer</span>
                                                @break
                                            @case('cash')
                                                <span class="badge badge-success"><i class="fas fa-money-bill"></i> Cash</span>
                                                @break
                                            @default
                                                <span class="badge badge-secondary">{{ ucfirst($donation->payment_method) }}</span>
                                        @endswitch
                                    </td>
                                    <td class="text-center">
                                        @if($donation->payment_screenshot)
                                            <img src="{{ $donation->payment_screenshot_url }}"
                                                 alt="Transaction Screenshot"
                                                 class="img-thumbnail"
                                                 style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
                                                 onclick="openImageModal('{{ $donation->payment_screenshot_url }}', '{{ $donation->donor_name }}')">
                                        @else
                                            <span class="text-muted"><i class="fas fa-image-slash"></i> No Image</span>
                                        @endif
                                    </td>
                                    <td>
                                        @switch($donation->status)
                                            @case('pending')
                                                <span class="badge badge-warning"><i class="fas fa-clock"></i> Pending</span>
                                                @break
                                            @case('completed')
                                                <span class="badge badge-success"><i class="fas fa-check"></i> Completed</span>
                                                @break
                                            @case('failed')
                                                <span class="badge badge-danger"><i class="fas fa-times"></i> Failed</span>
                                                @break
                                            @case('refunded')
                                                <span class="badge badge-secondary"><i class="fas fa-undo"></i> Refunded</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        <small>{{ $donation->created_at->format('M d, Y') }}<br>{{ $donation->created_at->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.donations.show', $donation) }}" class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No donations yet.</p>
                </div>
            @endif

            @if($recent_donations->count() > 0)
                <!-- Record Count -->
                <div class="mt-3">
                    <p class="text-muted" id="record-count">
                        Showing {{ $recent_donations->count() }} of {{ $recent_donations->total() }} records
                    </p>
                </div>
            @endif
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly donations chart
const ctx = document.getElementById('monthlyChart').getContext('2d');
const monthlyData = @json($monthly_stats);

const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const data = new Array(12).fill(0);
const amounts = new Array(12).fill(0);

monthlyData.forEach(item => {
    data[item.month - 1] = item.count;
    amounts[item.month - 1] = item.total;
});

new Chart(ctx, {
    type: 'bar',
    data: {
        labels: months,
        datasets: [{
            label: 'Number of Donations',
            data: data,
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    afterLabel: function(context) {
                        const monthIndex = context.dataIndex;
                        return 'Amount: ₹' + amounts[monthIndex].toLocaleString();
                    }
                }
            }
        }
    }
});

// Image modal function
function openImageModal(src, donorName) {
    document.getElementById('modalImage').src = src;
    document.getElementById('modalTitle').textContent = 'Transaction Screenshot - ' + donorName;
    $('#imageModal').modal('show');
}
</script>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Transaction Screenshot</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Transaction Screenshot" class="img-fluid">
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/infinite-scroll.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($recent_donations->count() > 0)
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card'),
        tableBody: document.getElementById('donations-table-body'),
        url: '{{ route("admin.donations.dashboard") }}',
        renderRow: function(donation, rowNumber) {
            const row = document.createElement('tr');

            // Format donor info
            let donorInfo = donation.is_anonymous ? 'Anonymous' : donation.donor_name;
            if (!donation.is_anonymous && donation.donor_email) {
                donorInfo += `<br><small class="text-muted">${donation.donor_email}</small>`;
            }
            if (donation.donor_phone) {
                donorInfo += `<br><small class="text-muted"><i class="fas fa-phone"></i> ${donation.donor_phone}</small>`;
            }

            // Format cause
            let causeInfo = donation.cause ?
                `<span class="badge badge-info">${donation.cause.title.substring(0, 20)}${donation.cause.title.length > 20 ? '...' : ''}</span>` :
                '<span class="text-muted">General</span>';

            // Format payment method
            let paymentMethod = '';
            switch(donation.payment_method) {
                case 'qr_code':
                    paymentMethod = '<span class="badge badge-warning"><i class="fas fa-qrcode"></i> QR Code</span>';
                    break;
                case 'gateway':
                    paymentMethod = '<span class="badge badge-primary"><i class="fas fa-credit-card"></i> Gateway</span>';
                    break;
                case 'bank_transfer':
                    paymentMethod = '<span class="badge badge-info"><i class="fas fa-university"></i> Bank Transfer</span>';
                    break;
                case 'cash':
                    paymentMethod = '<span class="badge badge-success"><i class="fas fa-money-bill"></i> Cash</span>';
                    break;
                default:
                    paymentMethod = `<span class="badge badge-secondary">${donation.payment_method.charAt(0).toUpperCase() + donation.payment_method.slice(1)}</span>`;
            }

            // Format transaction photo
            let transactionPhoto = '<span class="text-muted"><i class="fas fa-image-slash"></i> No Image</span>';
            if (donation.payment_screenshot) {
                const imageUrl = `{{ asset('storage/') }}/${donation.payment_screenshot}`;
                transactionPhoto = `<img src="${imageUrl}"
                                   alt="Transaction Screenshot"
                                   class="img-thumbnail"
                                   style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
                                   onclick="openImageModal('${imageUrl}', '${donation.donor_name}')">`;
            }

            // Format status
            let statusBadge = '';
            switch(donation.status) {
                case 'pending':
                    statusBadge = '<span class="badge badge-warning"><i class="fas fa-clock"></i> Pending</span>';
                    break;
                case 'completed':
                    statusBadge = '<span class="badge badge-success"><i class="fas fa-check"></i> Completed</span>';
                    break;
                case 'failed':
                    statusBadge = '<span class="badge badge-danger"><i class="fas fa-times"></i> Failed</span>';
                    break;
                case 'refunded':
                    statusBadge = '<span class="badge badge-secondary"><i class="fas fa-undo"></i> Refunded</span>';
                    break;
            }

            // Format date
            const createdAt = new Date(donation.created_at);
            const dateStr = createdAt.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
            const timeStr = createdAt.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>
                    <div>${donorInfo}</div>
                </td>
                <td>
                    <strong class="text-success">₹${parseFloat(donation.amount).toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</strong>
                </td>
                <td>${causeInfo}</td>
                <td>${paymentMethod}</td>
                <td class="text-center">${transactionPhoto}</td>
                <td>${statusBadge}</td>
                <td>
                    <small>${dateStr}<br>${timeStr}</small>
                </td>
                <td>
                    <a href="{{ route('admin.donations.show', '') }}/${donation.id}" class="btn btn-sm btn-outline-primary" title="View Details">
                        <i class="fas fa-eye"></i>
                    </a>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    @endif
});
</script>
@endpush
