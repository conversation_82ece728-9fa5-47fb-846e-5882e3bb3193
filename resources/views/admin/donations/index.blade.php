@extends('admin.layouts.app')

@section('title', 'Donation Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Donation Management</h1>
        <a href="{{ route('admin.donations.dashboard') }}" class="btn btn-primary">
            <i class="fas fa-chart-bar mr-2"></i>Dashboard
        </a>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.donations.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-control">
                            <option value="">All Status</option>
                            <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                            <option value="refunded" {{ request('status') === 'refunded' ? 'selected' : '' }}>Refunded</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select name="payment_method" id="payment_method" class="form-control">
                            <option value="">All Methods</option>
                            <option value="qr_code" {{ request('payment_method') === 'qr_code' ? 'selected' : '' }}>QR Code</option>
                            <option value="gateway" {{ request('payment_method') === 'gateway' ? 'selected' : '' }}>Gateway</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="cause_id" class="form-label">Cause</label>
                        <select name="cause_id" id="cause_id" class="form-control">
                            <option value="">All Causes</option>
                            @foreach($causes as $cause)
                                <option value="{{ $cause->id }}" {{ request('cause_id') == $cause->id ? 'selected' : '' }}>
                                    {{ $cause->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <div class="input-group">
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Name, Email, Transaction ID" value="{{ request('search') }}">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <a href="{{ route('admin.donations.index') }}" class="btn btn-secondary">Clear Filters</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Donations Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title mb-0">Donations ({{ $donations->total() }})</h3>
        </div>
        <div class="card-body p-0">
            @if($donations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped mb-0">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>Donor</th>
                                <th>Amount</th>
                                <th>Cause</th>
                                <th>Payment Method</th>
                                <th>Transaction Photo</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="donations-table-body">
                            @foreach($donations as $index => $donation)
                                <tr>
                                    <td>{{ ($donations->currentPage() - 1) * $donations->perPage() + $index + 1 }}</td>
                                    <td>
                                        <div>
                                            <strong>{{ $donation->is_anonymous ? 'Anonymous' : $donation->donor_name }}</strong>
                                            @if(!$donation->is_anonymous && $donation->donor_email)
                                                <br><small class="text-muted">{{ $donation->donor_email }}</small>
                                            @endif
                                            @if($donation->donor_phone)
                                                <br><small class="text-muted"><i class="fas fa-phone"></i> {{ $donation->donor_phone }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <strong class="text-success">₹{{ number_format($donation->amount, 2) }}</strong>
                                    </td>
                                    <td>
                                        @if($donation->cause)
                                            <span class="badge badge-info">{{ Str::limit($donation->cause->title, 20) }}</span>
                                        @else
                                            <span class="text-muted">General</span>
                                        @endif
                                    </td>
                                    <td>
                                        @switch($donation->payment_method)
                                            @case('qr_code')
                                                <span class="badge badge-warning"><i class="fas fa-qrcode"></i> QR Code</span>
                                                @break
                                            @case('gateway')
                                                <span class="badge badge-primary"><i class="fas fa-credit-card"></i> Gateway</span>
                                                @break
                                            @case('bank_transfer')
                                                <span class="badge badge-info"><i class="fas fa-university"></i> Bank Transfer</span>
                                                @break
                                            @case('cash')
                                                <span class="badge badge-success"><i class="fas fa-money-bill"></i> Cash</span>
                                                @break
                                            @default
                                                <span class="badge badge-secondary">{{ ucfirst($donation->payment_method) }}</span>
                                        @endswitch
                                    </td>
                                    <td class="text-center">
                                        @if($donation->payment_screenshot)
                                            <img src="{{ $donation->payment_screenshot_url }}"
                                                 alt="Transaction Screenshot"
                                                 class="img-thumbnail"
                                                 style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
                                                 onclick="openImageModal('{{ $donation->payment_screenshot_url }}', '{{ $donation->donor_name }}')">
                                        @else
                                            <span class="text-muted"><i class="fas fa-image-slash"></i> No Image</span>
                                        @endif
                                    </td>
                                    <td>
                                        @switch($donation->status)
                                            @case('pending')
                                                <span class="badge badge-warning"><i class="fas fa-clock"></i> Pending</span>
                                                @break
                                            @case('completed')
                                                <span class="badge badge-success"><i class="fas fa-check"></i> Completed</span>
                                                @break
                                            @case('failed')
                                                <span class="badge badge-danger"><i class="fas fa-times"></i> Failed</span>
                                                @break
                                            @case('refunded')
                                                <span class="badge badge-secondary"><i class="fas fa-undo"></i> Refunded</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        <small>{{ $donation->created_at->format('M d, Y') }}<br>{{ $donation->created_at->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.donations.show', $donation) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($donation->status === 'pending')
                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                        onclick="updateStatus({{ $donation->id }}, 'completed')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="updateStatus({{ $donation->id }}, 'failed')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <p class="text-muted">No donations found.</p>
                </div>
            @endif

            @if($donations->count() > 0)
                <!-- Record Count -->
                <div class="card-footer">
                    <p class="text-muted mb-0" id="record-count">
                        Showing {{ $donations->count() }} of {{ $donations->total() }} records
                    </p>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function updateStatus(donationId, status) {
    if (confirm('Are you sure you want to update this donation status?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/donations/${donationId}/status`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'PUT';
        
        const statusField = document.createElement('input');
        statusField.type = 'hidden';
        statusField.name = 'status';
        statusField.value = status;
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        form.appendChild(statusField);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function openImageModal(src, donorName) {
    document.getElementById('modalImage').src = src;
    document.getElementById('modalTitle').textContent = 'Transaction Screenshot - ' + donorName;
    $('#imageModal').modal('show');
}
</script>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Transaction Screenshot</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Transaction Screenshot" class="img-fluid">
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/infinite-scroll.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($donations->count() > 0)
    // Get current URL with filters
    const currentUrl = new URL(window.location.href);
    const baseUrl = '{{ route("admin.donations.index") }}';
    const queryParams = currentUrl.search;

    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card'),
        tableBody: document.getElementById('donations-table-body'),
        url: baseUrl + queryParams,
        renderRow: function(donation, rowNumber) {
            const row = document.createElement('tr');

            // Format donor info
            let donorInfo = donation.is_anonymous ? 'Anonymous' : donation.donor_name;
            if (!donation.is_anonymous && donation.donor_email) {
                donorInfo += `<br><small class="text-muted">${donation.donor_email}</small>`;
            }
            if (donation.donor_phone) {
                donorInfo += `<br><small class="text-muted"><i class="fas fa-phone"></i> ${donation.donor_phone}</small>`;
            }

            // Format cause
            let causeInfo = donation.cause ?
                `<span class="badge badge-info">${donation.cause.title.substring(0, 20)}${donation.cause.title.length > 20 ? '...' : ''}</span>` :
                '<span class="text-muted">General</span>';

            // Format payment method
            let paymentMethod = '';
            switch(donation.payment_method) {
                case 'qr_code':
                    paymentMethod = '<span class="badge badge-warning"><i class="fas fa-qrcode"></i> QR Code</span>';
                    break;
                case 'gateway':
                    paymentMethod = '<span class="badge badge-primary"><i class="fas fa-credit-card"></i> Gateway</span>';
                    break;
                case 'bank_transfer':
                    paymentMethod = '<span class="badge badge-info"><i class="fas fa-university"></i> Bank Transfer</span>';
                    break;
                case 'cash':
                    paymentMethod = '<span class="badge badge-success"><i class="fas fa-money-bill"></i> Cash</span>';
                    break;
                default:
                    paymentMethod = `<span class="badge badge-secondary">${donation.payment_method.charAt(0).toUpperCase() + donation.payment_method.slice(1)}</span>`;
            }

            // Format transaction photo
            let transactionPhoto = '<span class="text-muted"><i class="fas fa-image-slash"></i> No Image</span>';
            if (donation.payment_screenshot) {
                const imageUrl = `{{ asset('storage/') }}/${donation.payment_screenshot}`;
                transactionPhoto = `<img src="${imageUrl}"
                                   alt="Transaction Screenshot"
                                   class="img-thumbnail"
                                   style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
                                   onclick="openImageModal('${imageUrl}', '${donation.donor_name}')">`;
            }

            // Format status
            let statusBadge = '';
            switch(donation.status) {
                case 'pending':
                    statusBadge = '<span class="badge badge-warning"><i class="fas fa-clock"></i> Pending</span>';
                    break;
                case 'completed':
                    statusBadge = '<span class="badge badge-success"><i class="fas fa-check"></i> Completed</span>';
                    break;
                case 'failed':
                    statusBadge = '<span class="badge badge-danger"><i class="fas fa-times"></i> Failed</span>';
                    break;
                case 'refunded':
                    statusBadge = '<span class="badge badge-secondary"><i class="fas fa-undo"></i> Refunded</span>';
                    break;
            }

            // Format date
            const createdAt = new Date(donation.created_at);
            const dateStr = createdAt.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
            const timeStr = createdAt.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            // Action buttons
            let actionButtons = `<a href="{{ route('admin.donations.show', '') }}/${donation.id}"
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>`;

            if (donation.status === 'pending') {
                actionButtons += `
                    <button type="button" class="btn btn-sm btn-outline-success"
                            onclick="updateStatus(${donation.id}, 'completed')">
                        <i class="fas fa-check"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="updateStatus(${donation.id}, 'failed')">
                        <i class="fas fa-times"></i>
                    </button>`;
            }

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>
                    <div>${donorInfo}</div>
                </td>
                <td>
                    <strong class="text-success">₹${parseFloat(donation.amount).toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</strong>
                </td>
                <td>${causeInfo}</td>
                <td>${paymentMethod}</td>
                <td class="text-center">${transactionPhoto}</td>
                <td>${statusBadge}</td>
                <td>
                    <small>${dateStr}<br>${timeStr}</small>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        ${actionButtons}
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    @endif
});
</script>
@endpush
