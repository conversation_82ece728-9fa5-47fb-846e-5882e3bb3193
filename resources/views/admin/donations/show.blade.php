@extends('admin.layouts.app')

@section('title', 'Donation Details')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Donation Details #{{ $donation->id }}</h1>
        <a href="{{ route('admin.donations.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>Back to List
        </a>
    </div>

    <div class="row">
        <!-- Donation Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Donation Information</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Transaction ID:</strong></td>
                                    <td>{{ $donation->transaction_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Receipt Number:</strong></td>
                                    <td>{{ $donation->receipt_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Amount:</strong></td>
                                    <td><strong class="text-success">₹{{ number_format($donation->amount, 2) }}</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Currency:</strong></td>
                                    <td>{{ $donation->currency }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Method:</strong></td>
                                    <td>
                                        @if($donation->payment_method === 'qr_code')
                                            <span class="badge badge-warning">QR Code Payment</span>
                                        @else
                                            <span class="badge badge-primary">{{ ucfirst($donation->payment_gateway) }} Gateway</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @switch($donation->status)
                                            @case('pending')
                                                <span class="badge badge-warning">Pending</span>
                                                @break
                                            @case('completed')
                                                <span class="badge badge-success">Completed</span>
                                                @break
                                            @case('failed')
                                                <span class="badge badge-danger">Failed</span>
                                                @break
                                            @case('refunded')
                                                <span class="badge badge-secondary">Refunded</span>
                                                @break
                                        @endswitch
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Cause:</strong></td>
                                    <td>
                                        @if($donation->cause)
                                            <a href="{{ route('admin.causes.show', $donation->cause->id) }}" class="text-decoration-none">
                                                {{ $donation->cause->title }}
                                            </a>
                                        @else
                                            <span class="text-muted">General Donation</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Anonymous:</strong></td>
                                    <td>{{ $donation->is_anonymous ? 'Yes' : 'No' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $donation->created_at->format('M d, Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Donated At:</strong></td>
                                    <td>{{ $donation->donated_at ? $donation->donated_at->format('M d, Y H:i:s') : 'N/A' }}</td>
                                </tr>
                                @if($donation->gateway_transaction_id)
                                <tr>
                                    <td><strong>Gateway Transaction ID:</strong></td>
                                    <td>{{ $donation->gateway_transaction_id }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Donor Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Donor Information</h3>
                </div>
                <div class="card-body">
                    @if(!$donation->is_anonymous)
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td>{{ $donation->donor_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ $donation->donor_email }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>{{ $donation->donor_phone ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td>{{ $donation->donor_address ?: 'N/A' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    @else
                        <p class="text-muted">This is an anonymous donation. Donor information is not displayed.</p>
                    @endif
                </div>
            </div>

            <!-- Message -->
            @if($donation->message)
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Donor Message</h3>
                </div>
                <div class="card-body">
                    <p>{{ $donation->message }}</p>
                </div>
            </div>
            @endif
        </div>

        <!-- Actions & Payment Screenshot -->
        <div class="col-md-4">
            <!-- Status Update -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Update Status</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.donations.updateStatus', $donation) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control" required>
                                <option value="pending" {{ $donation->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="completed" {{ $donation->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="failed" {{ $donation->status === 'failed' ? 'selected' : '' }}>Failed</option>
                                <option value="refunded" {{ $donation->status === 'refunded' ? 'selected' : '' }}>Refunded</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin_notes">Admin Notes</label>
                            <textarea name="admin_notes" id="admin_notes" class="form-control" rows="3" 
                                      placeholder="Add notes about this donation...">{{ $donation->admin_notes }}</textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">Update Status</button>
                    </form>
                </div>
            </div>

            <!-- Payment Screenshot -->
            @if($donation->payment_screenshot)
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Payment Screenshot</h3>
                </div>
                <div class="card-body text-center">
                    <img src="{{ $donation->payment_screenshot_url }}" 
                         alt="Payment Screenshot" 
                         class="img-fluid rounded shadow"
                         style="max-height: 300px; cursor: pointer;"
                         onclick="openImageModal(this.src)">
                    <div class="mt-2">
                        <a href="{{ $donation->payment_screenshot_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt mr-1"></i>View Full Size
                        </a>
                    </div>
                </div>
            </div>
            @endif

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">Actions</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.donations.destroy', $donation) }}" method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this donation? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger btn-block">
                            <i class="fas fa-trash mr-2"></i>Delete Donation
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Screenshot</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Payment Screenshot" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
function openImageModal(src) {
    document.getElementById('modalImage').src = src;
    $('#imageModal').modal('show');
}
</script>
@endsection
