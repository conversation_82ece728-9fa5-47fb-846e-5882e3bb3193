@extends('admin.layouts.app')

@section('title', 'Edit Gallery')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Edit Gallery</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.gallery.show', $gallery) }}" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ route('admin.gallery.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <form action="{{ route('admin.gallery.update', $gallery) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <!-- Name -->
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="name">Gallery Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name', $gallery->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status">Status <span class="text-danger">*</span></label>
                                    <select class="form-control @error('status') is-invalid @enderror"
                                            id="status" name="status" required>
                                        <option value="active" {{ old('status', $gallery->status) == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ old('status', $gallery->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Slug (readonly) -->
                        <div class="form-group">
                            <label for="slug">Slug</label>
                            <input type="text" class="form-control" id="slug" value="{{ $gallery->slug }}" readonly>
                            <small class="form-text text-muted">Auto-generated from gallery name</small>
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3">{{ old('description', $gallery->description) }}</textarea>
                            <small class="form-text text-muted">Optional description for the gallery</small>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Cover Image -->
                        <div class="form-group">
                            <label for="cover_image">Cover Image</label>
                            
                            <!-- Current Cover Image -->
                            @if($gallery->cover_image)
                                <div class="mb-2">
                                    <img src="{{ asset('storage/' . $gallery->cover_image) }}" 
                                         alt="{{ $gallery->name }}" 
                                         class="img-thumbnail" 
                                         style="max-width: 200px;">
                                    <p class="text-muted small mt-1">Current cover image</p>
                                </div>
                            @endif
                            
                            <div class="custom-file">
                                <input type="file" class="custom-file-input @error('cover_image') is-invalid @enderror"
                                       id="cover_image" name="cover_image" accept="image/*">
                                <label class="custom-file-label" for="cover_image">Choose new cover image (optional)</label>
                            </div>
                            <small class="form-text text-muted">Upload a new cover image to replace current one (JPEG, PNG, JPG, GIF, max 2MB)</small>
                            @error('cover_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            
                            <!-- New Cover Image Preview -->
                            <div id="coverPreview" style="display: none;">
                                <img id="coverImg" src="" alt="Cover Preview" class="img-thumbnail mt-2" style="max-width: 200px;">
                                <p class="text-muted small mt-1">New cover image preview</p>
                            </div>
                        </div>

                        <!-- Current Gallery Images -->
                        @if($gallery->images)
                            @php
                                $images = json_decode($gallery->images, true);
                            @endphp
                            @if($images && count($images) > 0)
                                <div class="form-group">
                                    <label>Current Gallery Images</label>
                                    <div class="row">
                                        @foreach($images as $image)
                                            <div class="col-md-3 col-sm-4 mb-2">
                                                <img src="{{ asset('storage/' . $image) }}" 
                                                     alt="Gallery Image" 
                                                     class="img-thumbnail"
                                                     style="width: 100%; height: 150px; object-fit: cover;">
                                            </div>
                                        @endforeach
                                    </div>
                                    <small class="form-text text-muted">{{ count($images) }} image(s) currently in gallery</small>
                                </div>
                            @endif
                        @endif

                        <!-- Add New Gallery Images -->
                        <div class="form-group">
                            <label for="images">Add New Gallery Images</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input @error('images.*') is-invalid @enderror"
                                       id="images" name="images[]" accept="image/*" multiple>
                                <label class="custom-file-label" for="images">Choose new gallery images (optional)</label>
                            </div>
                            <small class="form-text text-muted">Upload additional images for the gallery (JPEG, PNG, JPG, GIF, max 2MB each). These will be added to existing images.</small>
                            @error('images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            
                            <!-- New Gallery Images Preview -->
                            <div id="galleryPreview" class="mt-2" style="display: none;">
                                <div class="row" id="galleryImages"></div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Gallery
                        </button>
                        <a href="{{ route('admin.gallery.show', $gallery) }}" class="btn btn-info">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ route('admin.gallery.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Cover image preview
document.getElementById('cover_image').addEventListener('change', function(e) {
    var fileName = e.target.files[0] ? e.target.files[0].name : 'Choose new cover image (optional)';
    var nextSibling = e.target.nextElementSibling;
    nextSibling.innerText = fileName;
    
    // Show cover image preview
    if (e.target.files && e.target.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('coverImg').src = e.target.result;
            document.getElementById('coverPreview').style.display = 'block';
        }
        reader.readAsDataURL(e.target.files[0]);
    } else {
        document.getElementById('coverPreview').style.display = 'none';
    }
});

// Gallery images preview
document.getElementById('images').addEventListener('change', function(e) {
    var fileCount = e.target.files.length;
    var fileName = fileCount > 0 ? fileCount + ' new file(s) selected' : 'Choose new gallery images (optional)';
    var nextSibling = e.target.nextElementSibling;
    nextSibling.innerText = fileName;
    
    // Show gallery images preview
    var galleryImagesDiv = document.getElementById('galleryImages');
    galleryImagesDiv.innerHTML = '';
    
    if (e.target.files && e.target.files.length > 0) {
        document.getElementById('galleryPreview').style.display = 'block';
        
        for (let i = 0; i < e.target.files.length; i++) {
            let file = e.target.files[i];
            let reader = new FileReader();
            
            reader.onload = function(e) {
                let col = document.createElement('div');
                col.className = 'col-md-3 mb-2';
                col.innerHTML = '<img src="' + e.target.result + '" alt="New Gallery Image" class="img-thumbnail" style="width: 100%; height: 150px; object-fit: cover;"><p class="text-muted small mt-1">New image</p>';
                galleryImagesDiv.appendChild(col);
            }
            
            reader.readAsDataURL(file);
        }
    } else {
        document.getElementById('galleryPreview').style.display = 'none';
    }
});
</script>
@endsection
