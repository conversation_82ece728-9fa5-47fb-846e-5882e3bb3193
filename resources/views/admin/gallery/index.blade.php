@extends('admin.layouts.app')

@section('title', 'Gallery')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Gallery Albums</h3>
                    <div class="card-tools">
                        <!-- Per Page Selector -->
                        <div class="d-inline-block mr-2">
                            <select class="form-control form-control-sm" onchange="changePerPage(this.value)">
                                <option value="10" {{ request('per_page', 10) == 10 ? 'selected' : '' }}>10 per page</option>
                                <option value="20" {{ request('per_page', 10) == 20 ? 'selected' : '' }}>20 per page</option>
                                <option value="50" {{ request('per_page', 10) == 50 ? 'selected' : '' }}>50 per page</option>
                                <option value="100" {{ request('per_page', 10) == 100 ? 'selected' : '' }}>100 per page</option>
                            </select>
                        </div>
                        <a href="{{ route('admin.gallery.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($galleries->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">#</th>
                                        <th>Cover Image</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="galleries-table-body">
                                    @foreach($galleries as $index => $gallery)
                                        <tr>
                                            <td>{{ $galleries->firstItem() + $index }}</td>
                                            <td>
                                                @if($gallery->cover_image)
                                                    <img src="{{ asset('storage/' . $gallery->cover_image) }}" 
                                                         alt="{{ $gallery->name }}" 
                                                         class="img-thumbnail" 
                                                         style="width: 80px; height: 50px; object-fit: cover;">
                                                @else
                                                    <span class="text-muted">No Image</span>
                                                @endif
                                            </td>
                                            <td>{{ $gallery->name }}</td>
                                            <td>{{ Str::limit($gallery->description, 50) }}</td>
                                            <td>
                                                <span class="badge badge-{{ $gallery->status == 'active' ? 'success' : 'secondary' }}">
                                                    {{ ucfirst($gallery->status) }}
                                                </span>
                                            </td>
                                            <td>{{ $gallery->created_at->format('M d, Y') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.gallery.show', $gallery) }}" 
                                                       class="btn btn-info btn-sm" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.gallery.edit', $gallery) }}" 
                                                       class="btn btn-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.gallery.destroy', $gallery) }}" 
                                                          method="POST" 
                                                          style="display: inline-block;"
                                                          onsubmit="return confirm('Are you sure you want to delete this gallery?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Record Count -->
                        <div class="mt-3">
                            <p class="text-muted" id="record-count">
                                Showing {{ $galleries->count() }} of {{ $galleries->total() }} records
                            </p>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No gallery albums found</h5>
                            <p class="text-muted">Create your first gallery album to get started.</p>
                            <a href="{{ route('admin.gallery.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Gallery
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/infinite-scroll.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($galleries->count() > 0)
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card-body'),
        tableBody: document.getElementById('galleries-table-body'),
        url: '{{ route("admin.gallery.index") }}',
        renderRow: function(gallery, rowNumber) {
            const row = document.createElement('tr');

            // Image column
            let imageHtml = '<span class="text-muted">No Image</span>';
            if (gallery.cover_image) {
                imageHtml = `<img src="{{ asset('storage/') }}/${gallery.cover_image}"
                            alt="${gallery.name}"
                            class="img-thumbnail"
                            style="width: 80px; height: 50px; object-fit: cover;">`;
            }

            // Status badge
            const statusClass = gallery.status === 'active' ? 'success' : 'secondary';

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>${imageHtml}</td>
                <td>${gallery.name}</td>
                <td>${gallery.description ? gallery.description.substring(0, 50) + (gallery.description.length > 50 ? '...' : '') : ''}</td>
                <td><span class="badge badge-${statusClass}">${gallery.status.charAt(0).toUpperCase() + gallery.status.slice(1)}</span></td>
                <td>${new Date(gallery.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}</td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="{{ route('admin.gallery.show', '') }}/${gallery.id}"
                           class="btn btn-info btn-sm" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ route('admin.gallery.edit', '') }}/${gallery.id}"
                           class="btn btn-warning btn-sm" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="{{ route('admin.gallery.destroy', '') }}/${gallery.id}"
                              method="POST"
                              style="display: inline-block;"
                              onsubmit="return confirm('Are you sure you want to delete this gallery?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    @endif
});
</script>
@endpush
