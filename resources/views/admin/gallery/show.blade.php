@extends('admin.layouts.app')

@section('title', 'View Gallery')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ $gallery->name }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.gallery.edit', $gallery) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.gallery.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Main Content -->
                        <div class="col-md-8">
                            <!-- Cover Image -->
                            @if($gallery->cover_image)
                                <div class="mb-4">
                                    <h5>Cover Image</h5>
                                    <img src="{{ asset('storage/' . $gallery->cover_image) }}" 
                                         alt="{{ $gallery->name }}" 
                                         class="img-fluid rounded"
                                         style="max-height: 400px;">
                                </div>
                            @endif

                            <!-- Description -->
                            @if($gallery->description)
                                <div class="mb-4">
                                    <h5>Description</h5>
                                    <p class="text-muted">{{ $gallery->description }}</p>
                                </div>
                            @endif

                            <!-- Gallery Images -->
                            @if($gallery->images)
                                @php
                                    $images = json_decode($gallery->images, true);
                                @endphp
                                @if($images && count($images) > 0)
                                    <div class="mb-4">
                                        <h5>Gallery Images ({{ count($images) }} images)</h5>
                                        <div class="row">
                                            @foreach($images as $image)
                                                <div class="col-md-4 col-sm-6 mb-3">
                                                    <div class="card">
                                                        <img src="{{ asset('storage/' . $image) }}" 
                                                             alt="Gallery Image" 
                                                             class="card-img-top"
                                                             style="height: 200px; object-fit: cover; cursor: pointer;"
                                                             onclick="openImageModal('{{ asset('storage/' . $image) }}')">
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            @endif

                            @if(!$gallery->images || count(json_decode($gallery->images, true) ?: []) == 0)
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No additional gallery images uploaded.
                                </div>
                            @endif
                        </div>

                        <!-- Sidebar -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Gallery Details</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Status -->
                                    <div class="mb-3">
                                        <strong>Status:</strong><br>
                                        <span class="badge badge-{{ $gallery->status == 'active' ? 'success' : 'secondary' }} badge-lg">
                                            {{ ucfirst($gallery->status) }}
                                        </span>
                                    </div>

                                    <!-- Slug -->
                                    <div class="mb-3">
                                        <strong>Slug:</strong><br>
                                        <code>{{ $gallery->slug }}</code>
                                    </div>

                                    <!-- Image Count -->
                                    <div class="mb-3">
                                        <strong>Total Images:</strong><br>
                                        @php
                                            $imageCount = 1; // Cover image
                                            if($gallery->images) {
                                                $images = json_decode($gallery->images, true);
                                                $imageCount += count($images ?: []);
                                            }
                                        @endphp
                                        {{ $imageCount }} image(s)
                                    </div>

                                    <!-- Created Date -->
                                    <div class="mb-3">
                                        <strong>Created:</strong><br>
                                        {{ $gallery->created_at->format('M d, Y \a\t g:i A') }}
                                    </div>

                                    <!-- Updated Date -->
                                    <div class="mb-3">
                                        <strong>Last Updated:</strong><br>
                                        {{ $gallery->updated_at->format('M d, Y \a\t g:i A') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('admin.gallery.edit', $gallery) }}" class="btn btn-warning btn-block">
                                            <i class="fas fa-edit"></i> Edit Gallery
                                        </a>

                                        <form action="{{ route('admin.gallery.destroy', $gallery) }}" 
                                              method="POST" 
                                              onsubmit="return confirm('Are you sure you want to delete this gallery? All images will be permanently deleted.')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-block">
                                                <i class="fas fa-trash"></i> Delete Gallery
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Gallery Image</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Gallery Image" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 0.9em;
    padding: 0.5em 0.75em;
}

.d-grid .btn {
    margin-bottom: 0.5rem;
}
</style>

<script>
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    $('#imageModal').modal('show');
}
</script>
@endsection
