@extends('admin.layouts.app')

@section('title', 'Website Configuration')

@section('content')
<div class="container-fluid">

    <!-- Settings Form -->
    <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <!-- General Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs mr-2"></i>
                    Website Settings
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($settingGroups['general'] as $setting)
                        <div class="col-md-6 mb-3">
                            <label for="{{ $setting->key }}" class="form-label">
                                {{ ucwords(str_replace('_', ' ', $setting->key)) }}
                                @if($setting->description)
                                    <small class="text-muted d-block">{{ $setting->description }}</small>
                                @endif
                            </label>

                            @if($setting->type === 'text')
                                <input type="text"
                                       id="{{ $setting->key }}"
                                       name="settings[{{ $setting->key }}]"
                                       value="{{ $setting->value }}"
                                       class="form-control">
                            @elseif($setting->type === 'image')
                                <div>
                                    @if($setting->value)
                                        <img src="{{ \App\Models\Setting::getImageUrl($setting->key) }}"
                                             alt="{{ ucwords(str_replace('_', ' ', $setting->key)) }}"
                                             class="img-thumbnail mb-2"
                                             style="width: 80px; height: 80px; object-fit: cover;">
                                    @endif
                                    <input type="file"
                                           id="{{ $setting->key }}"
                                           name="files[{{ $setting->key }}]"
                                           accept="image/*"
                                           class="form-control">
                                    <input type="hidden" name="settings[{{ $setting->key }}]" value="{{ $setting->value }}">
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Theme Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-palette mr-2"></i>
                    Theme & Branding
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($settingGroups['theme'] as $setting)
                        <div class="col-md-6 mb-3">
                            <label for="{{ $setting->key }}" class="form-label">
                                {{ ucwords(str_replace('_', ' ', $setting->key)) }}
                                @if($setting->description)
                                    <small class="text-muted d-block">{{ $setting->description }}</small>
                                @endif
                            </label>

                            @if($setting->type === 'text')
                                @if(str_contains($setting->key, 'color'))
                                    <div class="input-group">
                                        <input type="color"
                                               id="{{ $setting->key }}_picker"
                                               value="{{ $setting->value }}"
                                               onchange="document.getElementById('{{ $setting->key }}').value = this.value"
                                               class="form-control form-control-color"
                                               style="width: 60px;">
                                        <input type="text"
                                               id="{{ $setting->key }}"
                                               name="settings[{{ $setting->key }}]"
                                               value="{{ $setting->value }}"
                                               placeholder="#1e3a8a"
                                               class="form-control"
                                               onchange="document.getElementById('{{ $setting->key }}_picker').value = this.value">
                                    </div>
                                @else
                                    <input type="text"
                                           id="{{ $setting->key }}"
                                           name="settings[{{ $setting->key }}]"
                                           value="{{ $setting->value }}"
                                           class="form-control">
                                @endif
                            @elseif($setting->type === 'image')
                                <div>
                                    @if($setting->value)
                                        <img src="{{ \App\Models\Setting::getImageUrl($setting->key) }}"
                                             alt="{{ ucwords(str_replace('_', ' ', $setting->key)) }}"
                                             class="img-thumbnail mb-2"
                                             style="width: 80px; height: 80px; object-fit: cover;">
                                    @endif
                                    <input type="file"
                                           id="{{ $setting->key }}"
                                           name="files[{{ $setting->key }}]"
                                           accept="image/*"
                                           class="form-control">
                                    <input type="hidden" name="settings[{{ $setting->key }}]" value="{{ $setting->value }}">
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-phone mr-2"></i>
                    Contact Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($settingGroups['contact'] as $setting)
                        <div class="col-md-6 mb-3">
                            <label for="{{ $setting->key }}" class="form-label">
                                {{ ucwords(str_replace('_', ' ', $setting->key)) }}
                                @if($setting->description)
                                    <small class="text-muted d-block">{{ $setting->description }}</small>
                                @endif
                            </label>
                            <input type="text"
                                   id="{{ $setting->key }}"
                                   name="settings[{{ $setting->key }}]"
                                   value="{{ $setting->value }}"
                                   class="form-control">
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Social Media Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-share-alt mr-2"></i>
                    Social Media Links
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($settingGroups['social'] as $setting)
                        <div class="col-md-6 mb-3">
                            <label for="{{ $setting->key }}" class="form-label">
                                {{ ucwords(str_replace('_', ' ', $setting->key)) }}
                                @if($setting->description)
                                    <small class="text-muted d-block">{{ $setting->description }}</small>
                                @endif
                            </label>
                            <input type="text"
                                   id="{{ $setting->key }}"
                                   name="settings[{{ $setting->key }}]"
                                   value="{{ $setting->value }}"
                                   class="form-control">
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Payment Method Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-credit-card mr-2"></i>
                    Payment Method Configuration
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($settingGroups['payment'] as $setting)
                        <div class="col-md-{{ $setting->type === 'image' ? '12' : '6' }} mb-3">
                            <label for="{{ $setting->key }}" class="form-label">
                                {{ ucwords(str_replace('_', ' ', $setting->key)) }}
                                @if($setting->description)
                                    <small class="text-muted d-block">{{ $setting->description }}</small>
                                @endif
                            </label>

                            @if($setting->type === 'text')
                                <input type="text"
                                       id="{{ $setting->key }}"
                                       name="settings[{{ $setting->key }}]"
                                       value="{{ $setting->value }}"
                                       class="form-control">
                            @elseif($setting->type === 'boolean')
                                <select id="{{ $setting->key }}"
                                        name="settings[{{ $setting->key }}]"
                                        class="form-control">
                                    <option value="1" {{ $setting->value == '1' ? 'selected' : '' }}>Enabled</option>
                                    <option value="0" {{ $setting->value == '0' ? 'selected' : '' }}>Disabled</option>
                                </select>
                            @elseif($setting->type === 'image')
                                <div>
                                    @if($setting->value)
                                        <div class="mb-3">
                                            <img src="{{ \App\Models\Setting::getImageUrl($setting->key) }}"
                                                 alt="{{ ucwords(str_replace('_', ' ', $setting->key)) }}"
                                                 class="img-thumbnail"
                                                 style="width: 200px; height: 200px; object-fit: contain;">
                                            <div class="mt-2">
                                                <small class="text-muted">Current QR Code Preview</small>
                                            </div>
                                        </div>
                                    @endif
                                    <input type="file"
                                           id="{{ $setting->key }}"
                                           name="files[{{ $setting->key }}]"
                                           accept="image/*"
                                           class="form-control"
                                           onchange="previewQRCode(this)">
                                    <input type="hidden" name="settings[{{ $setting->key }}]" value="{{ $setting->value }}">
                                    <div id="qr-preview" class="mt-3" style="display: none;">
                                        <img id="qr-preview-img" class="img-thumbnail" style="width: 200px; height: 200px; object-fit: contain;">
                                        <div class="mt-2">
                                            <small class="text-muted">New QR Code Preview</small>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- SEO Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-search mr-2"></i>
                    SEO Settings
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($settingGroups['seo'] as $setting)
                        <div class="col-md-12 mb-3">
                            <label for="{{ $setting->key }}" class="form-label">
                                {{ ucwords(str_replace('_', ' ', $setting->key)) }}
                                @if($setting->description)
                                    <small class="text-muted d-block">{{ $setting->description }}</small>
                                @endif
                            </label>
                            @if($setting->key === 'meta_description')
                                <textarea id="{{ $setting->key }}"
                                          name="settings[{{ $setting->key }}]"
                                          rows="3"
                                          class="form-control">{{ $setting->value }}</textarea>
                            @else
                                <input type="text"
                                       id="{{ $setting->key }}"
                                       name="settings[{{ $setting->key }}]"
                                       value="{{ $setting->value }}"
                                       class="form-control">
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="text-right">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save mr-2"></i>
                Save Settings
            </button>
        </div>
    </form>
</div>

<script>
function previewQRCode(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('qr-preview');
            const previewImg = document.getElementById('qr-preview-img');
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    }
}
</script>

@endsection
