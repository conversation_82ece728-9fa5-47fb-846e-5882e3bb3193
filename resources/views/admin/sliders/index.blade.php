@extends('admin.layouts.app')

@section('title', 'Slider')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Slider Management</h3>
                    <div class="card-tools">
                        <!-- Per Page Selector -->
                        <div class="d-inline-block mr-2">
                            <select class="form-control form-control-sm" onchange="changePerPage(this.value)">
                                <option value="10" {{ request('per_page', 10) == 10 ? 'selected' : '' }}>10 per page</option>
                                <option value="20" {{ request('per_page', 10) == 20 ? 'selected' : '' }}>20 per page</option>
                                <option value="50" {{ request('per_page', 10) == 50 ? 'selected' : '' }}>50 per page</option>
                                <option value="100" {{ request('per_page', 10) == 100 ? 'selected' : '' }}>100 per page</option>
                            </select>
                        </div>
                        <a href="{{ route('admin.sliders.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($sliders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">#</th>
                                        <th>Image</th>
                                        <th>Title</th>
                                        <th>Description</th>
                                        <th>Order</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sliders-table-body">
                                    @foreach($sliders as $index => $slider)
                                        <tr>
                                            <td>{{ $sliders->firstItem() + $index }}</td>
                                            <td>
                                                @if($slider->image_path)
                                                    <img src="{{ asset('storage/' . $slider->image_path) }}"
                                                         alt="{{ $slider->title }}"
                                                         class="img-thumbnail"
                                                         style="width: 80px; height: 50px; object-fit: cover;">
                                                @else
                                                    <span class="text-muted">No Image</span>
                                                @endif
                                            </td>
                                            <td>{{ $slider->title }}</td>
                                            <td>{{ Str::limit($slider->description, 50) }}</td>
                                            <td>{{ $slider->sort_order }}</td>
                                            <td>
                                                <span class="badge badge-{{ $slider->status == 'active' ? 'success' : 'secondary' }}">
                                                    {{ ucfirst($slider->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.sliders.show', $slider) }}"
                                                       class="btn btn-info btn-sm" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.sliders.edit', $slider) }}"
                                                       class="btn btn-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.sliders.destroy', $slider) }}"
                                                          method="POST"
                                                          style="display: inline-block;"
                                                          onsubmit="return confirm('Are you sure you want to delete this slider?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Record Count -->
                        <div class="mt-3">
                            <p class="text-muted" id="record-count">
                                Showing {{ $sliders->count() }} of {{ $sliders->total() }} records
                            </p>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No sliders found</h5>
                            <p class="text-muted">Create your first slider to get started.</p>
                            <a href="{{ route('admin.sliders.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Slider
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/infinite-scroll.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($sliders->count() > 0)
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card-body'),
        tableBody: document.getElementById('sliders-table-body'),
        url: '{{ route("admin.sliders.index") }}',
        renderRow: function(slider, rowNumber) {
            const row = document.createElement('tr');

            // Image column
            let imageHtml = '<span class="text-muted">No Image</span>';
            if (slider.image_path) {
                imageHtml = `<img src="{{ asset('storage/') }}/${slider.image_path}"
                            alt="${slider.title}"
                            class="img-thumbnail"
                            style="width: 80px; height: 50px; object-fit: cover;">`;
            }

            // Status badge
            const statusClass = slider.status === 'active' ? 'success' : 'secondary';

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>${imageHtml}</td>
                <td>${slider.title}</td>
                <td>${slider.description ? slider.description.substring(0, 50) + (slider.description.length > 50 ? '...' : '') : ''}</td>
                <td>${slider.sort_order}</td>
                <td><span class="badge badge-${statusClass}">${slider.status.charAt(0).toUpperCase() + slider.status.slice(1)}</span></td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="{{ route('admin.sliders.show', '') }}/${slider.id}"
                           class="btn btn-info btn-sm" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ route('admin.sliders.edit', '') }}/${slider.id}"
                           class="btn btn-warning btn-sm" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="{{ route('admin.sliders.destroy', '') }}/${slider.id}"
                              method="POST"
                              style="display: inline-block;"
                              onsubmit="return confirm('Are you sure you want to delete this slider?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    @endif
});
</script>
@endpush
