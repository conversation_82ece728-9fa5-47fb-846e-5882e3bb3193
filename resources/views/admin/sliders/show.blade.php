@extends('admin.layouts.app')

@section('title', 'View Slider')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Slider Details: {{ $slider->title }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.sliders.edit', $slider) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.sliders.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Slider Image -->
                            @if($slider->image_path)
                            <div class="form-group">
                                <label>Slider Image</label>
                                <div class="text-center">
                                    <img src="{{ asset('storage/' . $slider->image_path) }}" 
                                         alt="{{ $slider->title }}" 
                                         class="img-fluid rounded shadow" 
                                         style="max-width: 100%; max-height: 400px;">
                                </div>
                            </div>
                            @endif

                            <!-- Title -->
                            <div class="form-group">
                                <label>Title</label>
                                <div class="form-control-plaintext border rounded p-2 bg-light">
                                    {{ $slider->title }}
                                </div>
                            </div>

                            <!-- Description -->
                            @if($slider->description)
                            <div class="form-group">
                                <label>Description</label>
                                <div class="form-control-plaintext border rounded p-2 bg-light" style="min-height: 100px;">
                                    {{ $slider->description }}
                                </div>
                            </div>
                            @endif


                        </div>

                        <div class="col-md-4">
                            <!-- Status Card -->
                            <div class="card card-outline card-info">
                                <div class="card-header">
                                    <h3 class="card-title">Slider Information</h3>
                                </div>
                                <div class="card-body">
                                    <!-- Status -->
                                    <div class="form-group">
                                        <label>Status</label>
                                        <div>
                                            <span class="badge badge-{{ $slider->status == 'active' ? 'success' : 'secondary' }} badge-lg">
                                                <i class="fas fa-{{ $slider->status == 'active' ? 'check-circle' : 'times-circle' }}"></i>
                                                {{ ucfirst($slider->status) }}
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Sort Order -->
                                    <div class="form-group">
                                        <label>Sort Order</label>
                                        <div class="form-control-plaintext border rounded p-2 bg-light">
                                            {{ $slider->sort_order }}
                                        </div>
                                    </div>

                                    <!-- Created Date -->
                                    <div class="form-group">
                                        <label>Created</label>
                                        <div class="form-control-plaintext border rounded p-2 bg-light">
                                            {{ $slider->created_at->format('M d, Y \a\t h:i A') }}
                                        </div>
                                    </div>

                                    <!-- Updated Date -->
                                    <div class="form-group">
                                        <label>Last Updated</label>
                                        <div class="form-control-plaintext border rounded p-2 bg-light">
                                            {{ $slider->updated_at->format('M d, Y \a\t h:i A') }}
                                        </div>
                                    </div>

                                    <!-- Image Info -->
                                    @if($slider->image_path)
                                    <div class="form-group">
                                        <label>Image Path</label>
                                        <div class="form-control-plaintext border rounded p-2 bg-light">
                                            <small class="text-muted">{{ $slider->image_path }}</small>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ route('admin.sliders.edit', $slider) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit Slider
                    </a>
                    <form action="{{ route('admin.sliders.destroy', $slider) }}" 
                          method="POST" 
                          style="display: inline-block;"
                          onsubmit="return confirm('Are you sure you want to delete this slider?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Delete Slider
                        </button>
                    </form>
                    <a href="{{ route('admin.sliders.index') }}" class="btn btn-secondary">
                        <i class="fas fa-list"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
