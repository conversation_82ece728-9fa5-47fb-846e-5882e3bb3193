<x-app-layout>
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-gov-blue to-gov-blue-dark text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                {{ $category->name ?? '' }}
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                {{ $category->description ?? (app()->getLocale() === 'hi' ? 'इस श्रेणी में ब्लॉग पोस्ट देखें।' : 'View blog posts in this category.') }}
            </p>
            
            <!-- Breadcrumb -->
            <nav class="mt-6">
                <ol class="flex items-center justify-center space-x-2 text-sm text-blue-200">
                    <li><a href="{{ route('home') }}" class="hover:text-white">{{ app()->getLocale() === 'hi' ? 'होम' : 'Home' }}</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li><a href="{{ route('blog.index') }}" class="hover:text-white">{{ app()->getLocale() === 'hi' ? 'ब्लॉग' : 'Blog' }}</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li class="text-white">{{ $category->name ?? '' }}</li>
                </ol>
            </nav>
        </div>
    </section>

    <!-- Blog Posts -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            @if($blogs->count() > 0)
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($blogs as $blog)
                        <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                            <div class="aspect-video bg-gray-200 relative">
                                @if($blog->featured_image)
                                    <img src="{{ asset('storage/' . $blog->featured_image) }}" 
                                         alt="{{ $blog->title }}"
                                         class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full flex items-center justify-center text-gray-400">
                                        <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                                        </svg>
                                    </div>
                                @endif
                                
                                <span class="absolute top-4 left-4 bg-gov-orange text-white px-3 py-1 rounded-full text-sm font-medium">
                                    {{ $category->name ?? '' }}
                                </span>
                            </div>
                            
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                                    <a href="{{ route('blog.show', $blog->slug) }}" class="hover:text-gov-orange transition-colors">
                                        {{ $blog->title }}
                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 mb-4 line-clamp-3">
                                    {{ $blog->excerpt }}
                                </p>
                                
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    <span>{{ $blog->published_at ? $blog->published_at->format('M d, Y') : 'Draft' }}</span>
                                    <span>{{ $blog->views }} {{ app()->getLocale() === 'hi' ? 'बार देखा गया' : 'views' }}</span>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $blogs->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                        {{ app()->getLocale() === 'hi' ? 'इस श्रेणी में कोई ब्लॉग पोस्ट नहीं मिला' : 'No blog posts found in this category' }}
                    </h3>
                    <p class="text-gray-600 mb-6">
                        {{ app()->getLocale() === 'hi' ? 'कृपया बाद में वापस जांचें या अन्य श्रेणियों को देखें।' : 'Please check back later or explore other categories.' }}
                    </p>
                    <a href="{{ route('blog.index') }}" class="bg-gov-orange text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors inline-block">
                        {{ app()->getLocale() === 'hi' ? 'सभी ब्लॉग देखें' : 'View All Blogs' }}
                    </a>
                </div>
            @endif
        </div>
    </section>


</x-app-layout>
