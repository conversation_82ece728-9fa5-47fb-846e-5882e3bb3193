<x-app-layout>
    <!-- Blog Header -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <!-- Breadcrumb -->
                <nav class="mb-6">
                    <ol class="flex items-center space-x-2 text-sm text-gray-600">
                        <li><a href="{{ route('home') }}" class="hover:text-gov-orange">{{ app()->getLocale() === 'hi' ? 'होम' : 'Home' }}</a></li>
                        <li><span class="mx-2">/</span></li>
                        <li><a href="{{ route('blog.index') }}" class="hover:text-gov-orange">{{ app()->getLocale() === 'hi' ? 'ब्लॉग' : 'Blog' }}</a></li>
                        <li><span class="mx-2">/</span></li>
                        <li class="text-gray-900">{{ Str::limit($blog->title, 50) }}</li>
                    </ol>
                </nav>

                <!-- Category -->
                @if($blog->category)
                    <div class="mb-4">
                        <span class="inline-block bg-gov-orange text-white px-4 py-2 rounded-full text-sm font-medium">
                            {{ $blog->category->name }}
                        </span>
                    </div>
                @endif

                <!-- Title -->
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    {{ $blog->title }}
                </h1>

                <!-- Meta Info -->
                <div class="flex flex-wrap items-center gap-6 text-gray-600 mb-8">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        {{ $blog->published_at ? $blog->published_at->format('F d, Y') : 'Draft' }}
                    </div>
                    
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        </svg>
                        {{ $blog->views }} {{ app()->getLocale() === 'hi' ? 'बार देखा गया' : 'views' }}
                    </div>

                    @if($blog->author)
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                            {{ $blog->author->name }}
                        </div>
                    @endif
                </div>

                <!-- Featured Image -->
                @if($blog->featured_image)
                    <div class="mb-8">
                        <img src="{{ asset('storage/' . $blog->featured_image) }}" 
                             alt="{{ $blog->title }}"
                             class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg">
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Blog Content -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <!-- Excerpt -->
                @if($blog->excerpt)
                    <div class="text-xl text-gray-600 mb-8 p-6 bg-gray-50 rounded-lg border-l-4 border-gov-orange">
                        {{ $blog->excerpt }}
                    </div>
                @endif

                <!-- Content -->
                <div class="prose prose-lg max-w-none">
                    {!! nl2br(e($blog->content)) !!}
                </div>

                <!-- Tags -->
                @if($blog->tags && count($blog->tags) > 0)
                    <div class="mt-12 pt-8 border-t border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            {{ app()->getLocale() === 'hi' ? 'टैग्स' : 'Tags' }}
                        </h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($blog->tags as $tag)
                                <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                                    {{ $tag }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Share -->
                <div class="mt-8 pt-8 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        {{ app()->getLocale() === 'hi' ? 'साझा करें' : 'Share this post' }}
                    </h3>
                    <div class="flex gap-4">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" 
                           target="_blank"
                           class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($blog->title) }}" 
                           target="_blank"
                           class="bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors">
                            Twitter
                        </a>
                        <a href="https://wa.me/?text={{ urlencode($blog->title . ' - ' . request()->url()) }}" 
                           target="_blank"
                           class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            WhatsApp
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Posts -->
    @if($relatedBlogs && $relatedBlogs->count() > 0)
        <section class="py-12 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
                        {{ app()->getLocale() === 'hi' ? 'संबंधित पोस्ट' : 'Related Posts' }}
                    </h2>
                    
                    <div class="grid md:grid-cols-3 gap-8">
                        @foreach($relatedBlogs as $relatedBlog)
                            <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                                <div class="aspect-video bg-gray-200 relative">
                                    @if($relatedBlog->featured_image)
                                        <img src="{{ asset('storage/' . $relatedBlog->featured_image) }}" 
                                             alt="{{ $relatedBlog->title }}"
                                             class="w-full h-full object-cover">
                                    @else
                                        <div class="w-full h-full flex items-center justify-center text-gray-400">
                                            <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                                            </svg>
                                        </div>
                                    @endif
                                </div>
                                
                                <div class="p-6">
                                    <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
                                        <a href="{{ route('blog.show', $relatedBlog->slug) }}" class="hover:text-gov-orange transition-colors">
                                            {{ $relatedBlog->title }}
                                        </a>
                                    </h3>
                                    
                                    <p class="text-gray-600 mb-4 line-clamp-2">
                                        {{ $relatedBlog->excerpt }}
                                    </p>
                                    
                                    <div class="text-sm text-gray-500">
                                        {{ $relatedBlog->published_at ? $relatedBlog->published_at->format('M d, Y') : 'Draft' }}
                                    </div>
                                </div>
                            </article>
                        @endforeach
                    </div>
                </div>
            </div>
        </section>
    @endif
</x-app-layout>
