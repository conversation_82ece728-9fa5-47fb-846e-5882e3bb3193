<x-app-layout>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-16">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4 font-hindi">
                    {{ app()->getLocale() === 'hi' ? 'ब्लॉग' : 'Blog' }}
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                    {{ app()->getLocale() === 'hi' ? 'हमारे कार्यों और समुदायिक गतिविधियों के बारे में नवीनतम ब्लॉग और अपडेट पढ़ें।' : 'Read the latest blogs and updates about our work and community activities.' }}
                </p>
            </div>
        </div>
    </section>

    <!-- Filters and Search -->
    <section class="py-8 bg-gray-50">
        <div class="container mx-auto px-4">
            <form method="GET" action="{{ route('blog.index') }}" class="flex flex-wrap gap-4 items-center justify-between">
                <!-- Search -->
                <div class="flex-1 min-w-64">
                    <input type="text" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="{{ app()->getLocale() === 'hi' ? 'ब्लॉग खोजें...' : 'Search blogs...' }}"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gov-orange focus:border-transparent">
                </div>

                <!-- Category Filter -->
                <div class="min-w-48">
                    <select name="category" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gov-orange focus:border-transparent">
                        <option value="">{{ app()->getLocale() === 'hi' ? 'सभी श्रेणियां' : 'All Categories' }}</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->slug }}" {{ request('category') === $category->slug ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Sort -->
                <div class="min-w-48">
                    <select name="sort" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gov-orange focus:border-transparent">
                        <option value="latest" {{ request('sort') === 'latest' ? 'selected' : '' }}>
                            {{ app()->getLocale() === 'hi' ? 'नवीनतम' : 'Latest' }}
                        </option>
                        <option value="oldest" {{ request('sort') === 'oldest' ? 'selected' : '' }}>
                            {{ app()->getLocale() === 'hi' ? 'पुराने' : 'Oldest' }}
                        </option>
                        <option value="title" {{ request('sort') === 'title' ? 'selected' : '' }}>
                            {{ app()->getLocale() === 'hi' ? 'शीर्षक' : 'Title' }}
                        </option>
                    </select>
                </div>

                <button type="submit" class="bg-gov-orange text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    {{ app()->getLocale() === 'hi' ? 'खोजें' : 'Search' }}
                </button>
            </form>
        </div>
    </section>

    <!-- Blog Posts -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            @if($blogs->count() > 0)
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($blogs as $blog)
                        <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                            <div class="aspect-video bg-gray-200 relative">
                                @if($blog->featured_image)
                                    <img src="{{ asset('storage/' . $blog->featured_image) }}" 
                                         alt="{{ $blog->title }}"
                                         class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full flex items-center justify-center text-gray-400">
                                        <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                                        </svg>
                                    </div>
                                @endif
                                
                                @if($blog->category)
                                    <span class="absolute top-4 left-4 bg-gov-orange text-white px-3 py-1 rounded-full text-sm font-medium">
                                        {{ $blog->category->name }}
                                    </span>
                                @endif
                            </div>
                            
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                                    <a href="{{ route('blog.show', $blog->slug) }}" class="hover:text-gov-orange transition-colors">
                                        {{ $blog->title }}
                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 mb-4 line-clamp-3">
                                    {{ $blog->excerpt }}
                                </p>
                                
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    <span>{{ $blog->published_at ? $blog->published_at->format('M d, Y') : 'Draft' }}</span>
                                    <span>{{ $blog->views }} {{ app()->getLocale() === 'hi' ? 'बार देखा गया' : 'views' }}</span>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $blogs->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                        {{ app()->getLocale() === 'hi' ? 'कोई ब्लॉग पोस्ट नहीं मिला' : 'No blog posts found' }}
                    </h3>
                    <p class="text-gray-600">
                        {{ app()->getLocale() === 'hi' ? 'कृपया अपने खोज मानदंड को समायोजित करें या बाद में वापस जांचें।' : 'Please adjust your search criteria or check back later.' }}
                    </p>
                </div>
            @endif
        </div>
    </section>


</x-app-layout>
