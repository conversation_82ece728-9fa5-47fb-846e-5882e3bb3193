<x-app-layout>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-16">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto">
                <div class="flex items-center mb-4">
                    <a href="{{ route('causes.index') }}" 
                       class="text-white hover:text-gray-300 flex items-center mr-4">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                        </svg>
                        {{ app()->getLocale() === 'hi' ? 'सभी कार्यक्रम' : 'All Causes' }}
                    </a>
                    @if($cause->is_featured)
                        <span class="bg-gov-orange text-white px-3 py-1 rounded-full text-sm font-semibold">
                            {{ app()->getLocale() === 'hi' ? 'फीचर्ड' : 'Featured' }}
                        </span>
                    @endif
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold mb-4 font-hindi">
                    {{ $cause->title }}
                </h1>
                
                <p class="text-xl md:text-2xl text-gray-200 mb-6">
                    {{ $cause->short_description }}
                </p>

                <!-- Progress Stats -->
                <div class="grid md:grid-cols-3 gap-6 bg-white/10 backdrop-blur-sm rounded-lg p-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold mb-2">{{ $cause->formatted_raised_amount }}</div>
                        <div class="text-gray-200">{{ app()->getLocale() === 'hi' ? 'संग्रहित राशि' : 'Raised Amount' }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold mb-2">{{ $cause->formatted_goal_amount }}</div>
                        <div class="text-gray-200">{{ app()->getLocale() === 'hi' ? 'लक्ष्य राशि' : 'Goal Amount' }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold mb-2">{{ number_format($cause->progress_percentage, 1) }}%</div>
                        <div class="text-gray-200">{{ app()->getLocale() === 'hi' ? 'प्रगति' : 'Progress' }}</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-3 gap-12">
                    <!-- Main Content -->
                    <div class="lg:col-span-2">
                        <!-- Featured Image -->
                        @if($cause->featured_image)
                            <div class="mb-8">
                                <img src="{{ $cause->featured_image_url }}" 
                                     alt="{{ $cause->title }}"
                                     class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg">
                            </div>
                        @endif

                        <!-- Description -->
                        <div class="prose prose-lg max-w-none mb-8">
                            <h2 class="text-2xl font-bold text-gov-blue mb-4 font-hindi">
                                {{ app()->getLocale() === 'hi' ? 'विवरण' : 'Description' }}
                            </h2>
                            <div class="text-gray-700 leading-relaxed">
                                {!! nl2br(e($cause->description)) !!}
                            </div>
                        </div>

                        <!-- Timeline -->
                        <div class="bg-gray-50 rounded-lg p-6 mb-8">
                            <h3 class="text-xl font-bold text-gov-blue mb-4 font-hindi">
                                {{ app()->getLocale() === 'hi' ? 'समयसीमा' : 'Timeline' }}
                            </h3>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <div class="text-sm text-gray-500 mb-1">
                                        {{ app()->getLocale() === 'hi' ? 'प्रारंभ तिथि' : 'Start Date' }}
                                    </div>
                                    <div class="font-semibold">
                                        {{ $cause->start_date ? $cause->start_date->format('d M Y') : 'N/A' }}
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500 mb-1">
                                        {{ app()->getLocale() === 'hi' ? 'समाप्ति तिथि' : 'End Date' }}
                                    </div>
                                    <div class="font-semibold">
                                        {{ $cause->end_date ? $cause->end_date->format('d M Y') : 'N/A' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Share Section -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold text-gov-blue mb-4 font-hindi">
                                {{ app()->getLocale() === 'hi' ? 'साझा करें' : 'Share This Cause' }}
                            </h3>
                            <div class="flex flex-wrap gap-3">
                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" 
                                   target="_blank"
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                    </svg>
                                    Facebook
                                </a>
                                <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($cause->title) }}" 
                                   target="_blank"
                                   class="bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                    Twitter
                                </a>
                                <a href="https://wa.me/?text={{ urlencode($cause->title . ' - ' . request()->url()) }}" 
                                   target="_blank"
                                   class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                    </svg>
                                    WhatsApp
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="lg:col-span-1">
                        <!-- Donation Card -->
                        <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-6 mb-8 sticky top-8">
                            <!-- Progress Bar -->
                            <div class="mb-6">
                                <div class="flex justify-between text-sm text-gray-600 mb-2">
                                    <span>{{ app()->getLocale() === 'hi' ? 'प्रगति' : 'Progress' }}</span>
                                    <span>{{ number_format($cause->progress_percentage, 1) }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="bg-gov-green h-3 rounded-full transition-all duration-300" 
                                         style="width: {{ min(100, $cause->progress_percentage) }}%"></div>
                                </div>
                            </div>

                            <!-- Amount Details -->
                            <div class="space-y-3 mb-6">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">{{ app()->getLocale() === 'hi' ? 'संग्रहित' : 'Raised' }}</span>
                                    <span class="font-bold text-gov-blue">{{ $cause->formatted_raised_amount }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">{{ app()->getLocale() === 'hi' ? 'लक्ष्य' : 'Goal' }}</span>
                                    <span class="font-bold text-gov-blue">{{ $cause->formatted_goal_amount }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">{{ app()->getLocale() === 'hi' ? 'शेष' : 'Remaining' }}</span>
                                    <span class="font-bold text-gray-700">{{ $cause->formatted_remaining_amount }}</span>
                                </div>
                            </div>

                            <!-- Donate Button -->
                            <a href="{{ route('donate.cause', $cause) }}" 
                               class="w-full bg-gov-orange hover:bg-orange-600 text-white text-center py-3 px-6 rounded-lg font-semibold transition-colors block mb-4">
                                {{ app()->getLocale() === 'hi' ? 'अभी दान करें' : 'Donate Now' }}
                            </a>

                            <!-- Status -->
                            <div class="text-center">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    {{ $cause->status === 'active' ? 'bg-green-100 text-green-800' : 
                                       ($cause->status === 'completed' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                                    @if($cause->status === 'active')
                                        {{ app()->getLocale() === 'hi' ? 'सक्रिय' : 'Active' }}
                                    @elseif($cause->status === 'completed')
                                        {{ app()->getLocale() === 'hi' ? 'पूर्ण' : 'Completed' }}
                                    @else
                                        {{ app()->getLocale() === 'hi' ? 'निष्क्रिय' : 'Inactive' }}
                                    @endif
                                </span>
                            </div>
                        </div>

                        <!-- Related Causes -->
                        @if(isset($relatedCauses) && $relatedCauses->count() > 0)
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h3 class="text-xl font-bold text-gov-blue mb-4 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'अन्य कार्यक्रम' : 'Other Causes' }}
                                </h3>
                                <div class="space-y-4">
                                    @foreach($relatedCauses as $relatedCause)
                                        <div class="bg-white rounded-lg p-4 shadow-sm">
                                            <h4 class="font-semibold text-gov-blue mb-2">
                                                <a href="{{ route('causes.show', $relatedCause) }}" class="hover:underline">
                                                    {{ $relatedCause->title }}
                                                </a>
                                            </h4>
                                            <p class="text-sm text-gray-600 mb-2">
                                                {{ Str::limit($relatedCause->short_description, 80) }}
                                            </p>
                                            <div class="flex justify-between text-xs text-gray-500">
                                                <span>{{ $relatedCause->formatted_raised_amount }}</span>
                                                <span>{{ number_format($relatedCause->progress_percentage, 1) }}%</span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>
</x-app-layout>
