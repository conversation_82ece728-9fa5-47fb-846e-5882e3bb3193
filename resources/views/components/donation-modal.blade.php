<!-- Donation Modal -->
<div id="donationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
        <!-- <PERSON><PERSON> Header -->
        <div class="bg-gov-blue text-white p-6 rounded-t-lg">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-bold font-hindi">
                    {{ app()->getLocale() === 'hi' ? 'दान करें' : 'Make Donation' }}
                </h2>
                <button onclick="closeDonationModal()" class="text-white hover:text-gray-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <form id="donationForm" action="{{ route('donate.process') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="cause_id" id="modal_cause_id" value="">
                <input type="hidden" name="payment_method" value="qr_code">
                
                <!-- Amount Section -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'दान की राशि (₹)' : 'Donation Amount (₹)' }} *
                    </label>
                    
                    <!-- Quick Amount Buttons -->
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <button type="button" onclick="setModalAmount(500)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                            ₹500
                        </button>
                        <button type="button" onclick="setModalAmount(1000)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                            ₹1,000
                        </button>
                        <button type="button" onclick="setModalAmount(2500)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                            ₹2,500
                        </button>
                        <button type="button" onclick="setModalAmount(5000)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                            ₹5,000
                        </button>
                    </div>
                    
                    <input type="number" name="amount" id="modal_amount" min="1" step="0.01" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                           placeholder="{{ app()->getLocale() === 'hi' ? 'कस्टम राशि दर्ज करें' : 'Enter custom amount' }}">
                </div>

                <!-- QR Code Section -->
                @if(\App\Models\Setting::get('qr_payment_enabled', '1') == '1')
                <div class="mb-6 text-center">
                    <label class="block text-gray-700 text-sm font-bold mb-4 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'भुगतान QR कोड' : 'Payment QR Code' }}
                    </label>
                    
                    @php
                        $qrCodeUrl = \App\Models\Setting::getImageUrl('payment_qr_code');
                    @endphp
                    
                    @if($qrCodeUrl)
                        <div class="bg-gray-50 p-4 rounded-lg inline-block">
                            <img src="{{ $qrCodeUrl }}" alt="Payment QR Code" class="w-48 h-48 object-contain mx-auto border rounded shadow">
                        </div>
                        <p class="mt-3 text-gray-600 text-sm font-hindi">
                            {{ \App\Models\Setting::get('payment_instructions', app()->getLocale() === 'hi' ? 'कृपया ऊपर दिए गए QR कोड को स्कैन करके भुगतान करें।' : 'Please scan the QR code above to make your payment.') }}
                        </p>
                    @else
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <p class="text-yellow-800 font-hindi">
                                {{ app()->getLocale() === 'hi' ? 'QR कोड उपलब्ध नहीं है। कृपया व्यवस्थापक से संपर्क करें।' : 'QR code not available. Please contact administrator.' }}
                            </p>
                        </div>
                    @endif
                </div>
                @endif

                <!-- Donor Information -->
                <div class="grid md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'पूरा नाम' : 'Full Name' }} *
                        </label>
                        <input type="text" name="donor_name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                               placeholder="{{ app()->getLocale() === 'hi' ? 'आपका नाम' : 'Your Name' }}">
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'ईमेल पता' : 'Email Address' }} *
                        </label>
                        <input type="email" name="donor_email" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                               placeholder="{{ app()->getLocale() === 'hi' ? 'आपका ईमेल' : 'Your Email' }}">
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'फोन नंबर' : 'Phone Number' }}
                    </label>
                    <input type="tel" name="donor_phone"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                           placeholder="{{ app()->getLocale() === 'hi' ? 'आपका फोन नंबर' : 'Your Phone Number' }}">
                </div>

                <!-- Payment Screenshot Upload -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'भुगतान स्क्रीनशॉट' : 'Payment Screenshot' }} *
                    </label>
                    <input type="file" name="payment_screenshot" accept="image/*" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                           onchange="previewScreenshot(this)">
                    <small class="text-gray-500 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'भुगतान के बाद स्क्रीनशॉट अपलोड करें' : 'Upload screenshot after making payment' }}
                    </small>
                    
                    <!-- Screenshot Preview -->
                    <div id="screenshot-preview" class="mt-3 hidden">
                        <img id="screenshot-preview-img" class="w-32 h-32 object-cover border rounded">
                        <small class="block text-gray-500 mt-1">{{ app()->getLocale() === 'hi' ? 'स्क्रीनशॉट पूर्वावलोकन' : 'Screenshot Preview' }}</small>
                    </div>
                </div>

                <!-- Message -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'संदेश (वैकल्पिक)' : 'Message (Optional)' }}
                    </label>
                    <textarea name="message" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                              placeholder="{{ app()->getLocale() === 'hi' ? 'आपका संदेश' : 'Your message' }}"></textarea>
                </div>

                <!-- Anonymous Donation -->
                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_anonymous" value="1" class="mr-2">
                        <span class="text-gray-700 font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'गुमनाम दान' : 'Anonymous donation' }}
                        </span>
                    </label>
                </div>

                <!-- Submit Button -->
                <div class="flex gap-3">
                    <button type="button" onclick="closeDonationModal()" 
                            class="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                        {{ app()->getLocale() === 'hi' ? 'रद्द करें' : 'Cancel' }}
                    </button>
                    <button type="submit" 
                            class="flex-1 bg-gov-orange hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                        {{ app()->getLocale() === 'hi' ? 'दान जमा करें' : 'Submit Donation' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openDonationModal(causeId = null) {
    document.getElementById('modal_cause_id').value = causeId || '';
    document.getElementById('donationModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeDonationModal() {
    document.getElementById('donationModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    // Reset form
    document.getElementById('donationForm').reset();
    document.getElementById('screenshot-preview').classList.add('hidden');
}

function setModalAmount(amount) {
    document.getElementById('modal_amount').value = amount;
}

function previewScreenshot(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('screenshot-preview');
            const previewImg = document.getElementById('screenshot-preview-img');
            previewImg.src = e.target.result;
            preview.classList.remove('hidden');
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Close modal when clicking outside
document.getElementById('donationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDonationModal();
    }
});
</script>
