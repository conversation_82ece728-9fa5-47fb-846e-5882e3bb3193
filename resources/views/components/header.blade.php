<!-- Top Government Bar -->
<div class="bg-tricolor-gradient h-1"></div>

<!-- Top Info Bar -->
<div class="bg-gov-navy text-white py-2 text-sm">
    <div class="container mx-auto px-4">
        <div class="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
            <div class="flex items-center space-x-4">
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                    {{ \App\Models\Setting::get('contact_email', '<EMAIL>') }}
                </span>
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                    </svg>
                    {{ \App\Models\Setting::get('contact_phone', '+91 98765 43210') }}
                </span>
            </div>
            
            <div class="flex items-center space-x-4">
                <!-- Social Links -->
                <div class="flex space-x-2">
                    @if(\App\Models\Setting::get('facebook_url'))
                        <a href="{{ \App\Models\Setting::get('facebook_url') }}" target="_blank" class="hover:text-gov-orange transition-colors" title="Facebook">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                    @endif

                    @if(\App\Models\Setting::get('twitter_url'))
                        <a href="{{ \App\Models\Setting::get('twitter_url') }}" target="_blank" class="hover:text-gov-orange transition-colors" title="Twitter">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                    @endif

                    @if(\App\Models\Setting::get('instagram_url'))
                        <a href="{{ \App\Models\Setting::get('instagram_url') }}" target="_blank" class="hover:text-gov-orange transition-colors" title="Instagram">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                            </svg>
                        </a>
                    @endif

                    @if(\App\Models\Setting::get('youtube_url'))
                        <a href="{{ \App\Models\Setting::get('youtube_url') }}" target="_blank" class="hover:text-gov-orange transition-colors" title="YouTube">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="bg-white shadow-lg sticky top-0 z-40">
    <div class="container mx-auto px-4">
        <div class="flex items-center justify-between py-4">
            <!-- Logo and NGO Name -->
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                    <img src="{{ \App\Models\Setting::getImageUrl('logo', 'images/logo.png') }}" alt="NGO Logo" class="h-12 w-12 rounded-full border-2 border-secondary">
                </div>
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-primary font-hindi">
                        {{ \App\Models\Setting::get('site_name', 'NGO Portal') }}
                    </h1>
                    <p class="text-sm text-gray-600 font-hindi">
                        {{ \App\Models\Setting::get('site_tagline', 'समाज सेवा में अग्रणी') }}
                    </p>
                </div>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden lg:flex items-center space-x-8">
                <a href="{{ route('home') }}" class="text-gray-700 hover:text-secondary font-medium transition-colors {{ request()->routeIs('home') ? 'text-secondary border-b-2 border-secondary' : '' }}">
                    {{ app()->getLocale() === 'hi' ? 'होम' : 'Home' }}
                </a>
                <a href="{{ route('about') }}" class="text-gray-700 hover:text-secondary font-medium transition-colors {{ request()->routeIs('about') ? 'text-secondary border-b-2 border-secondary' : '' }}">
                    {{ app()->getLocale() === 'hi' ? 'हमारे बारे में' : 'About Us' }}
                </a>
                <a href="{{ route('causes.index') }}" class="text-gray-700 hover:text-secondary font-medium transition-colors {{ request()->routeIs('causes.*') ? 'text-secondary border-b-2 border-secondary' : '' }}">
                    {{ app()->getLocale() === 'hi' ? 'कारण' : 'Causes' }}
                </a>
                <a href="{{ route('blog.index') }}" class="text-gray-700 hover:text-secondary font-medium transition-colors {{ request()->routeIs('blog.*') ? 'text-secondary border-b-2 border-secondary' : '' }}">
                    {{ app()->getLocale() === 'hi' ? 'ब्लॉग' : 'Blogs' }}
                </a>
                <a href="{{ route('gallery.index') }}" class="text-gray-700 hover:text-secondary font-medium transition-colors {{ request()->routeIs('gallery.*') ? 'text-secondary border-b-2 border-secondary' : '' }}">
                    {{ app()->getLocale() === 'hi' ? 'गैलरी' : 'Gallery' }}
                </a>
                <a href="{{ route('contact.index') }}" class="text-gray-700 hover:text-secondary font-medium transition-colors {{ request()->routeIs('contact.*') ? 'text-secondary border-b-2 border-secondary' : '' }}">
                    {{ app()->getLocale() === 'hi' ? 'संपर्क' : 'Contact' }}
                </a>
            </nav>

            <!-- Donate Button & Mobile Menu -->
            <div class="flex items-center space-x-4">
                <!-- Donate Button -->
                <a href="{{ route('donate.index') }}" class="bg-secondary hover:bg-secondary text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"></path>
                    </svg>
                    <span>{{ app()->getLocale() === 'hi' ? 'दान करें' : 'Donate' }}</span>
                </a>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="lg:hidden p-2 rounded-md text-gray-700 hover:text-secondary focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="lg:hidden hidden bg-white border-t border-gray-200">
        <div class="px-4 py-2 space-y-1">
            <a href="{{ route('home') }}" class="block px-3 py-2 text-gray-700 hover:text-secondary hover:bg-gray-50 rounded-md {{ request()->routeIs('home') ? 'text-secondary bg-orange-50' : '' }}">
                {{ app()->getLocale() === 'hi' ? 'होम' : 'Home' }}
            </a>
            <a href="{{ route('about') }}" class="block px-3 py-2 text-gray-700 hover:text-secondary hover:bg-gray-50 rounded-md {{ request()->routeIs('about') ? 'text-secondary bg-orange-50' : '' }}">
                {{ app()->getLocale() === 'hi' ? 'हमारे बारे में' : 'About Us' }}
            </a>
            <a href="{{ route('causes.index') }}" class="block px-3 py-2 text-gray-700 hover:text-secondary hover:bg-gray-50 rounded-md {{ request()->routeIs('causes.*') ? 'text-secondary bg-orange-50' : '' }}">
                {{ app()->getLocale() === 'hi' ? 'कारण' : 'Causes' }}
            </a>
            <a href="{{ route('blog.index') }}" class="block px-3 py-2 text-gray-700 hover:text-secondary hover:bg-gray-50 rounded-md {{ request()->routeIs('blog.*') ? 'text-secondary bg-orange-50' : '' }}">
                {{ app()->getLocale() === 'hi' ? 'ब्लॉग' : 'Blogs' }}
            </a>
            <a href="{{ route('gallery.index') }}" class="block px-3 py-2 text-gray-700 hover:text-secondary hover:bg-gray-50 rounded-md {{ request()->routeIs('gallery.*') ? 'text-secondary bg-orange-50' : '' }}">
                {{ app()->getLocale() === 'hi' ? 'गैलरी' : 'Gallery' }}
            </a>
            <a href="{{ route('contact.index') }}" class="block px-3 py-2 text-gray-700 hover:text-secondary hover:bg-gray-50 rounded-md {{ request()->routeIs('contact.*') ? 'text-secondary bg-orange-50' : '' }}">
                {{ app()->getLocale() === 'hi' ? 'संपर्क' : 'Contact' }}
            </a>
        </div>
    </div>
</header>

<script>
    // Mobile menu toggle
    document.getElementById('mobile-menu-button').addEventListener('click', function() {
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenu.classList.toggle('hidden');
    });
</script>
