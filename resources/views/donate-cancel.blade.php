<x-app-layout>
    @section('title', app()->getLocale() === 'hi' ? 'दान रद्द - एनजीओ पोर्टल' : 'Donation Cancelled - NGO Portal')
    @section('meta_description', app()->getLocale() === 'hi' ? 'दान प्रक्रिया रद्द कर दी गई है।' : 'Donation process has been cancelled.')

    <!-- Cancel Section -->
    <section class="py-20 bg-gradient-to-r from-red-400 to-red-600">
        <div class="container mx-auto px-4 text-center text-white">
            <!-- Cancel Icon -->
            <div class="mb-8">
                <div class="inline-flex items-center justify-center w-24 h-24 bg-white rounded-full">
                    <svg class="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
            </div>
            
            <h1 class="text-4xl md:text-6xl font-bold mb-6 font-hindi">
                {{ app()->getLocale() === 'hi' ? 'दान रद्द' : 'Donation Cancelled' }}
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                {{ app()->getLocale() === 'hi' 
                    ? 'आपकी दान प्रक्रिया रद्द कर दी गई है।'
                    : 'Your donation process has been cancelled.' }}
            </p>
        </div>
    </section>

    <!-- Information Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto text-center">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'क्या हुआ?' : 'What Happened?' }}
                    </h2>
                    
                    <div class="text-gray-600 mb-8 space-y-4">
                        <p class="font-hindi">
                            {{ app()->getLocale() === 'hi' 
                                ? 'आपने दान प्रक्रिया को रद्द कर दिया है या भुगतान पूरा नहीं हुआ है।'
                                : 'You have cancelled the donation process or the payment was not completed.' }}
                        </p>
                        
                        <p class="font-hindi">
                            {{ app()->getLocale() === 'hi' 
                                ? 'कोई राशि आपके खाते से काटी नहीं गई है।'
                                : 'No amount has been deducted from your account.' }}
                        </p>
                    </div>
                    
                    <!-- Reasons -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-8">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800 font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'संभावित कारण:' : 'Possible Reasons:' }}
                        </h3>
                        
                        <ul class="text-left text-gray-600 space-y-2">
                            <li class="flex items-start">
                                <span class="text-red-500 mr-2">•</span>
                                <span class="font-hindi">
                                    {{ app()->getLocale() === 'hi' 
                                        ? 'आपने भुगतान प्रक्रिया को रद्द कर दिया'
                                        : 'You cancelled the payment process' }}
                                </span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-red-500 mr-2">•</span>
                                <span class="font-hindi">
                                    {{ app()->getLocale() === 'hi' 
                                        ? 'भुगतान गेटवे में तकनीकी समस्या'
                                        : 'Technical issue with payment gateway' }}
                                </span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-red-500 mr-2">•</span>
                                <span class="font-hindi">
                                    {{ app()->getLocale() === 'hi' 
                                        ? 'इंटरनेट कनेक्शन की समस्या'
                                        : 'Internet connection issue' }}
                                </span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-red-500 mr-2">•</span>
                                <span class="font-hindi">
                                    {{ app()->getLocale() === 'hi' 
                                        ? 'अपर्याप्त खाता शेष'
                                        : 'Insufficient account balance' }}
                                </span>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('donate.index') }}" 
                           class="bg-gov-orange hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'फिर से दान करें' : 'Try Donating Again' }}
                        </a>
                        
                        <a href="{{ route('home') }}" 
                           class="bg-gov-blue hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'होम पेज पर जाएं' : 'Go to Home' }}
                        </a>
                        
                        <a href="{{ route('contact.index') }}"
                           class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'सहायता संपर्क' : 'Contact Support' }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Help Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-center mb-12 text-gov-navy font-hindi">
                    {{ app()->getLocale() === 'hi' ? 'सहायता चाहिए?' : 'Need Help?' }}
                </h2>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Contact Info -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 text-gov-navy font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'हमसे संपर्क करें' : 'Contact Us' }}
                        </h3>
                        
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gov-blue mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                </svg>
                                <span><EMAIL></span>
                            </div>
                            
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gov-blue mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                </svg>
                                <span>+91 78129199</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- FAQ -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 text-gov-navy font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'अक्सर पूछे जाने वाले प्रश्न' : 'Frequently Asked Questions' }}
                        </h3>
                        
                        <div class="space-y-3">
                            <div>
                                <h4 class="font-semibold text-gray-800 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'क्या मेरा पैसा सुरक्षित है?' : 'Is my money safe?' }}
                                </h4>
                                <p class="text-gray-600 text-sm font-hindi">
                                    {{ app()->getLocale() === 'hi' 
                                        ? 'हां, हम सुरक्षित भुगतान गेटवे का उपयोग करते हैं।'
                                        : 'Yes, we use secure payment gateways.' }}
                                </p>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold text-gray-800 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'क्या मुझे रसीद मिलेगी?' : 'Will I get a receipt?' }}
                                </h4>
                                <p class="text-gray-600 text-sm font-hindi">
                                    {{ app()->getLocale() === 'hi' 
                                        ? 'हां, सफल दान पर आपको ईमेल रसीद मिलेगी।'
                                        : 'Yes, you will receive an email receipt upon successful donation.' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Encouragement Section -->
    <section class="py-16 bg-gov-blue text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6 font-hindi">
                {{ app()->getLocale() === 'hi' ? 'आपका योगदान मायने रखता है' : 'Your Contribution Matters' }}
            </h2>
            <p class="text-xl mb-8 max-w-3xl mx-auto">
                {{ app()->getLocale() === 'hi' 
                    ? 'हर छोटा योगदान एक बड़ा बदलाव लाता है। कृपया फिर से कोशिश करें।'
                    : 'Every small contribution makes a big difference. Please try again.' }}
            </p>
            
            <a href="{{ route('donate.index') }}" 
               class="bg-gov-orange hover:bg-orange-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors inline-flex items-center">
                <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"></path>
                </svg>
                {{ app()->getLocale() === 'hi' ? 'अभी दान करें' : 'Donate Now' }}
            </a>
        </div>
    </section>
</x-app-layout>
