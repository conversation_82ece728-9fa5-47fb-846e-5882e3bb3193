<x-app-layout>
    @section('title', app()->getLocale() === 'hi' ? 'दान करें - ' . $cause->title : 'Donate - ' . $cause->title)
    @section('meta_description', Str::limit($cause->description, 160))

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="relative container mx-auto px-4 text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 font-hindi">
                {{ app()->getLocale() === 'hi' ? 'दान करें' : 'Donate Now' }}
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                {{ $cause->title }}
            </p>
        </div>
    </section>

    <!-- Donation Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Cause Details -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        @if($cause->image)
                        <img src="{{ asset('storage/' . $cause->image) }}" alt="{{ $cause->title }}" class="w-full h-64 object-cover">
                        @endif
                        
                        <div class="p-6">
                            <h2 class="text-2xl font-bold mb-4 text-gov-navy font-hindi">{{ $cause->title }}</h2>
                            <p class="text-gray-600 mb-6">{{ $cause->description }}</p>
                            
                            <!-- Progress Bar -->
                            @php
                                $percentage = $cause->goal_amount > 0 ? min(($cause->raised_amount / $cause->goal_amount) * 100, 100) : 0;
                            @endphp
                            <div class="mb-6">
                                <div class="flex justify-between text-sm text-gray-600 mb-2">
                                    <span class="font-hindi">{{ app()->getLocale() === 'hi' ? 'जुटाई गई राशि' : 'Raised' }}</span>
                                    <span>{{ number_format($percentage, 1) }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="bg-gov-orange h-3 rounded-full transition-all duration-300" style="width: {{ $percentage }}%"></div>
                                </div>
                                <div class="flex justify-between text-lg font-semibold mt-2">
                                    <span class="text-gov-orange">₹{{ number_format($cause->raised_amount) }}</span>
                                    <span class="text-gray-600">₹{{ number_format($cause->goal_amount) }}</span>
                                </div>
                            </div>
                            
                            @if($cause->content)
                            <div class="prose max-w-none">
                                {!! $cause->content !!}
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Donation Form -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="bg-gov-blue text-white p-6">
                            <h3 class="text-2xl font-bold font-hindi">
                                {{ app()->getLocale() === 'hi' ? 'दान का विवरण' : 'Donation Details' }}
                            </h3>
                        </div>
                        
                        <div class="p-6 text-center">
                            <div class="mb-6">
                                <i class="fas fa-heart fa-3x text-gov-orange mb-4"></i>
                                <h3 class="text-xl font-bold text-gov-navy mb-2 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'इस कारण के लिए दान करें' : 'Support This Cause' }}
                                </h3>
                                <p class="text-gray-600 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'आपका योगदान इस महत्वपूर्ण कारण के लिए बहुत मायने रखता है।' : 'Your contribution makes a significant difference to this important cause.' }}
                                </p>
                            </div>

                            <!-- Quick Amount Buttons -->
                            <div class="grid grid-cols-2 gap-3 mb-4">
                                <button type="button" onclick="selectAmount(500)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹500
                                </button>
                                <button type="button" onclick="selectAmount(1000)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹1,000
                                </button>
                                <button type="button" onclick="selectAmount(2500)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹2,500
                                </button>
                                <button type="button" onclick="selectAmount(5000)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹5,000
                                </button>
                                <button type="button" onclick="enableCustomAmount()" class="amount-btn others-btn bg-gray-100 hover:bg-gov-orange hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-edit mr-1"></i>
                                    {{ app()->getLocale() === 'hi' ? 'अन्य राशि' : 'Others' }}
                                </button>
                            </div>

                            <!-- Custom Amount Input (Initially Hidden) -->
                            <div id="customAmountSection" class="custom-amount-section mb-4 hidden">
                                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                    <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                        <i class="fas fa-rupee-sign text-gov-orange mr-1"></i>
                                        {{ app()->getLocale() === 'hi' ? 'कस्टम राशि दर्ज करें' : 'Enter Custom Amount' }}
                                    </label>
                                    <div class="flex gap-2">
                                        <input type="number" id="customAmount" min="1" step="0.01"
                                               class="custom-amount-input flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-orange"
                                               placeholder="{{ app()->getLocale() === 'hi' ? '₹ राशि दर्ज करें' : '₹ Enter amount' }}">
                                        <button type="button" onclick="proceedWithCustomAmount({{ $cause->id }})"
                                                class="bg-gov-orange hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors font-bold">
                                            <i class="fas fa-arrow-right mr-1"></i>
                                            {{ app()->getLocale() === 'hi' ? 'आगे बढ़ें' : 'Proceed' }}
                                        </button>
                                    </div>
                                    <small class="text-gray-600 mt-1 block font-hindi">
                                        {{ app()->getLocale() === 'hi' ? 'न्यूनतम राशि ₹1' : 'Minimum amount ₹1' }}
                                    </small>
                                </div>
                            </div>

                            <!-- Donor Details Section -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                <h3 class="text-lg font-bold text-blue-800 mb-4 font-hindi">
                                    <i class="fas fa-user mr-2"></i>
                                    {{ app()->getLocale() === 'hi' ? 'दानकर्ता की जानकारी' : 'Donor Information' }}
                                </h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <!-- Donor Name -->
                                    <div>
                                        <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                            {{ app()->getLocale() === 'hi' ? 'पूरा नाम' : 'Full Name' }} *
                                        </label>
                                        <input type="text" name="donor_name" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                               placeholder="{{ app()->getLocale() === 'hi' ? 'अपना पूरा नाम दर्ज करें' : 'Enter your full name' }}">
                                    </div>

                                    <!-- Donor Email -->
                                    <div>
                                        <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                            {{ app()->getLocale() === 'hi' ? 'ईमेल पता' : 'Email Address' }} *
                                        </label>
                                        <input type="email" name="donor_email" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                               placeholder="{{ app()->getLocale() === 'hi' ? 'आपका ईमेल पता' : 'Your email address' }}">
                                    </div>

                                    <!-- Donor Phone -->
                                    <div>
                                        <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                            {{ app()->getLocale() === 'hi' ? 'मोबाइल नंबर' : 'Mobile Number' }} *
                                        </label>
                                        <input type="tel" name="donor_phone" required pattern="[0-9]{10}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                               placeholder="{{ app()->getLocale() === 'hi' ? '10 अंकों का मोबाइल नंबर' : '10-digit mobile number' }}">
                                    </div>

                                    <!-- Payment Method (Default QR) -->
                                    <div>
                                        <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                            {{ app()->getLocale() === 'hi' ? 'भुगतान विधि' : 'Payment Method' }}
                                        </label>
                                        <select name="payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue">
                                            <option value="qr_code" selected>
                                                {{ app()->getLocale() === 'hi' ? 'QR कोड स्कैन' : 'QR Code Scan' }}
                                            </option>
                                            <option value="bank_transfer">
                                                {{ app()->getLocale() === 'hi' ? 'बैंक ट्रांसफर' : 'Bank Transfer' }}
                                            </option>
                                            <option value="cash">
                                                {{ app()->getLocale() === 'hi' ? 'नकद' : 'Cash' }}
                                            </option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Optional Message -->
                                <div class="mt-4">
                                    <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                        {{ app()->getLocale() === 'hi' ? 'संदेश (वैकल्पिक)' : 'Message (Optional)' }}
                                    </label>
                                    <textarea name="message" rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                              placeholder="{{ app()->getLocale() === 'hi' ? 'कोई संदेश या टिप्पणी' : 'Any message or comment' }}"></textarea>
                                </div>

                                <!-- Anonymous Donation -->
                                <div class="mt-4">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="is_anonymous" value="1" class="mr-2">
                                        <span class="text-gray-700 font-hindi">
                                            {{ app()->getLocale() === 'hi' ? 'गुमनाम दान (नाम प्रकाशित न करें)' : 'Anonymous donation (do not publish name)' }}
                                        </span>
                                    </label>
                                </div>
                            </div>

                            <!-- Main Donate Button -->
                            <button type="button" onclick="openDonationModalFromForm({{ $cause->id }})" class="w-full bg-gov-orange hover:bg-orange-600 text-white font-bold py-4 px-6 rounded-lg transition-colors text-lg">
                                <i class="fas fa-heart mr-2"></i>
                                {{ app()->getLocale() === 'hi' ? 'अभी दान करें' : 'Donate Now' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Include Donation Modal -->
    @include('components.donation-modal')

    <script>
    function setAmount(amount) {
        document.querySelector('input[name="amount"]').value = amount;
    }

    // Global variable to store selected amount
    let selectedAmount = 0;

    function selectAmount(amount) {
        selectedAmount = amount;
        updateAmountButtonStates(amount);
        hideCustomAmountSection();
    }

    function enableCustomAmount() {
        const customSection = document.getElementById('customAmountSection');
        const customInput = document.getElementById('customAmount');

        // Show custom amount section with animation
        customSection.classList.remove('hidden');
        setTimeout(() => {
            customSection.classList.add('show');
        }, 10);

        // Focus on input after animation
        setTimeout(() => {
            customInput.focus();
        }, 300);

        // Update button states
        updateAmountButtonStates('custom');
        selectedAmount = 0; // Reset selected amount when custom is chosen
    }

    function openDonationModalFromForm(causeId) {
        // Validate donor details
        const donorName = document.querySelector('input[name="donor_name"]').value.trim();
        const donorEmail = document.querySelector('input[name="donor_email"]').value.trim();
        const donorPhone = document.querySelector('input[name="donor_phone"]').value.trim();

        if (!donorName) {
            Swal.fire({
                icon: 'warning',
                title: '{{ app()->getLocale() === 'hi' ? 'नाम आवश्यक है' : 'Name Required' }}',
                text: '{{ app()->getLocale() === 'hi' ? 'कृपया अपना नाम दर्ज करें' : 'Please enter your name' }}',
                confirmButtonColor: '#f97316'
            });
            document.querySelector('input[name="donor_name"]').focus();
            return;
        }

        if (!donorEmail) {
            Swal.fire({
                icon: 'warning',
                title: '{{ app()->getLocale() === 'hi' ? 'ईमेल आवश्यक है' : 'Email Required' }}',
                text: '{{ app()->getLocale() === 'hi' ? 'कृपया अपना ईमेल पता दर्ज करें' : 'Please enter your email address' }}',
                confirmButtonColor: '#f97316'
            });
            document.querySelector('input[name="donor_email"]').focus();
            return;
        }

        if (!donorPhone || donorPhone.length !== 10) {
            Swal.fire({
                icon: 'warning',
                title: '{{ app()->getLocale() === 'hi' ? 'मोबाइल नंबर आवश्यक है' : 'Mobile Number Required' }}',
                text: '{{ app()->getLocale() === 'hi' ? 'कृपया 10 अंकों का मोबाइल नंबर दर्ज करें' : 'Please enter a 10-digit mobile number' }}',
                confirmButtonColor: '#f97316'
            });
            document.querySelector('input[name="donor_phone"]').focus();
            return;
        }

        // Get amount - either from custom input or selected amount
        let amount = '';
        const customAmount = document.getElementById('customAmount').value;
        if (customAmount) {
            amount = customAmount;
        } else if (selectedAmount > 0) {
            amount = selectedAmount;
        }

        if (!amount || parseFloat(amount) <= 0) {
            Swal.fire({
                icon: 'warning',
                title: '{{ app()->getLocale() === 'hi' ? 'राशि आवश्यक है' : 'Amount Required' }}',
                text: '{{ app()->getLocale() === 'hi' ? 'कृपया एक वैध राशि चुनें या दर्ज करें' : 'Please select or enter a valid amount' }}',
                confirmButtonColor: '#f97316'
            });
            return;
        }

        // Open donation modal with all details
        openDonationModal(causeId, amount);
    }

    function hideCustomAmountSection() {
        const customSection = document.getElementById('customAmountSection');
        const customInput = document.getElementById('customAmount');

        customSection.classList.remove('show');
        setTimeout(() => {
            customSection.classList.add('hidden');
            customInput.value = '';
        }, 300);
    }

    function proceedWithCustomAmount(causeId) {
        const customAmount = document.getElementById('customAmount').value;

        if (!customAmount || parseFloat(customAmount) <= 0) {
            Swal.fire({
                icon: 'warning',
                title: '{{ app()->getLocale() === 'hi' ? 'अमान्य राशि' : 'Invalid Amount' }}',
                text: '{{ app()->getLocale() === 'hi' ? 'कृपया एक वैध राशि दर्ज करें' : 'Please enter a valid amount' }}',
                confirmButtonColor: '#f97316'
            });
            document.getElementById('customAmount').focus();
            return;
        }

        selectedAmount = parseFloat(customAmount);
        updateAmountButtonStates('custom');
    }

    function updateAmountButtonStates(selectedAmount) {
        const buttons = document.querySelectorAll('.amount-btn');
        buttons.forEach(button => {
            button.classList.remove('bg-gov-blue', 'text-white', 'bg-gov-orange', 'active');
            button.classList.add('bg-gray-100');
        });

        if (selectedAmount === 'custom') {
            const othersBtn = buttons[buttons.length - 1]; // Last button is "Others"
            othersBtn.classList.remove('bg-gray-100');
            othersBtn.classList.add('bg-gov-orange', 'text-white', 'active');
        } else {
            buttons.forEach(button => {
                const buttonText = button.textContent.trim();
                if (buttonText.includes(selectedAmount.toString())) {
                    button.classList.remove('bg-gray-100');
                    button.classList.add('bg-gov-blue', 'text-white', 'active');
                }
            });
        }
    }

    // Allow Enter key to proceed with custom amount
    document.addEventListener('DOMContentLoaded', function() {
        const customAmountInput = document.getElementById('customAmount');
        if (customAmountInput) {
            customAmountInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const causeId = {{ $cause->id }};
                    proceedWithCustomAmount(causeId);
                }
            });

            // Update selected amount when typing in custom input
            customAmountInput.addEventListener('input', function(e) {
                const value = parseFloat(e.target.value);
                if (value > 0) {
                    selectedAmount = value;
                } else {
                    selectedAmount = 0;
                }
            });
        }
    });
    </script>
</x-app-layout>
