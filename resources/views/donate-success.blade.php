<x-app-layout>
    @section('title', app()->getLocale() === 'hi' ? 'दान सफल - एनजीओ पोर्टल' : 'Donation Successful - NGO Portal')
    @section('meta_description', app()->getLocale() === 'hi' ? 'आपका दान सफलतापूर्वक पूरा हो गया है। धन्यवाद!' : 'Your donation has been completed successfully. Thank you!')

    <!-- Success Section -->
    <section class="py-20 bg-gradient-to-r from-green-400 to-green-600">
        <div class="container mx-auto px-4 text-center text-white">
            <!-- Success Icon -->
            <div class="mb-8">
                <div class="inline-flex items-center justify-center w-24 h-24 bg-white rounded-full">
                    <svg class="w-12 h-12 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
            </div>
            
            <h1 class="text-4xl md:text-6xl font-bold mb-6 font-hindi">
                {{ app()->getLocale() === 'hi' ? 'धन्यवाद!' : 'Thank You!' }}
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                {{ app()->getLocale() === 'hi' 
                    ? 'आपका दान सफलतापूर्वक पूरा हो गया है।'
                    : 'Your donation has been completed successfully.' }}
            </p>
        </div>
    </section>

    <!-- Donation Details -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="bg-gov-blue text-white p-6">
                        <h2 class="text-2xl font-bold font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'दान की रसीद' : 'Donation Receipt' }}
                        </h2>
                    </div>
                    
                    <div class="p-6">
                        <div class="grid gap-4">
                            <!-- Receipt Number -->
                            <div class="flex justify-between border-b pb-2">
                                <span class="font-semibold text-gray-700 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'रसीद संख्या:' : 'Receipt Number:' }}
                                </span>
                                <span class="text-gray-900">{{ $donation->receipt_number }}</span>
                            </div>
                            
                            <!-- Transaction ID -->
                            <div class="flex justify-between border-b pb-2">
                                <span class="font-semibold text-gray-700 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'लेनदेन आईडी:' : 'Transaction ID:' }}
                                </span>
                                <span class="text-gray-900">{{ $donation->transaction_id }}</span>
                            </div>
                            
                            <!-- Donor Name -->
                            @if(!$donation->is_anonymous)
                            <div class="flex justify-between border-b pb-2">
                                <span class="font-semibold text-gray-700 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'दाता का नाम:' : 'Donor Name:' }}
                                </span>
                                <span class="text-gray-900">{{ $donation->donor_name }}</span>
                            </div>
                            @endif
                            
                            <!-- Amount -->
                            <div class="flex justify-between border-b pb-2">
                                <span class="font-semibold text-gray-700 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'राशि:' : 'Amount:' }}
                                </span>
                                <span class="text-2xl font-bold text-gov-orange">₹{{ number_format($donation->amount, 2) }}</span>
                            </div>
                            
                            <!-- Cause -->
                            @if($donation->cause)
                            <div class="flex justify-between border-b pb-2">
                                <span class="font-semibold text-gray-700 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'कारण:' : 'Cause:' }}
                                </span>
                                <span class="text-gray-900">{{ $donation->cause->title }}</span>
                            </div>
                            @endif
                            
                            <!-- Payment Method -->
                            <div class="flex justify-between border-b pb-2">
                                <span class="font-semibold text-gray-700 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'भुगतान विधि:' : 'Payment Method:' }}
                                </span>
                                <span class="text-gray-900 capitalize">{{ $donation->payment_gateway }}</span>
                            </div>
                            
                            <!-- Date -->
                            <div class="flex justify-between border-b pb-2">
                                <span class="font-semibold text-gray-700 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'दिनांक:' : 'Date:' }}
                                </span>
                                <span class="text-gray-900">{{ $donation->donated_at->format('d M Y, h:i A') }}</span>
                            </div>
                            
                            <!-- Status -->
                            <div class="flex justify-between">
                                <span class="font-semibold text-gray-700 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'स्थिति:' : 'Status:' }}
                                </span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-semibold">
                                    {{ app()->getLocale() === 'hi' ? 'पूर्ण' : 'Completed' }}
                                </span>
                            </div>
                        </div>
                        
                        <!-- Message -->
                        @if($donation->message)
                        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                            <h4 class="font-semibold text-gray-700 mb-2 font-hindi">
                                {{ app()->getLocale() === 'hi' ? 'आपका संदेश:' : 'Your Message:' }}
                            </h4>
                            <p class="text-gray-600">{{ $donation->message }}</p>
                        </div>
                        @endif
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-8 text-center space-y-4">
                    <p class="text-gray-600 font-hindi">
                        {{ app()->getLocale() === 'hi' 
                            ? 'रसीद की एक प्रति आपके ईमेल पर भेजी गई है।'
                            : 'A copy of the receipt has been sent to your email.' }}
                    </p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('home') }}" 
                           class="bg-gov-blue hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'होम पेज पर जाएं' : 'Go to Home' }}
                        </a>
                        
                        <a href="{{ route('donate.index') }}" 
                           class="bg-gov-orange hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'फिर से दान करें' : 'Donate Again' }}
                        </a>
                        
                        <button onclick="window.print()" 
                                class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'प्रिंट करें' : 'Print Receipt' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Impact Message -->
    <section class="py-16 bg-gov-blue text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6 font-hindi">
                {{ app()->getLocale() === 'hi' ? 'आपका योगदान मायने रखता है' : 'Your Contribution Matters' }}
            </h2>
            <p class="text-xl mb-8 max-w-3xl mx-auto">
                {{ app()->getLocale() === 'hi' 
                    ? 'आपके दान से हमें अपने मिशन को आगे बढ़ाने में मदद मिलती है। आपका समर्थन हमारे लिए अमूल्य है।'
                    : 'Your donation helps us advance our mission. Your support is invaluable to us.' }}
            </p>
            
            <!-- Social Sharing -->
            <div class="flex justify-center space-x-4">
                <a href="#" class="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full transition-colors">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                    </svg>
                </a>
                <a href="#" class="bg-blue-800 hover:bg-blue-900 text-white p-3 rounded-full transition-colors">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                    </svg>
                </a>
            </div>
        </div>
    </section>
</x-app-layout>
