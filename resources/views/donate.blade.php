<x-app-layout>
    @section('title', app()->getLocale() === 'hi' ? 'दान करें - एनजीओ पोर्टल' : 'Donate - NGO Portal')
    @section('meta_description', app()->getLocale() === 'hi' ? 'आपका छोटा सा योगदान भी किसी के जीवन में बड़ा बदलाव ला सकता है। अभी दान करें।' : 'Your small contribution can make a big difference in someone\'s life. Donate now.')

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="relative container mx-auto px-4 text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 font-hindi">
                {{ app()->getLocale() === 'hi' ? 'दान करें' : 'Make a Donation' }}
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                {{ app()->getLocale() === 'hi' 
                    ? 'आपका छोटा सा योगदान भी किसी के जीवन में बड़ा बदलाव ला सकता है।'
                    : 'Your small contribution can make a big difference in someone\'s life.' }}
            </p>
        </div>
    </section>

    <!-- Donation Form Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="bg-gov-blue text-white p-6">
                        <h2 class="text-2xl font-bold font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'दान का विवरण' : 'Donation Details' }}
                        </h2>
                    </div>
                    
                    <form action="{{ route('donate.process') }}" method="POST" class="p-6" onsubmit="handleMainDonateSubmit(event)">
                        @csrf
                        
                        <!-- Amount Selection -->
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                {{ app()->getLocale() === 'hi' ? 'दान की राशि (₹)' : 'Donation Amount (₹)' }}
                            </label>
                            
                            <!-- Quick Amount Buttons -->
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3 mb-4">
                                <button type="button" onclick="setAmount(500)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹500
                                </button>
                                <button type="button" onclick="setAmount(1000)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹1,000
                                </button>
                                <button type="button" onclick="setAmount(2500)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹2,500
                                </button>
                                <button type="button" onclick="setAmount(5000)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹5,000
                                </button>
                                <button type="button" onclick="enableCustomAmount()" class="amount-btn others-btn bg-gray-100 hover:bg-gov-orange hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-edit mr-1"></i>
                                    {{ app()->getLocale() === 'hi' ? 'अन्य राशि' : 'Others' }}
                                </button>
                            </div>
                            
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
                                <input type="number" name="amount" id="amount" min="1" step="0.01" required
                                       class="custom-amount-input w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="{{ app()->getLocale() === 'hi' ? 'कस्टम राशि दर्ज करें' : 'Enter custom amount' }}">
                            </div>
                        </div>

                        <!-- Donor Information -->
                        <div class="grid md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'पूरा नाम' : 'Full Name' }} *
                                </label>
                                <input type="text" name="donor_name" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="{{ app()->getLocale() === 'hi' ? 'आपका नाम' : 'Your Name' }}">
                            </div>
                            
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'ईमेल पता' : 'Email Address' }} *
                                </label>
                                <input type="email" name="donor_email" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="{{ app()->getLocale() === 'hi' ? 'आपका ईमेल' : 'Your Email' }}">
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'मोबाइल नंबर' : 'Mobile Number' }} *
                                </label>
                                <input type="tel" name="donor_phone" required pattern="[0-9]{10}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="{{ app()->getLocale() === 'hi' ? '10 अंकों का मोबाइल नंबर' : '10-digit mobile number' }}">
                            </div>

                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'भुगतान विधि' : 'Payment Method' }}
                                </label>
                                <select name="payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue">
                                    <option value="qr_code" selected>
                                        {{ app()->getLocale() === 'hi' ? 'QR कोड स्कैन' : 'QR Code Scan' }}
                                    </option>
                                    <option value="bank_transfer">
                                        {{ app()->getLocale() === 'hi' ? 'बैंक ट्रांसफर' : 'Bank Transfer' }}
                                    </option>
                                    <option value="cash">
                                        {{ app()->getLocale() === 'hi' ? 'नकद' : 'Cash' }}
                                    </option>
                                </select>
                            </div>
                        </div>

                        <!-- Message -->
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                {{ app()->getLocale() === 'hi' ? 'संदेश (वैकल्पिक)' : 'Message (Optional)' }}
                            </label>
                            <textarea name="message" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                      placeholder="{{ app()->getLocale() === 'hi' ? 'आपका संदेश' : 'Your message' }}"></textarea>
                        </div>

                        <!-- Anonymous Donation -->
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_anonymous" value="1" class="mr-2">
                                <span class="text-gray-700 font-hindi">
                                    {{ app()->getLocale() === 'hi' ? 'गुमनाम दान (आपका नाम सार्वजनिक नहीं किया जाएगा)' : 'Anonymous donation (your name will not be published)' }}
                                </span>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <button type="button" onclick="openDonationModalFromForm()" class="w-full bg-gov-orange hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'अभी दान करें' : 'Donate Now' }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Causes -->
    @if($featuredCauses->count() > 0)
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-12 text-gov-navy font-hindi">
                {{ app()->getLocale() === 'hi' ? 'विशेष कारण' : 'Featured Causes' }}
            </h2>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($featuredCauses as $cause)
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    @if($cause->image)
                    <img src="{{ asset('storage/' . $cause->image) }}" alt="{{ $cause->title }}" class="w-full h-48 object-cover">
                    @endif
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2 text-gov-navy font-hindi">{{ $cause->title }}</h3>
                        <p class="text-gray-600 mb-4">{{ Str::limit($cause->description, 100) }}</p>
                        
                        <!-- Progress Bar -->
                        @php
                            $percentage = $cause->goal_amount > 0 ? min(($cause->raised_amount / $cause->goal_amount) * 100, 100) : 0;
                        @endphp
                        <div class="mb-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>{{ app()->getLocale() === 'hi' ? 'जुटाई गई राशि' : 'Raised' }}: ₹{{ number_format($cause->raised_amount) }}</span>
                                <span>{{ number_format($percentage, 1) }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gov-orange h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">
                                {{ app()->getLocale() === 'hi' ? 'लक्ष्य' : 'Goal' }}: ₹{{ number_format($cause->goal_amount) }}
                            </div>
                        </div>
                        
                        <button onclick="openDonationModal({{ $cause->id }})"
                                class="w-full bg-secondary hover:bg-secondary text-white text-center py-2 px-4 rounded-lg transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'इस कारण के लिए दान करें' : 'Donate to this Cause' }}
                        </button>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Include Donation Modal -->
    @include('components.donation-modal')

    <script>
    function setAmount(amount) {
        const amountInput = document.querySelector('input[name="amount"]');
        if (amountInput) {
            amountInput.value = amount;
            amountInput.readOnly = true;
            amountInput.style.backgroundColor = '#f3f4f6';
        }

        // Update button states
        updateAmountButtonStates(amount);
    }

    function enableCustomAmount() {
        const amountInput = document.querySelector('input[name="amount"]');
        if (amountInput) {
            amountInput.value = '';
            amountInput.readOnly = false;
            amountInput.style.backgroundColor = 'white';
            amountInput.focus();
            amountInput.placeholder = '{{ app()->getLocale() === 'hi' ? 'कस्टम राशि दर्ज करें' : 'Enter custom amount' }}';
        }

        // Update button states
        updateAmountButtonStates('custom');
    }

    function updateAmountButtonStates(selectedAmount) {
        const buttons = document.querySelectorAll('.amount-btn');
        buttons.forEach(button => {
            button.classList.remove('bg-gov-blue', 'text-white', 'bg-gov-orange', 'active');
            button.classList.add('bg-gray-100');
        });

        if (selectedAmount === 'custom') {
            const othersBtn = buttons[buttons.length - 1]; // Last button is "Others"
            othersBtn.classList.remove('bg-gray-100');
            othersBtn.classList.add('bg-gov-orange', 'text-white', 'active');
        } else {
            buttons.forEach(button => {
                const buttonText = button.textContent.trim();
                if (buttonText.includes(selectedAmount.toString())) {
                    button.classList.remove('bg-gray-100');
                    button.classList.add('bg-gov-blue', 'text-white', 'active');
                }
            });
        }
    }

    // Allow manual input to enable custom mode
    document.addEventListener('DOMContentLoaded', function() {
        const amountInput = document.querySelector('input[name="amount"]');
        if (amountInput) {
            amountInput.addEventListener('input', function() {
                if (!this.readOnly) {
                    updateAmountButtonStates('custom');
                }
            });
        }
    });

    function openDonationModalWithAmount(causeId, amount) {
        openDonationModal(causeId, amount);
    }

    function openDonationModalFromForm(causeId = null) {
        // Get amount from the main form
        const amountInput = document.querySelector('input[name="amount"]');
        const amount = amountInput ? amountInput.value : '';

        // Validate amount
        if (!amount || parseFloat(amount) <= 0) {
            alert('{{ app()->getLocale() === 'hi' ? 'कृपया एक वैध राशि दर्ज करें' : 'Please enter a valid amount' }}');
            if (amountInput) amountInput.focus();
            return;
        }

        openDonationModal(causeId, amount);
    }

    // Handle main donate form submission with SweetAlert
    function handleMainDonateSubmit(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);
        const submitButton = form.querySelector('button[type="submit"]');

        // Basic validation
        const amount = formData.get('amount');
        const donorName = formData.get('donor_name');
        const donorEmail = formData.get('donor_email');
        const donorPhone = formData.get('donor_phone');

        if (!amount || parseFloat(amount) <= 0) {
            Swal.fire({
                icon: 'warning',
                title: '{{ app()->getLocale() === 'hi' ? 'राशि आवश्यक है' : 'Amount Required' }}',
                text: '{{ app()->getLocale() === 'hi' ? 'कृपया एक वैध राशि दर्ज करें' : 'Please enter a valid amount' }}',
                confirmButtonColor: '#f97316'
            });
            return;
        }

        if (!donorName.trim()) {
            Swal.fire({
                icon: 'warning',
                title: '{{ app()->getLocale() === 'hi' ? 'नाम आवश्यक है' : 'Name Required' }}',
                text: '{{ app()->getLocale() === 'hi' ? 'कृपया अपना नाम दर्ज करें' : 'Please enter your name' }}',
                confirmButtonColor: '#f97316'
            });
            return;
        }

        if (!donorEmail.trim()) {
            Swal.fire({
                icon: 'warning',
                title: '{{ app()->getLocale() === 'hi' ? 'ईमेल आवश्यक है' : 'Email Required' }}',
                text: '{{ app()->getLocale() === 'hi' ? 'कृपया अपना ईमेल पता दर्ज करें' : 'Please enter your email address' }}',
                confirmButtonColor: '#f97316'
            });
            return;
        }

        if (!donorPhone.trim() || donorPhone.length !== 10) {
            Swal.fire({
                icon: 'warning',
                title: '{{ app()->getLocale() === 'hi' ? 'मोबाइल नंबर आवश्यक है' : 'Mobile Number Required' }}',
                text: '{{ app()->getLocale() === 'hi' ? 'कृपया 10 अंकों का मोबाइल नंबर दर्ज करें' : 'Please enter a 10-digit mobile number' }}',
                confirmButtonColor: '#f97316'
            });
            return;
        }

        // Disable submit button and show loading
        submitButton.disabled = true;
        submitButton.innerHTML = '{{ app()->getLocale() === 'hi' ? '<i class="fas fa-spinner fa-spin mr-2"></i>जमा हो रहा है...' : '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting...' }}';

        // Submit form via fetch
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: '{{ app()->getLocale() === 'hi' ? 'धन्यवाद!' : 'Thank You!' }}',
                    text: data.message || '{{ app()->getLocale() === 'hi' ? 'आपका दान सफलतापूर्वक जमा हो गया है।' : 'Your donation has been submitted successfully.' }}',
                    confirmButtonColor: '#f97316',
                    confirmButtonText: '{{ app()->getLocale() === 'hi' ? 'ठीक है' : 'OK' }}'
                }).then(() => {
                    // Optionally redirect or refresh
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        window.location.reload();
                    }
                });
            } else {
                throw new Error(data.message || 'Submission failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: '{{ app()->getLocale() === 'hi' ? 'त्रुटि!' : 'Error!' }}',
                text: error.message || '{{ app()->getLocale() === 'hi' ? 'दान जमा करने में त्रुटि हुई। कृपया पुनः प्रयास करें।' : 'There was an error submitting your donation. Please try again.' }}',
                confirmButtonColor: '#f97316',
                confirmButtonText: '{{ app()->getLocale() === 'hi' ? 'ठीक है' : 'OK' }}'
            });
        })
        .finally(() => {
            // Re-enable submit button
            submitButton.disabled = false;
            submitButton.innerHTML = '{{ app()->getLocale() === 'hi' ? '<i class="fas fa-heart mr-2"></i>दान करें' : '<i class="fas fa-heart mr-2"></i>Donate Now' }}';
        });
    }
    </script>
</x-app-layout>
