<x-app-layout>
    @push('styles')
<style>
    /* Gallery image modal styles */
    .gallery-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
    }

    .gallery-modal.active {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .gallery-modal-content {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    }

    .gallery-modal-close {
        position: absolute;
        top: 20px;
        right: 30px;
        color: white;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
        z-index: 1001;
    }

    .gallery-modal-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        font-size: 24px;
        padding: 10px 15px;
        cursor: pointer;
        border-radius: 50%;
        transition: background-color 0.3s;
    }

    .gallery-modal-nav:hover {
        background: rgba(255, 255, 255, 0.4);
    }

    .gallery-modal-prev {
        left: 20px;
    }

    .gallery-modal-next {
        right: 20px;
    }

    /* Gallery grid styles */
    .gallery-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .gallery-item {
        aspect-ratio: 1;
        overflow: hidden;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: transform 0.3s ease;
    }

    .gallery-item:hover {
        transform: scale(1.05);
    }

    .gallery-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
</style>
@endpush
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-16">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <nav class="text-blue-200 mb-4">
                    <a href="{{ route('home') }}" class="hover:text-white">{{ app()->getLocale() === 'hi' ? 'होम' : 'Home' }}</a>
                    <span class="mx-2">/</span>
                    <a href="{{ route('gallery.index') }}" class="hover:text-white">{{ app()->getLocale() === 'hi' ? 'गैलरी' : 'Gallery' }}</a>
                    <span class="mx-2">/</span>
                    <span>{{ $album->name }}</span>
                </nav>
                
                <h1 class="text-4xl md:text-5xl font-bold mb-4 font-hindi">
                    {{ $album->name }}
                </h1>
                
                @if($album->description)
                    <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                        {{ $album->description }}
                    </p>
                @endif
                
                @if($album->event_date)
                    <div class="mt-4 flex items-center justify-center text-blue-200">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        <span>{{ $album->event_date->format('M d, Y') }}</span>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Gallery Images -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            @if($album->items && $album->items->count() > 0)
                <div class="gallery-grid">
                    @foreach($album->items as $index => $item)
                        <div class="gallery-item" onclick="openModal({{ $index }})">
                            <img src="{{ asset('storage/' . $item->file_path) }}"
                                 alt="{{ $item->title ?? $album->name }}"
                                 loading="lazy">
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                        {{ app()->getLocale() === 'hi' ? 'कोई तस्वीरें नहीं मिलीं' : 'No images found' }}
                    </h3>
                    <p class="text-gray-600">
                        {{ app()->getLocale() === 'hi' ? 'इस एल्बम में अभी तक कोई तस्वीरें नहीं हैं।' : 'This album doesn\'t have any images yet.' }}
                    </p>
                </div>
            @endif
        </div>
    </section>

    <!-- Modal for viewing images -->
    <div id="galleryModal" class="gallery-modal">
        <span class="gallery-modal-close" onclick="closeModal()">&times;</span>
        <button class="gallery-modal-nav gallery-modal-prev" onclick="changeImage(-1)">&#10094;</button>
        <img id="modalImage" class="gallery-modal-content" src="" alt="">
        <button class="gallery-modal-nav gallery-modal-next" onclick="changeImage(1)">&#10095;</button>
    </div>

    <!-- Back to Gallery -->
    <section class="bg-gray-50 py-8">
        <div class="container mx-auto px-4 text-center">
            <a href="{{ route('gallery.index') }}" class="bg-gov-navy hover:bg-blue-900 text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                {{ app()->getLocale() === 'hi' ? 'गैलरी पर वापस जाएं' : 'Back to Gallery' }}
            </a>
        </div>
    </section>

@push('scripts')
<script>
    let currentImageIndex = 0;
    const images = @json($album->items ? $album->items->pluck('file_path')->toArray() : []);
    const titles = @json($album->items ? $album->items->pluck('title')->toArray() : []);

    function openModal(index) {
        currentImageIndex = index;
        const modal = document.getElementById('galleryModal');
        const modalImage = document.getElementById('modalImage');
        
        modal.classList.add('active');
        modalImage.src = '{{ asset("storage/") }}/' + images[index];
        modalImage.alt = titles[index] || '{{ $album->name }}';
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    function closeModal() {
        const modal = document.getElementById('galleryModal');
        modal.classList.remove('active');
        
        // Restore body scroll
        document.body.style.overflow = 'auto';
    }

    function changeImage(direction) {
        currentImageIndex += direction;
        
        if (currentImageIndex >= images.length) {
            currentImageIndex = 0;
        } else if (currentImageIndex < 0) {
            currentImageIndex = images.length - 1;
        }
        
        const modalImage = document.getElementById('modalImage');
        modalImage.src = '{{ asset("storage/") }}/' + images[currentImageIndex];
        modalImage.alt = titles[currentImageIndex] || '{{ $album->name }}';
    }

    // Close modal when clicking outside the image
    document.getElementById('galleryModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        } else if (e.key === 'ArrowLeft') {
            changeImage(-1);
        } else if (e.key === 'ArrowRight') {
            changeImage(1);
        }
    });
</script>
@endpush
</x-app-layout>
