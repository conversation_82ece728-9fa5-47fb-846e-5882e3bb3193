<x-app-layout>
    @push('styles')
    <style>
        /* Gallery cover image fallback styling */
        .gallery-cover-image[data-is-fallback="true"] {
            object-fit: contain !important;
            object-position: center !important;
            padding: 1.5rem !important;
            background-color: #f8fafc !important;
            max-width: 50% !important;
            max-height: 50% !important;
            width: auto !important;
            height: auto !important;
            margin: auto !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
        }
    </style>
    @endpush
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-16">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4 font-hindi">
                    {{ app()->getLocale() === 'hi' ? 'गैलरी' : 'Gallery' }}
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                    {{ app()->getLocale() === 'hi' ? 'हमारे कार्यक्रमों और गतिविधियों की तस्वीरें और वीडियो देखें।' : 'View photos and videos from our programs and activities.' }}
                </p>
            </div>
        </div>
    </section>



    <!-- Gallery Albums -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            @if($albums->count() > 0)
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($albums as $album)
                        <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
                            <div class="aspect-video bg-gray-200 relative overflow-hidden">
                                <img src="{{ $album->cover_image_url }}"
                                     alt="{{ $album->name }}"
                                     class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 gallery-cover-image"
                                     data-is-fallback="{{ $album->cover_image ? 'false' : 'true' }}">
                                            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>

                                
                                <!-- Overlay -->
                                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                                    <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">
                                    <a href="{{ route('gallery.show', $album->slug) }}" class="hover:text-gov-orange transition-colors">
                                        {{ $album->name }}
                                    </a>
                                </h3>
                                
                                @if($album->description)
                                    <p class="text-gray-600 mb-4 line-clamp-3">
                                        {{ $album->description }}
                                    </p>
                                @endif
                                
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    @if($album->event_date)
                                        <span>{{ $album->event_date->format('M d, Y') }}</span>
                                    @endif
                                    <span>{{ $album->items_count ?? 0 }} {{ app()->getLocale() === 'hi' ? 'आइटम' : 'items' }}</span>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $albums->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                        {{ app()->getLocale() === 'hi' ? 'कोई गैलरी एल्बम नहीं मिला' : 'No gallery albums found' }}
                    </h3>
                    <p class="text-gray-600">
                        {{ app()->getLocale() === 'hi' ? 'कृपया अपने खोज मानदंड को समायोजित करें या बाद में वापस जांचें।' : 'Please adjust your search criteria or check back later.' }}
                    </p>
                </div>
            @endif
        </div>
    </section>


</x-app-layout>
