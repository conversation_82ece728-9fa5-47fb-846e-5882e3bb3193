<x-app-layout>
    @push('styles')
    <style>
        /* Gallery cover image fallback styling */
        .gallery-cover-image[data-is-fallback="true"] {
            object-fit: contain !important;
            object-position: center !important;
            padding: 1.5rem !important;
            background-color: #f8fafc !important;
            max-width: 50% !important;
            max-height: 50% !important;
            width: auto !important;
            height: auto !important;
            margin: auto !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
        }
    </style>
    @endpush

    <!-- Hero Section with Image Slider -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-20 overflow-hidden">
        <!-- Image Slider Background -->
        <div class="absolute inset-0">
            <div class="hero-slider relative w-full h-full">
                @if($sliders->count() > 0)
                    @foreach($sliders as $index => $slider)
                        <div class="slide {{ $index === 0 ? 'active' : '' }} absolute inset-0 bg-cover bg-center transition-opacity duration-1000 {{ $index === 0 ? '' : 'opacity-0' }}" style="background-image: url('{{ $slider->image_url }}')">
                            <div class="absolute inset-0 bg-black opacity-50"></div>
                        </div>
                    @endforeach
                @else
                    <!-- Fallback slide if no sliders in database -->
                    <div class="slide active absolute inset-0 bg-cover bg-center transition-opacity duration-1000" style="background-image: url('{{ asset('images/hero-bg.jpg') }}')">
                        <div class="absolute inset-0 bg-black opacity-50"></div>
                    </div>
                @endif

                @if($sliders->count() > 1)
                    <!-- Navigation Arrows -->
                    <button class="slider-prev absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300 z-10">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>

                    <button class="slider-next absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300 z-10">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>

                    <!-- Slide Indicators -->
                    <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                        @foreach($sliders as $index => $slider)
                            <button class="indicator {{ $index === 0 ? 'active' : '' }} w-3 h-3 bg-white {{ $index === 0 ? '' : 'bg-opacity-50' }} rounded-full transition-all duration-300" data-slide="{{ $index }}"></button>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>

        <div class="relative container mx-auto px-4 text-center z-20">
            <div class="max-w-4xl mx-auto">
                @if($sliders->count() > 0)
                    @php $firstSlider = $sliders->first(); @endphp
                    <h1 class="text-4xl md:text-6xl font-bold mb-6 font-hindi translate-content" lang="hi">
                        {{ $firstSlider->title }}
                    </h1>
                    <p class="text-xl md:text-2xl mb-8 font-hindi opacity-90 translate-content" lang="hi">
                        {{ $firstSlider->description }}
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('causes.index') }}" class="bg-gov-orange hover:bg-orange-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors inline-flex items-center justify-center">
                            <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"></path>
                            </svg>
                            {{ app()->getLocale() === 'hi' ? 'अभी दान करें' : 'Donate Now' }}
                        </a>
                        <a href="{{ route('about') }}" class="border-2 border-white text-white hover:bg-white hover:text-gov-navy px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'हमारे बारे में जानें' : 'Learn About Us' }}
                        </a>
                    </div>
                @else
                    <!-- Fallback content -->
                    <h1 class="text-4xl md:text-6xl font-bold mb-6 font-hindi">
                        @if(app()->getLocale() === 'hi')
                            समाज में बदलाव लाएं
                        @else
                            Making a Difference
                        @endif
                    </h1>
                    <p class="text-xl md:text-2xl mb-8 font-hindi opacity-90">
                        @if(app()->getLocale() === 'hi')
                            हमारे साथ मिलकर एक बेहतर कल का निर्माण करें। आपका छोटा सा योगदान भी किसी के जीवन में बड़ा बदलाव ला सकता है।
                        @else
                            Together, let's build a better tomorrow. Your small contribution can make a big difference in someone's life.
                        @endif
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('donate.index') }}" class="bg-gov-orange hover:bg-orange-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors inline-flex items-center justify-center">
                            <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"></path>
                            </svg>
                            {{ app()->getLocale() === 'hi' ? 'अभी दान करें' : 'Donate Now' }}
                        </a>
                        <a href="{{ route('about') }}" class="border-2 border-white text-white hover:bg-white hover:text-gov-navy px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
                            {{ app()->getLocale() === 'hi' ? 'हमारे बारे में जानें' : 'Learn About Us' }}
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Slider JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('.slide');
            const indicators = document.querySelectorAll('.indicator');
            const prevBtn = document.querySelector('.slider-prev');
            const nextBtn = document.querySelector('.slider-next');
            let currentSlide = 0;
            const totalSlides = slides.length;

            function showSlide(index) {
                // Hide all slides
                slides.forEach(slide => {
                    slide.classList.remove('active');
                    slide.style.opacity = '0';
                });

                // Hide all indicators
                indicators.forEach(indicator => {
                    indicator.classList.remove('active');
                    indicator.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
                });

                // Show current slide
                slides[index].classList.add('active');
                slides[index].style.opacity = '1';

                // Show current indicator
                indicators[index].classList.add('active');
                indicators[index].style.backgroundColor = 'rgba(255, 255, 255, 1)';

                currentSlide = index;
            }

            function nextSlide() {
                const next = (currentSlide + 1) % totalSlides;
                showSlide(next);
            }

            function prevSlide() {
                const prev = (currentSlide - 1 + totalSlides) % totalSlides;
                showSlide(prev);
            }

            // Event listeners
            nextBtn.addEventListener('click', nextSlide);
            prevBtn.addEventListener('click', prevSlide);

            // Indicator clicks
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => showSlide(index));
            });

            // Auto-slide every 5 seconds
            setInterval(nextSlide, 5000);
        });
    </script>

    <!-- Statistics Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="bg-gov-orange text-white w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"></path>
                            <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-3xl font-bold text-gov-navy mb-2">₹{{ number_format($totalDonations, 0) }}</h3>
                    <p class="text-gray-600 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'कुल दान राशि' : 'Total Donations' }}
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="bg-gov-green text-white w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-3xl font-bold text-gov-navy mb-2">{{ number_format($totalDonors) }}</h3>
                    <p class="text-gray-600 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'दानदाता' : 'Donors' }}
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="bg-gov-blue text-white w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-3xl font-bold text-gov-navy mb-2">{{ $activeCauses }}</h3>
                    <p class="text-gray-600 font-hindi">
                        {{ app()->getLocale() === 'hi' ? 'सक्रिय कारण' : 'Active Causes' }}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Causes Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gov-navy mb-4 font-hindi">
                    {{ app()->getLocale() === 'hi' ? 'कारण' : 'Causes' }}
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto font-hindi">
                    {{ app()->getLocale() === 'hi' 
                        ? 'हमारे वर्तमान अभियानों में योगदान दें और समाज में सकारात्मक बदलाव लाने में हमारी मदद करें।'
                        : 'Contribute to our current campaigns and help us bring positive change to society.' }}
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @forelse($featuredCauses as $cause)
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow flex flex-col h-full">
                        <!-- Image -->
                        <div class="relative h-48 flex-shrink-0">
                            <img src="{{ $cause->featured_image_url }}" alt="{{ $cause->title }}" class="w-full h-full object-cover">
                            <div class="absolute top-4 right-4 bg-gov-orange text-white px-3 py-1 rounded-full text-sm font-semibold">
                                {{ number_format($cause->progress_percentage, 0) }}%
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="p-6 flex flex-col flex-grow">
                            <!-- Title - Fixed Height -->
                            <h3 class="text-xl font-bold text-gov-navy mb-3 font-hindi min-h-[3.5rem] line-clamp-2 flex-shrink-0 translate-content" lang="hi">
                                {{ $cause->title }}
                            </h3>

                            <!-- Description - Fixed Height -->
                            <p class="text-gray-600 mb-6 font-hindi line-clamp-3 min-h-[4.5rem] flex-shrink-0 translate-content" lang="hi">
                                {{ Str::limit($cause->short_description, 120) }}
                            </p>

                            <!-- Progress Section - Consistent Spacing -->
                            <div class="mb-6 flex-shrink-0">
                                <!-- Progress Bar -->
                                <div class="mb-4">
                                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                                        <span>{{ app()->getLocale() === 'hi' ? 'प्रगति' : 'Progress' }}</span>
                                        <span>{{ number_format($cause->progress_percentage, 1) }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-gov-green h-2 rounded-full transition-all duration-300"
                                             style="width: {{ min(100, $cause->progress_percentage) }}%"></div>
                                    </div>
                                </div>

                                <!-- Amount Info -->
                                <div class="flex justify-between text-sm">
                                    <div>
                                        <div class="text-gray-500 mb-1">
                                            {{ app()->getLocale() === 'hi' ? 'संग्रहित' : 'Raised' }}
                                        </div>
                                        <div class="font-semibold text-gov-blue">
                                            {{ $cause->formatted_raised_amount }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-gray-500 mb-1">
                                            {{ app()->getLocale() === 'hi' ? 'लक्ष्य' : 'Goal' }}
                                        </div>
                                        <div class="font-semibold text-gov-blue">
                                            {{ $cause->formatted_goal_amount }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Spacer to push buttons to bottom -->
                            <div class="flex-grow"></div>

                            <!-- Action Buttons - Fixed at Bottom -->
                            <div class="flex gap-2 flex-shrink-0">
                                <a href="{{ route('causes.show', $cause->slug) }}"
                                   class="flex-1 bg-gov-blue hover:bg-blue-700 text-white text-center py-2.5 px-4 rounded-lg transition-colors font-medium">
                                    {{ app()->getLocale() === 'hi' ? 'विवरण देखें' : 'View Details' }}
                                </a>
                                <a href="{{ route('donate.cause', $cause->slug) }}"
                                   class="flex-1 bg-gov-orange hover:bg-orange-600 text-white text-center py-2.5 px-4 rounded-lg transition-colors font-medium">
                                    {{ app()->getLocale() === 'hi' ? 'दान करें' : 'Donate Now' }}
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <p class="text-gray-500 font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'कोई कारण उपलब्ध नहीं है।' : 'No causes available.' }}
                        </p>
                    </div>
                @endforelse
            </div>

            <div class="text-center mt-12">
                <a href="{{ route('causes.index') }}" class="bg-gov-navy hover:bg-blue-900 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    {{ app()->getLocale() === 'hi' ? 'सभी कारण देखें' : 'View All Causes' }}
                </a>
            </div>
        </div>
    </section>

    <!-- Recent Blogs Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gov-navy mb-4 font-hindi">
                    {{ app()->getLocale() === 'hi' ? 'ब्लॉग' : 'Blogs' }}
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto font-hindi">
                    {{ app()->getLocale() === 'hi'
                        ? 'हमारे कार्यों और उपलब्धियों के बारे में अपडेट पढ़ें।'
                        : 'Read updates about our work and achievements.' }}
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @forelse($recentBlogs as $blog)
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative">
                            <img src="{{ $blog->featured_image_url }}" alt="{{ $blog->title }}" class="w-full h-48 object-cover">
                            <div class="absolute top-4 left-4 bg-{{ $blog->category->color ?? 'gov-blue' }} text-white px-3 py-1 rounded-full text-sm">
                                {{ $blog->category->name }}
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <div class="flex items-center text-sm text-gray-500 mb-2">
                                <span>{{ $blog->formatted_published_date }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ $blog->reading_time }}</span>
                            </div>
                            
                            <h3 class="text-xl font-bold text-gov-navy mb-2 font-hindi" lang="hi">
                                <a href="{{ route('blog.show', $blog->slug) }}" class="hover:text-gov-orange transition-colors">
                                    {{ $blog->title }}
                                </a>
                            </h3>

                            <p class="text-gray-600 mb-4 font-hindi" lang="hi">{{ Str::limit($blog->excerpt, 120) }}</p>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <img src="{{ $blog->author->avatar_url }}" alt="{{ $blog->author->name }}" class="w-8 h-8 rounded-full mr-2">
                                    <span class="text-sm text-gray-600">{{ $blog->author->name }}</span>
                                </div>
                                
                                <a href="{{ route('blog.show', $blog->slug) }}" class="text-gov-orange hover:text-orange-600 font-semibold text-sm">
                                    {{ app()->getLocale() === 'hi' ? 'पढ़ें →' : 'Read More →' }}
                                </a>
                            </div>
                        </div>
                    </article>
                @empty
                    <div class="col-span-full text-center py-12">
                        <p class="text-gray-500 font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'कोई ब्लॉग उपलब्ध नहीं है।' : 'No blogs available.' }}
                        </p>
                    </div>
                @endforelse
            </div>

            <div class="text-center mt-12">
                <a href="{{ route('blog.index') }}" class="bg-gov-navy hover:bg-blue-900 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    {{ app()->getLocale() === 'hi' ? 'सभी ब्लॉग देखें' : 'View All Blogs' }}
                </a>
            </div>
        </div>
    </section>

    <!-- Recent Gallery Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gov-navy mb-4 font-hindi">
                    {{ app()->getLocale() === 'hi' ? 'गैलरी' : 'Gallery' }}
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto font-hindi">
                    {{ app()->getLocale() === 'hi'
                        ? 'हमारे कार्यक्रमों और गतिविधियों की तस्वीरें देखें।'
                        : 'View photos from our programs and activities.' }}
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @forelse($recentGalleries as $gallery)
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow group">
                        <div class="relative h-48 flex-shrink-0">
                            <img src="{{ $gallery->cover_image_url }}" alt="{{ $gallery->name }}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 gallery-cover-image" data-is-fallback="{{ $gallery->cover_image ? 'false' : 'true' }}">
                            <div class="absolute top-4 right-4 bg-gov-blue text-white px-3 py-1 rounded-full text-sm font-semibold">
                                {{ $gallery->items_count ?? 0 }} {{ app()->getLocale() === 'hi' ? 'फोटो' : 'Photos' }}
                            </div>
                        </div>

                        <div class="p-6">
                            @if($gallery->event_date)
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span>{{ $gallery->formatted_event_date }}</span>
                                </div>
                            @endif

                            <h3 class="text-xl font-bold text-gov-navy mb-2 font-hindi" lang="hi">
                                <a href="{{ route('gallery.show', $gallery->slug) }}" class="hover:text-gov-orange transition-colors">
                                    {{ $gallery->name }}
                                </a>
                            </h3>

                            @if($gallery->description)
                                <p class="text-gray-600 mb-4 font-hindi line-clamp-3" lang="hi">{{ Str::limit($gallery->description, 120) }}</p>
                            @endif

                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span>{{ $gallery->items_count ?? 0 }} {{ app()->getLocale() === 'hi' ? 'आइटम' : 'items' }}</span>
                                </div>

                                <a href="{{ route('gallery.show', ['locale' => app()->getLocale(), 'album' => $gallery->slug]) }}" class="text-gov-orange hover:text-orange-600 font-semibold text-sm">
                                    {{ app()->getLocale() === 'hi' ? 'देखें →' : 'View →' }}
                                </a>
                            </div>
                        </div>
                    </article>
                @empty
                    <div class="col-span-full text-center py-12">
                        <p class="text-gray-500 font-hindi">
                            {{ app()->getLocale() === 'hi' ? 'कोई गैलरी उपलब्ध नहीं है।' : 'No gallery available.' }}
                        </p>
                    </div>
                @endforelse
            </div>

            <div class="text-center mt-12">
                <a href="{{ route('gallery.index') }}" class="bg-gov-navy hover:bg-blue-900 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    {{ app()->getLocale() === 'hi' ? 'सभी गैलरी देखें' : 'View All Gallery' }}
                </a>
            </div>
        </div>
    </section>
</x-app-layout>
