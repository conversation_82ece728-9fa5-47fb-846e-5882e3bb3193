<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\CauseController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\GalleryController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\DonationController;
use Illuminate\Support\Facades\Route;

// Public Routes
    
    // Home
    Route::get('/', [HomeController::class, 'index'])->name('home');
    Route::get('/about', [HomeController::class, 'about'])->name('about');

    // Causes
    Route::get('/causes', [CauseController::class, 'index'])->name('causes.index');
    Route::get('/causes/{cause:slug}', [CauseController::class, 'show'])->name('causes.show');

    // Blog
    Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
    Route::get('/blog/category/{category:slug}', [BlogController::class, 'category'])->name('blog.category');
    Route::get('/blog/{blog:slug}', [BlogController::class, 'show'])->name('blog.show');

    // Gallery
    Route::get('/gallery', [GalleryController::class, 'index'])->name('gallery.index');
    Route::get('/gallery/{album:slug}', [GalleryController::class, 'show'])->name('gallery.show');

    // Contact
    Route::get('/contact', [ContactController::class, 'index'])->name('contact.index');
    Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

    // Donations
    Route::get('/donate', [DonationController::class, 'index'])->name('donate.index');
    Route::get('/donate/{cause:slug}', [DonationController::class, 'cause'])->name('donate.cause');
    Route::post('/donate/process', [DonationController::class, 'process'])->name('donate.process');
    Route::get('/donate/success/{donation}', [DonationController::class, 'success'])->name('donate.success');
    Route::get('/donate/cancel', [DonationController::class, 'cancel'])->name('donate.cancel');

// Payment Webhooks (no locale needed)
Route::post('/webhooks/razorpay', [DonationController::class, 'razorpayWebhook'])->name('webhooks.razorpay');
Route::post('/webhooks/stripe', [DonationController::class, 'stripeWebhook'])->name('webhooks.stripe');
Route::post('/webhooks/paypal', [DonationController::class, 'paypalWebhook'])->name('webhooks.paypal');

// SEO Routes
Route::get('/sitemap.xml', [HomeController::class, 'sitemap'])->name('sitemap');
Route::get('/robots.txt', [HomeController::class, 'robots'])->name('robots');

// Authentication Routes
require __DIR__.'/auth.php';

// Admin Routes (always in Hindi/English)
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    // Site Settings
    Route::get('/settings', [App\Http\Controllers\Admin\SiteSettingController::class, 'index'])->name('settings.index');
    Route::put('/settings', [App\Http\Controllers\Admin\SiteSettingController::class, 'update'])->name('settings.update');
    Route::post('/settings/reset', [App\Http\Controllers\Admin\SiteSettingController::class, 'reset'])->name('settings.reset');

    // Content Management
    Route::resource('sliders', App\Http\Controllers\Admin\SliderController::class);

    // Causes with explicit ID binding for admin
    Route::get('causes', [App\Http\Controllers\Admin\CauseController::class, 'index'])->name('causes.index');
    Route::get('causes/create', [App\Http\Controllers\Admin\CauseController::class, 'create'])->name('causes.create');
    Route::post('causes', [App\Http\Controllers\Admin\CauseController::class, 'store'])->name('causes.store');
    Route::get('causes/{id}', [App\Http\Controllers\Admin\CauseController::class, 'show'])->name('causes.show')->where('id', '[0-9]+');
    Route::get('causes/{id}/edit', [App\Http\Controllers\Admin\CauseController::class, 'edit'])->name('causes.edit')->where('id', '[0-9]+');
    Route::put('causes/{id}', [App\Http\Controllers\Admin\CauseController::class, 'update'])->name('causes.update')->where('id', '[0-9]+');
    Route::delete('causes/{id}', [App\Http\Controllers\Admin\CauseController::class, 'destroy'])->name('causes.destroy')->where('id', '[0-9]+');

    Route::resource('blogs', App\Http\Controllers\Admin\BlogController::class);
    Route::resource('gallery', App\Http\Controllers\Admin\GalleryController::class);
    Route::resource('contacts', App\Http\Controllers\Admin\ContactController::class);
    Route::resource('about', App\Http\Controllers\Admin\AboutController::class);
});
