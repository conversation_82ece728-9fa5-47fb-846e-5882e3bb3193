<?php $__env->startSection('title', 'About Page Management'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <!-- <h1 class="h3 mb-0 text-gray-800">About Page Management</h1> -->
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                            <li class="breadcrumb-item active">About Sections</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        <?php endif; ?>

        <!-- Main Content Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">About Page Sections</h6>
                    <div>
                        <a href="<?php echo e(route('admin.about.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New Section
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if($sections->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="50">#</th>
                                    <th>Image</th>
                                    <th>Section Type</th>
                                    <th>Title</th>
                                    <th>Content</th>
                                    <th>Order</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="sections-table-body">
                                <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($sections->firstItem() + $index); ?></td>
                                        <td>
                                            <?php if($section->image_path): ?>
                                                <img src="<?php echo e(asset('storage/' . $section->image_path)); ?>"
                                                     alt="<?php echo e($section->title); ?>"
                                                     class="img-thumbnail"
                                                     style="width: 80px; height: 50px; object-fit: cover;">
                                            <?php elseif($section->icon): ?>
                                                <div class="text-center p-2">
                                                    <i class="<?php echo e($section->icon); ?> fa-2x text-primary"></i>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">No Image</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge badge-info"><?php echo e(ucfirst($section->section_type)); ?></span>
                                        </td>
                                        <td><?php echo e($section->title); ?></td>
                                        <td><?php echo e(Str::limit(strip_tags($section->content), 50)); ?></td>
                                        <td><?php echo e($section->sort_order); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo e($section->status == 'active' ? 'success' : 'secondary'); ?>">
                                                <?php echo e(ucfirst($section->status)); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.about.show', $section)); ?>"
                                                   class="btn btn-info btn-sm" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.about.edit', $section)); ?>"
                                                   class="btn btn-warning btn-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.about.destroy', $section)); ?>"
                                                      method="POST"
                                                      style="display: inline-block;"
                                                      onsubmit="return confirm('Are you sure you want to delete this section?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Record Count -->
                    <div class="mt-3">
                        <p class="text-muted" id="record-count">
                            Showing <?php echo e($sections->count()); ?> of <?php echo e($sections->total()); ?> records
                        </p>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No about sections found</h5>
                        <p class="text-muted">Start by creating your first about page section.</p>
                        <a href="<?php echo e(route('admin.about.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create First Section
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/infinite-scroll.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if($sections->count() > 0): ?>
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card-body'),
        tableBody: document.getElementById('sections-table-body'),
        url: '<?php echo e(route("admin.about.index")); ?>',
        renderRow: function(section, rowNumber) {
            const row = document.createElement('tr');

            // Image column
            let imageHtml = '<span class="text-muted">No Image</span>';
            if (section.image_path) {
                imageHtml = `<img src="<?php echo e(asset('storage/')); ?>/${section.image_path}"
                            alt="${section.title}"
                            class="img-thumbnail"
                            style="width: 80px; height: 50px; object-fit: cover;">`;
            } else if (section.icon) {
                imageHtml = `<div class="text-center p-2">
                            <i class="${section.icon} fa-2x text-primary"></i>
                            </div>`;
            }

            // Status badge
            const statusClass = section.status === 'active' ? 'success' : 'secondary';

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>${imageHtml}</td>
                <td><span class="badge badge-info">${section.section_type.charAt(0).toUpperCase() + section.section_type.slice(1)}</span></td>
                <td>${section.title}</td>
                <td>${section.content ? section.content.replace(/<[^>]*>/g, '').substring(0, 50) + (section.content.length > 50 ? '...' : '') : ''}</td>
                <td>${section.sort_order}</td>
                <td><span class="badge badge-${statusClass}">${section.status.charAt(0).toUpperCase() + section.status.slice(1)}</span></td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="<?php echo e(route('admin.about.show', '')); ?>/${section.id}"
                           class="btn btn-info btn-sm" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="<?php echo e(route('admin.about.edit', '')); ?>/${section.id}"
                           class="btn btn-warning btn-sm" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="<?php echo e(route('admin.about.destroy', '')); ?>/${section.id}"
                              method="POST"
                              style="display: inline-block;"
                              onsubmit="return confirm('Are you sure you want to delete this section?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/about/index.blade.php ENDPATH**/ ?>