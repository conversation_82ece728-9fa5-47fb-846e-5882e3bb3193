<?php $__env->startSection('title', 'Website Configuration'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">

    <!-- Settings Form -->
    <form method="POST" action="<?php echo e(route('admin.settings.update')); ?>" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>

        <!-- General Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs mr-2"></i>
                    Website Settings
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php $__currentLoopData = $settingGroups['general']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-6 mb-3">
                            <label for="<?php echo e($setting->key); ?>" class="form-label">
                                <?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>

                                <?php if($setting->description): ?>
                                    <small class="text-muted d-block"><?php echo e($setting->description); ?></small>
                                <?php endif; ?>
                            </label>

                            <?php if($setting->type === 'text'): ?>
                                <input type="text"
                                       id="<?php echo e($setting->key); ?>"
                                       name="settings[<?php echo e($setting->key); ?>]"
                                       value="<?php echo e($setting->value); ?>"
                                       class="form-control">
                            <?php elseif($setting->type === 'image'): ?>
                                <div>
                                    <?php if($setting->value): ?>
                                        <img src="<?php echo e(\App\Models\Setting::getImageUrl($setting->key)); ?>"
                                             alt="<?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>"
                                             class="img-thumbnail mb-2"
                                             style="width: 80px; height: 80px; object-fit: cover;">
                                    <?php endif; ?>
                                    <input type="file"
                                           id="<?php echo e($setting->key); ?>"
                                           name="files[<?php echo e($setting->key); ?>]"
                                           accept="image/*"
                                           class="form-control">
                                    <input type="hidden" name="settings[<?php echo e($setting->key); ?>]" value="<?php echo e($setting->value); ?>">
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Theme Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-palette mr-2"></i>
                    Theme & Branding
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php $__currentLoopData = $settingGroups['theme']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-6 mb-3">
                            <label for="<?php echo e($setting->key); ?>" class="form-label">
                                <?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>

                                <?php if($setting->description): ?>
                                    <small class="text-muted d-block"><?php echo e($setting->description); ?></small>
                                <?php endif; ?>
                            </label>

                            <?php if($setting->type === 'text'): ?>
                                <?php if(str_contains($setting->key, 'color')): ?>
                                    <div class="input-group">
                                        <input type="color"
                                               id="<?php echo e($setting->key); ?>_picker"
                                               value="<?php echo e($setting->value); ?>"
                                               onchange="document.getElementById('<?php echo e($setting->key); ?>').value = this.value"
                                               class="form-control form-control-color"
                                               style="width: 60px;">
                                        <input type="text"
                                               id="<?php echo e($setting->key); ?>"
                                               name="settings[<?php echo e($setting->key); ?>]"
                                               value="<?php echo e($setting->value); ?>"
                                               placeholder="#1e3a8a"
                                               class="form-control"
                                               onchange="document.getElementById('<?php echo e($setting->key); ?>_picker').value = this.value">
                                    </div>
                                <?php else: ?>
                                    <input type="text"
                                           id="<?php echo e($setting->key); ?>"
                                           name="settings[<?php echo e($setting->key); ?>]"
                                           value="<?php echo e($setting->value); ?>"
                                           class="form-control">
                                <?php endif; ?>
                            <?php elseif($setting->type === 'image'): ?>
                                <div>
                                    <?php if($setting->value): ?>
                                        <img src="<?php echo e(\App\Models\Setting::getImageUrl($setting->key)); ?>"
                                             alt="<?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>"
                                             class="img-thumbnail mb-2"
                                             style="width: 80px; height: 80px; object-fit: cover;">
                                    <?php endif; ?>
                                    <input type="file"
                                           id="<?php echo e($setting->key); ?>"
                                           name="files[<?php echo e($setting->key); ?>]"
                                           accept="image/*"
                                           class="form-control">
                                    <input type="hidden" name="settings[<?php echo e($setting->key); ?>]" value="<?php echo e($setting->value); ?>">
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-phone mr-2"></i>
                    Contact Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php $__currentLoopData = $settingGroups['contact']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-6 mb-3">
                            <label for="<?php echo e($setting->key); ?>" class="form-label">
                                <?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>

                                <?php if($setting->description): ?>
                                    <small class="text-muted d-block"><?php echo e($setting->description); ?></small>
                                <?php endif; ?>
                            </label>
                            <input type="text"
                                   id="<?php echo e($setting->key); ?>"
                                   name="settings[<?php echo e($setting->key); ?>]"
                                   value="<?php echo e($setting->value); ?>"
                                   class="form-control">
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Social Media Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-share-alt mr-2"></i>
                    Social Media Links
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php $__currentLoopData = $settingGroups['social']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-6 mb-3">
                            <label for="<?php echo e($setting->key); ?>" class="form-label">
                                <?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>

                                <?php if($setting->description): ?>
                                    <small class="text-muted d-block"><?php echo e($setting->description); ?></small>
                                <?php endif; ?>
                            </label>
                            <input type="text"
                                   id="<?php echo e($setting->key); ?>"
                                   name="settings[<?php echo e($setting->key); ?>]"
                                   value="<?php echo e($setting->value); ?>"
                                   class="form-control">
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Payment Method Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-qrcode mr-2"></i>
                    QR Payment Configuration
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php $__currentLoopData = $settingGroups['payment']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-<?php echo e($setting->type === 'image' ? '12' : '6'); ?> mb-3">
                            <label for="<?php echo e($setting->key); ?>" class="form-label">
                                <?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>

                                <?php if($setting->description): ?>
                                    <small class="text-muted d-block"><?php echo e($setting->description); ?></small>
                                <?php endif; ?>
                            </label>

                            <?php if($setting->type === 'text'): ?>
                                <textarea id="<?php echo e($setting->key); ?>"
                                          name="settings[<?php echo e($setting->key); ?>]"
                                          rows="3"
                                          class="form-control"
                                          placeholder="Enter payment instructions for users"><?php echo e($setting->value); ?></textarea>
                            <?php elseif($setting->type === 'boolean'): ?>
                                <select id="<?php echo e($setting->key); ?>"
                                        name="settings[<?php echo e($setting->key); ?>]"
                                        class="form-control">
                                    <option value="1" <?php echo e($setting->value == '1' ? 'selected' : ''); ?>>Enabled</option>
                                    <option value="0" <?php echo e($setting->value == '0' ? 'selected' : ''); ?>>Disabled</option>
                                </select>
                            <?php elseif($setting->type === 'image'): ?>
                                <div>
                                    <?php
                                        $imageUrl = \App\Models\Setting::getImageUrl($setting->key);
                                    ?>
                                    <?php if($imageUrl): ?>
                                        <div class="mb-3">
                                            <img src="<?php echo e($imageUrl); ?>"
                                                 alt="<?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>"
                                                 class="img-thumbnail"
                                                 style="width: 200px; height: 200px; object-fit: contain; border: 2px solid #ddd;">
                                            <div class="mt-2">
                                                <small class="text-success">
                                                    <i class="fas fa-check-circle"></i> Current QR Code
                                                </small>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="mb-3 text-center p-4 border border-dashed rounded">
                                            <i class="fas fa-qrcode fa-3x text-muted mb-2"></i>
                                            <p class="text-muted">No QR code uploaded yet</p>
                                        </div>
                                    <?php endif; ?>
                                    <input type="file"
                                           id="<?php echo e($setting->key); ?>"
                                           name="files[<?php echo e($setting->key); ?>]"
                                           accept="image/*"
                                           class="form-control"
                                           onchange="previewQRCode(this, '<?php echo e($setting->key); ?>')">
                                    <input type="hidden" name="settings[<?php echo e($setting->key); ?>]" value="<?php echo e($setting->value); ?>">
                                    <div id="qr-preview-<?php echo e($setting->key); ?>" class="mt-3" style="display: none;">
                                        <img id="qr-preview-img-<?php echo e($setting->key); ?>" class="img-thumbnail" style="width: 200px; height: 200px; object-fit: contain; border: 2px solid #28a745;">
                                        <div class="mt-2">
                                            <small class="text-success">
                                                <i class="fas fa-eye"></i> New QR Code Preview
                                            </small>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle"></i> Upload a clear QR code image (PNG, JPG, GIF). Recommended size: 500x500px
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- SEO Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-search mr-2"></i>
                    SEO Settings
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php $__currentLoopData = $settingGroups['seo']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-12 mb-3">
                            <label for="<?php echo e($setting->key); ?>" class="form-label">
                                <?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>

                                <?php if($setting->description): ?>
                                    <small class="text-muted d-block"><?php echo e($setting->description); ?></small>
                                <?php endif; ?>
                            </label>
                            <?php if($setting->key === 'meta_description'): ?>
                                <textarea id="<?php echo e($setting->key); ?>"
                                          name="settings[<?php echo e($setting->key); ?>]"
                                          rows="3"
                                          class="form-control"><?php echo e($setting->value); ?></textarea>
                            <?php else: ?>
                                <input type="text"
                                       id="<?php echo e($setting->key); ?>"
                                       name="settings[<?php echo e($setting->key); ?>]"
                                       value="<?php echo e($setting->value); ?>"
                                       class="form-control">
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="text-right">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save mr-2"></i>
                Save Settings
            </button>
        </div>
    </form>
</div>

<script>
function previewQRCode(input, key) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('qr-preview-' + key);
            const previewImg = document.getElementById('qr-preview-img-' + key);
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    }
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/settings/index.blade.php ENDPATH**/ ?>