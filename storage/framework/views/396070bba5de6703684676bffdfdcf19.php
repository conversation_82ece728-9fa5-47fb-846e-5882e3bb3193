<?php $__env->startSection('title', 'Gallery'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Gallery Albums</h3>
                    <div class="card-tools">
                        <!-- Per Page Selector -->
                        <div class="d-inline-block mr-2">
                            <select class="form-control form-control-sm" onchange="changePerPage(this.value)">
                                <option value="10" <?php echo e(request('per_page', 10) == 10 ? 'selected' : ''); ?>>10 per page</option>
                                <option value="20" <?php echo e(request('per_page', 10) == 20 ? 'selected' : ''); ?>>20 per page</option>
                                <option value="50" <?php echo e(request('per_page', 10) == 50 ? 'selected' : ''); ?>>50 per page</option>
                                <option value="100" <?php echo e(request('per_page', 10) == 100 ? 'selected' : ''); ?>>100 per page</option>
                            </select>
                        </div>
                        <a href="<?php echo e(route('admin.gallery.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if($galleries->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">#</th>
                                        <th>Cover Image</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="galleries-table-body">
                                    <?php $__currentLoopData = $galleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($galleries->firstItem() + $index); ?></td>
                                            <td>
                                                <?php if($gallery->cover_image): ?>
                                                    <img src="<?php echo e(asset('storage/' . $gallery->cover_image)); ?>" 
                                                         alt="<?php echo e($gallery->name); ?>" 
                                                         class="img-thumbnail" 
                                                         style="width: 80px; height: 50px; object-fit: cover;">
                                                <?php else: ?>
                                                    <span class="text-muted">No Image</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($gallery->name); ?></td>
                                            <td><?php echo e(Str::limit($gallery->description, 50)); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo e($gallery->status == 'active' ? 'success' : 'secondary'); ?>">
                                                    <?php echo e(ucfirst($gallery->status)); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($gallery->created_at->format('M d, Y')); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.gallery.show', $gallery)); ?>" 
                                                       class="btn btn-info btn-sm" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.gallery.edit', $gallery)); ?>" 
                                                       class="btn btn-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.gallery.destroy', $gallery)); ?>" 
                                                          method="POST" 
                                                          style="display: inline-block;"
                                                          onsubmit="return confirm('Are you sure you want to delete this gallery?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Record Count -->
                        <div class="mt-3">
                            <p class="text-muted" id="record-count">
                                Showing <?php echo e($galleries->count()); ?> of <?php echo e($galleries->total()); ?> records
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No gallery albums found</h5>
                            <p class="text-muted">Create your first gallery album to get started.</p>
                            <a href="<?php echo e(route('admin.gallery.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Gallery
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/infinite-scroll.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if($galleries->count() > 0): ?>
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card-body'),
        tableBody: document.getElementById('galleries-table-body'),
        url: '<?php echo e(route("admin.gallery.index")); ?>',
        renderRow: function(gallery, rowNumber) {
            const row = document.createElement('tr');

            // Image column
            let imageHtml = '<span class="text-muted">No Image</span>';
            if (gallery.cover_image) {
                imageHtml = `<img src="<?php echo e(asset('storage/')); ?>/${gallery.cover_image}"
                            alt="${gallery.name}"
                            class="img-thumbnail"
                            style="width: 80px; height: 50px; object-fit: cover;">`;
            }

            // Status badge
            const statusClass = gallery.status === 'active' ? 'success' : 'secondary';

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>${imageHtml}</td>
                <td>${gallery.name}</td>
                <td>${gallery.description ? gallery.description.substring(0, 50) + (gallery.description.length > 50 ? '...' : '') : ''}</td>
                <td><span class="badge badge-${statusClass}">${gallery.status.charAt(0).toUpperCase() + gallery.status.slice(1)}</span></td>
                <td>${new Date(gallery.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}</td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="<?php echo e(route('admin.gallery.show', '')); ?>/${gallery.id}"
                           class="btn btn-info btn-sm" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="<?php echo e(route('admin.gallery.edit', '')); ?>/${gallery.id}"
                           class="btn btn-warning btn-sm" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="<?php echo e(route('admin.gallery.destroy', '')); ?>/${gallery.id}"
                              method="POST"
                              style="display: inline-block;"
                              onsubmit="return confirm('Are you sure you want to delete this gallery?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/gallery/index.blade.php ENDPATH**/ ?>