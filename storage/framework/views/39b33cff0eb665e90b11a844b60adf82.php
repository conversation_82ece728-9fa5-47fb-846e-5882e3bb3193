<?php if (isset($component)) { $__componentOriginal4619374cef299e94fd7263111d0abc69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4619374cef299e94fd7263111d0abc69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-16">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4 font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'हमारे बारे में' : 'About Us'); ?>

                </h1>
                <p class="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
                    <?php echo e(app()->getLocale() === 'hi' 
                        ? 'समाज सेवा और मानवता के लिए समर्पित संगठन' 
                        : 'An organization dedicated to social service and humanity'); ?>

                </p>
            </div>
        </div>
    </section>

    <!-- Mission & Vision Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-2 gap-12">
                <!-- Mission -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-orange rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'हमारा मिशन' : 'Our Mission'); ?>

                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo e(app()->getLocale() === 'hi' 
                            ? 'समाज के वंचित वर्गों के उत्थान के लिए शिक्षा, स्वास्थ्य, और आर्थिक सहायता प्रदान करना। हमारा लक्ष्य एक न्यायसंगत और समान समाज का निर्माण करना है जहाँ हर व्यक्ति को विकास के समान अवसर मिलें।'
                            : 'To provide education, healthcare, and economic assistance for the upliftment of underprivileged sections of society. Our goal is to build a just and equitable society where every individual gets equal opportunities for development.'); ?>

                    </p>
                </div>

                <!-- Vision -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-green rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'हमारा विजन' : 'Our Vision'); ?>

                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo e(app()->getLocale() === 'hi' 
                            ? 'एक ऐसे भारत का निर्माण जहाँ गरीबी, अशिक्षा और बीमारी का अंत हो। हम चाहते हैं कि हर बच्चा शिक्षा प्राप्त करे, हर परिवार को स्वास्थ्य सेवा मिले, और हर व्यक्ति सम्मान के साथ जीवन जी सके।'
                            : 'Building an India where poverty, illiteracy and disease are eradicated. We want every child to receive education, every family to have access to healthcare, and every person to live with dignity.'); ?>

                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gov-blue mb-4 font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'हमारे मूल्य' : 'Our Values'); ?>

                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    <?php echo e(app()->getLocale() === 'hi' 
                        ? 'ये मूल्य हमारे काम की नींव हैं और हमारे हर निर्णय का आधार हैं।'
                        : 'These values form the foundation of our work and guide every decision we make.'); ?>

                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <!-- Transparency -->
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="w-16 h-16 bg-tricolor-saffron rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 1a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'पारदर्शिता' : 'Transparency'); ?>

                    </h3>
                    <p class="text-gray-600">
                        <?php echo e(app()->getLocale() === 'hi' 
                            ? 'हमारे सभी कार्य और वित्तीय लेन-देन पूर्णतः पारदर्शी हैं।'
                            : 'All our activities and financial transactions are completely transparent.'); ?>

                    </p>
                </div>

                <!-- Integrity -->
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="w-16 h-16 bg-tricolor-white border-2 border-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gov-blue" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'ईमानदारी' : 'Integrity'); ?>

                    </h3>
                    <p class="text-gray-600">
                        <?php echo e(app()->getLocale() === 'hi' 
                            ? 'हम अपने सभी कार्यों में ईमानदारी और नैतिकता का पालन करते हैं।'
                            : 'We maintain honesty and ethics in all our activities.'); ?>

                    </p>
                </div>

                <!-- Compassion -->
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <div class="w-16 h-16 bg-tricolor-green rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'करुणा' : 'Compassion'); ?>

                    </h3>
                    <p class="text-gray-600">
                        <?php echo e(app()->getLocale() === 'hi' 
                            ? 'हम दूसरों के दुःख को समझते हैं और उनकी मदद के लिए तत्पर रहते हैं।'
                            : 'We understand others\' suffering and are always ready to help them.'); ?>

                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gov-blue mb-4 font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'हमारी टीम' : 'Our Team'); ?>

                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    <?php echo e(app()->getLocale() === 'hi' 
                        ? 'समर्पित व्यक्तियों का समूह जो समाज सेवा के लिए प्रतिबद्ध है।'
                        : 'A group of dedicated individuals committed to social service.'); ?>

                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <!-- Team Member 1 -->
                <div class="text-center">
                    <div class="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-16 h-16 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'राज कुमार शर्मा' : 'Raj Kumar Sharma'); ?>

                    </h3>
                    <p class="text-gov-orange font-semibold mb-2">
                        <?php echo e(app()->getLocale() === 'hi' ? 'संस्थापक एवं अध्यक्ष' : 'Founder & President'); ?>

                    </p>
                    <p class="text-gray-600 text-sm">
                        <?php echo e(app()->getLocale() === 'hi' 
                            ? '25 वर्षों का सामाजिक कार्य का अनुभव'
                            : '25 years of social work experience'); ?>

                    </p>
                </div>

                <!-- Team Member 2 -->
                <div class="text-center">
                    <div class="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-16 h-16 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'प्रिया गुप्ता' : 'Priya Gupta'); ?>

                    </h3>
                    <p class="text-gov-orange font-semibold mb-2">
                        <?php echo e(app()->getLocale() === 'hi' ? 'कार्यक्रम निदेशक' : 'Program Director'); ?>

                    </p>
                    <p class="text-gray-600 text-sm">
                        <?php echo e(app()->getLocale() === 'hi' 
                            ? 'शिक्षा और महिला सशक्तिकरण विशेषज्ञ'
                            : 'Education and Women Empowerment Specialist'); ?>

                    </p>
                </div>

                <!-- Team Member 3 -->
                <div class="text-center">
                    <div class="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-16 h-16 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'डॉ. अमित वर्मा' : 'Dr. Amit Verma'); ?>

                    </h3>
                    <p class="text-gov-orange font-semibold mb-2">
                        <?php echo e(app()->getLocale() === 'hi' ? 'स्वास्थ्य सलाहकार' : 'Health Advisor'); ?>

                    </p>
                    <p class="text-gray-600 text-sm">
                        <?php echo e(app()->getLocale() === 'hi' 
                            ? 'सामुदायिक स्वास्थ्य विशेषज्ञ'
                            : 'Community Health Specialist'); ?>

                    </p>
                </div>
            </div>
        </div>
    </section>


 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $attributes = $__attributesOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__attributesOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $component = $__componentOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__componentOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php /**PATH D:\xampp8.2.3\htdocs\ngonew\resources\views/about.blade.php ENDPATH**/ ?>