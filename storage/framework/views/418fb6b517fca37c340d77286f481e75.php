<?php if (isset($component)) { $__componentOriginal4619374cef299e94fd7263111d0abc69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4619374cef299e94fd7263111d0abc69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php $__env->startSection('title', app()->getLocale() === 'hi' ? 'दान करें - एनजीओ पोर्टल' : 'Donate - NGO Portal'); ?>
    <?php $__env->startSection('meta_description', app()->getLocale() === 'hi' ? 'आपका छोटा सा योगदान भी किसी के जीवन में बड़ा बदलाव ला सकता है। अभी दान करें।' : 'Your small contribution can make a big difference in someone\'s life. Donate now.'); ?>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="relative container mx-auto px-4 text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 font-hindi">
                <?php echo e(app()->getLocale() === 'hi' ? 'दान करें' : 'Make a Donation'); ?>

            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                <?php echo e(app()->getLocale() === 'hi' 
                    ? 'आपका छोटा सा योगदान भी किसी के जीवन में बड़ा बदलाव ला सकता है।'
                    : 'Your small contribution can make a big difference in someone\'s life.'); ?>

            </p>
        </div>
    </section>

    <!-- Donation Form Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="bg-gov-blue text-white p-6">
                        <h2 class="text-2xl font-bold font-hindi">
                            <?php echo e(app()->getLocale() === 'hi' ? 'दान का विवरण' : 'Donation Details'); ?>

                        </h2>
                    </div>
                    
                    <form action="<?php echo e(route('donate.process')); ?>" method="POST" class="p-6">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Amount Selection -->
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                <?php echo e(app()->getLocale() === 'hi' ? 'दान की राशि (₹)' : 'Donation Amount (₹)'); ?>

                            </label>
                            
                            <!-- Quick Amount Buttons -->
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                                <button type="button" onclick="setAmount(500)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹500
                                </button>
                                <button type="button" onclick="setAmount(1000)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹1,000
                                </button>
                                <button type="button" onclick="setAmount(2500)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹2,500
                                </button>
                                <button type="button" onclick="setAmount(5000)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹5,000
                                </button>
                            </div>
                            
                            <input type="number" name="amount" id="amount" min="1" step="0.01" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                   placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'कस्टम राशि दर्ज करें' : 'Enter custom amount'); ?>">
                        </div>

                        <!-- Donor Information -->
                        <div class="grid md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'पूरा नाम' : 'Full Name'); ?> *
                                </label>
                                <input type="text" name="donor_name" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'आपका नाम' : 'Your Name'); ?>">
                            </div>
                            
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'ईमेल पता' : 'Email Address'); ?> *
                                </label>
                                <input type="email" name="donor_email" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'आपका ईमेल' : 'Your Email'); ?>">
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'फोन नंबर' : 'Phone Number'); ?>

                                </label>
                                <input type="tel" name="donor_phone"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'आपका फोन नंबर' : 'Your Phone Number'); ?>">
                            </div>
                            
                            <div>
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'भुगतान QR कोड' : 'Payment QR Code'); ?>

                                </label>
                                <div class="mt-4 flex flex-col items-center">
                                    <img src="<?php echo e(\App\Models\Setting::getImageUrl('qr_code_image', asset('images/qr-code.png'))); ?>" alt="QR Code" class="w-48 h-48 object-contain border rounded shadow">
                                    <span class="mt-2 text-gray-600 text-sm font-hindi text-center">
                                        <?php echo e(app()->getLocale() === 'hi' ? 'कृपया ऊपर दिए गए QR कोड को स्कैन करके भुगतान करें।' : 'Please scan the QR code above to make your payment.'); ?>

                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Message -->
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                <?php echo e(app()->getLocale() === 'hi' ? 'संदेश (वैकल्पिक)' : 'Message (Optional)'); ?>

                            </label>
                            <textarea name="message" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                      placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'आपका संदेश' : 'Your message'); ?>"></textarea>
                        </div>

                        <!-- Anonymous Donation -->
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_anonymous" value="1" class="mr-2">
                                <span class="text-gray-700 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'गुमनाम दान (आपका नाम सार्वजनिक नहीं किया जाएगा)' : 'Anonymous donation (your name will not be published)'); ?>

                                </span>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="w-full bg-gov-orange hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                            <?php echo e(app()->getLocale() === 'hi' ? 'अभी दान करें' : 'Donate Now'); ?>

                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Causes -->
    <?php if($featuredCauses->count() > 0): ?>
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-12 text-gov-navy font-hindi">
                <?php echo e(app()->getLocale() === 'hi' ? 'विशेष कारण' : 'Featured Causes'); ?>

            </h2>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $featuredCauses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cause): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <?php if($cause->image): ?>
                    <img src="<?php echo e(asset('storage/' . $cause->image)); ?>" alt="<?php echo e($cause->title); ?>" class="w-full h-48 object-cover">
                    <?php endif; ?>
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2 text-gov-navy font-hindi"><?php echo e($cause->title); ?></h3>
                        <p class="text-gray-600 mb-4"><?php echo e(Str::limit($cause->description, 100)); ?></p>
                        
                        <!-- Progress Bar -->
                        <?php
                            $percentage = $cause->goal_amount > 0 ? min(($cause->raised_amount / $cause->goal_amount) * 100, 100) : 0;
                        ?>
                        <div class="mb-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span><?php echo e(app()->getLocale() === 'hi' ? 'जुटाई गई राशि' : 'Raised'); ?>: ₹<?php echo e(number_format($cause->raised_amount)); ?></span>
                                <span><?php echo e(number_format($percentage, 1)); ?>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gov-orange h-2 rounded-full" style="width: <?php echo e($percentage); ?>%"></div>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">
                                <?php echo e(app()->getLocale() === 'hi' ? 'लक्ष्य' : 'Goal'); ?>: ₹<?php echo e(number_format($cause->goal_amount)); ?>

                            </div>
                        </div>
                        
                        <a href="<?php echo e(route('donate.cause', $cause)); ?>" 
                           class="w-full bg-secondary hover:bg-secondary text-white text-center py-2 px-4 rounded-lg transition-colors block">
                            <?php echo e(app()->getLocale() === 'hi' ? 'इस कारण के लिए दान करें' : 'Donate to this Cause'); ?>

                        </a>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <script>
    function setAmount(amount) {
        document.querySelector('input[name="amount"]').value = amount;
    }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $attributes = $__attributesOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__attributesOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $component = $__componentOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__componentOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/donate.blade.php ENDPATH**/ ?>