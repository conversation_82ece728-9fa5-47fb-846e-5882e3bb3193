<?php $__env->startSection('title', 'Donation Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Donation Details #<?php echo e($donation->id); ?></h1>
        <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>Back to List
        </a>
    </div>

    <div class="row">
        <!-- Donation Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Donation Information</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Transaction ID:</strong></td>
                                    <td><?php echo e($donation->transaction_id); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Receipt Number:</strong></td>
                                    <td><?php echo e($donation->receipt_number); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Amount:</strong></td>
                                    <td><strong class="text-success">₹<?php echo e(number_format($donation->amount, 2)); ?></strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Currency:</strong></td>
                                    <td><?php echo e($donation->currency); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Method:</strong></td>
                                    <td>
                                        <?php if($donation->payment_method === 'qr_code'): ?>
                                            <span class="badge badge-warning">QR Code Payment</span>
                                        <?php else: ?>
                                            <span class="badge badge-primary"><?php echo e(ucfirst($donation->payment_gateway)); ?> Gateway</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <?php switch($donation->status):
                                            case ('pending'): ?>
                                                <span class="badge badge-warning">Pending</span>
                                                <?php break; ?>
                                            <?php case ('completed'): ?>
                                                <span class="badge badge-success">Completed</span>
                                                <?php break; ?>
                                            <?php case ('failed'): ?>
                                                <span class="badge badge-danger">Failed</span>
                                                <?php break; ?>
                                            <?php case ('refunded'): ?>
                                                <span class="badge badge-secondary">Refunded</span>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Cause:</strong></td>
                                    <td>
                                        <?php if($donation->cause): ?>
                                            <a href="<?php echo e(route('admin.causes.show', $donation->cause->id)); ?>" class="text-decoration-none">
                                                <?php echo e($donation->cause->title); ?>

                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">General Donation</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Anonymous:</strong></td>
                                    <td><?php echo e($donation->is_anonymous ? 'Yes' : 'No'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td><?php echo e($donation->created_at->format('M d, Y H:i:s')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Donated At:</strong></td>
                                    <td><?php echo e($donation->donated_at ? $donation->donated_at->format('M d, Y H:i:s') : 'N/A'); ?></td>
                                </tr>
                                <?php if($donation->gateway_transaction_id): ?>
                                <tr>
                                    <td><strong>Gateway Transaction ID:</strong></td>
                                    <td><?php echo e($donation->gateway_transaction_id); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Donor Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Donor Information</h3>
                </div>
                <div class="card-body">
                    <?php if(!$donation->is_anonymous): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td><?php echo e($donation->donor_name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td><?php echo e($donation->donor_email); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td><?php echo e($donation->donor_phone ?: 'N/A'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td><?php echo e($donation->donor_address ?: 'N/A'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">This is an anonymous donation. Donor information is not displayed.</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Message -->
            <?php if($donation->message): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Donor Message</h3>
                </div>
                <div class="card-body">
                    <p><?php echo e($donation->message); ?></p>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Actions & Payment Screenshot -->
        <div class="col-md-4">
            <!-- Status Update -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Update Status</h3>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.donations.updateStatus', $donation)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control" required>
                                <option value="pending" <?php echo e($donation->status === 'pending' ? 'selected' : ''); ?>>Pending</option>
                                <option value="completed" <?php echo e($donation->status === 'completed' ? 'selected' : ''); ?>>Completed</option>
                                <option value="failed" <?php echo e($donation->status === 'failed' ? 'selected' : ''); ?>>Failed</option>
                                <option value="refunded" <?php echo e($donation->status === 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin_notes">Admin Notes</label>
                            <textarea name="admin_notes" id="admin_notes" class="form-control" rows="3" 
                                      placeholder="Add notes about this donation..."><?php echo e($donation->admin_notes); ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">Update Status</button>
                    </form>
                </div>
            </div>

            <!-- Payment Screenshot -->
            <?php if($donation->payment_screenshot): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title mb-0">Payment Screenshot</h3>
                </div>
                <div class="card-body text-center">
                    <img src="<?php echo e($donation->payment_screenshot_url); ?>" 
                         alt="Payment Screenshot" 
                         class="img-fluid rounded shadow"
                         style="max-height: 300px; cursor: pointer;"
                         onclick="openImageModal(this.src)">
                    <div class="mt-2">
                        <a href="<?php echo e($donation->payment_screenshot_url); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt mr-1"></i>View Full Size
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">Actions</h3>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.donations.destroy', $donation)); ?>" method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this donation? This action cannot be undone.')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger btn-block">
                            <i class="fas fa-trash mr-2"></i>Delete Donation
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Screenshot</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Payment Screenshot" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
function openImageModal(src) {
    document.getElementById('modalImage').src = src;
    $('#imageModal').modal('show');
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/donations/show.blade.php ENDPATH**/ ?>