<?php if (isset($component)) { $__componentOriginal4619374cef299e94fd7263111d0abc69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4619374cef299e94fd7263111d0abc69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-16"
             <?php if($heroSection && $heroSection->image_url): ?>
             style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('<?php echo e($heroSection->image_url); ?>'); background-size: cover; background-position: center;"
             <?php endif; ?>>
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4 font-hindi">
                    <?php if($heroSection): ?>
                        <?php echo e($heroSection->title); ?>

                    <?php else: ?>
                        <?php echo e(app()->getLocale() === 'hi' ? 'हमारे बारे में' : 'About Us'); ?>

                    <?php endif; ?>
                </h1>
                <p class="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
                    <?php if($heroSection && $heroSection->subtitle): ?>
                        <?php echo e($heroSection->subtitle); ?>

                    <?php else: ?>
                        <?php echo e(app()->getLocale() === 'hi'
                            ? 'समाज सेवा और मानवता के लिए समर्पित संगठन'
                            : 'An organization dedicated to social service and humanity'); ?>

                    <?php endif; ?>
                </p>
                <?php if($heroSection && $heroSection->content): ?>
                    <div class="mt-4 text-lg text-gray-300 max-w-4xl mx-auto">
                        <?php echo nl2br(e($heroSection->content)); ?>

                    </div>
                <?php endif; ?>
                <?php if($heroSection && $heroSection->button_text && $heroSection->button_url): ?>
                    <div class="mt-6">
                        <a href="<?php echo e($heroSection->button_url); ?>" class="bg-gov-orange hover:bg-orange-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
                            <?php echo e($heroSection->button_text); ?>

                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Mission & Vision Section -->
    <?php if($missionSection || $visionSection): ?>
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-2 gap-12">
                <!-- Mission -->
                <?php if($missionSection): ?>
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-orange rounded-full flex items-center justify-center mx-auto mb-6">
                        <?php if($missionSection->icon): ?>
                            <i class="<?php echo e($missionSection->icon); ?> text-white text-3xl"></i>
                        <?php else: ?>
                            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        <?php endif; ?>
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        <?php echo e($missionSection->title); ?>

                    </h2>
                    <?php if($missionSection->subtitle): ?>
                        <h3 class="text-xl text-gov-orange mb-4"><?php echo e($missionSection->subtitle); ?></h3>
                    <?php endif; ?>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo nl2br(e($missionSection->content)); ?>

                    </p>
                    <?php if($missionSection->button_text && $missionSection->button_url): ?>
                        <div class="mt-4">
                            <a href="<?php echo e($missionSection->button_url); ?>" class="bg-gov-orange hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                <?php echo e($missionSection->button_text); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <!-- Fallback Mission -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-orange rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'हमारा मिशन' : 'Our Mission'); ?>

                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo e(app()->getLocale() === 'hi'
                            ? 'समाज के वंचित वर्गों के उत्थान के लिए शिक्षा, स्वास्थ्य, और आर्थिक सहायता प्रदान करना।'
                            : 'To provide education, healthcare, and economic assistance for the upliftment of underprivileged sections of society.'); ?>

                    </p>
                </div>
                <?php endif; ?>

                <!-- Vision -->
                <?php if($visionSection): ?>
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-green rounded-full flex items-center justify-center mx-auto mb-6">
                        <?php if($visionSection->icon): ?>
                            <i class="<?php echo e($visionSection->icon); ?> text-white text-3xl"></i>
                        <?php else: ?>
                            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                            </svg>
                        <?php endif; ?>
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        <?php echo e($visionSection->title); ?>

                    </h2>
                    <?php if($visionSection->subtitle): ?>
                        <h3 class="text-xl text-gov-orange mb-4"><?php echo e($visionSection->subtitle); ?></h3>
                    <?php endif; ?>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo nl2br(e($visionSection->content)); ?>

                    </p>
                    <?php if($visionSection->button_text && $visionSection->button_url): ?>
                        <div class="mt-4">
                            <a href="<?php echo e($visionSection->button_url); ?>" class="bg-gov-green hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                <?php echo e($visionSection->button_text); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <!-- Fallback Vision -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gov-green rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h2 class="text-3xl font-bold text-gov-blue mb-4 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'हमारा विजन' : 'Our Vision'); ?>

                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo e(app()->getLocale() === 'hi'
                            ? 'एक ऐसे भारत का निर्माण जहाँ गरीबी, अशिक्षा और बीमारी का अंत हो।'
                            : 'Building an India where poverty, illiteracy and disease are eradicated.'); ?>

                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Values Section -->
    <?php if($valuesSections->count() > 0): ?>
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gov-blue mb-4 font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'हमारे मूल्य' : 'Our Values'); ?>

                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    <?php echo e(app()->getLocale() === 'hi'
                        ? 'ये मूल्य हमारे काम की नींव हैं और हमारे हर निर्णय का आधार हैं।'
                        : 'These values form the foundation of our work and guide every decision we make.'); ?>

                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <?php $__currentLoopData = $valuesSections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <?php if($value->image_url): ?>
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full overflow-hidden">
                            <img src="<?php echo e($value->image_url); ?>" alt="<?php echo e($value->title); ?>" class="w-full h-full object-cover">
                        </div>
                    <?php elseif($value->icon): ?>
                        <div class="w-16 h-16 bg-gov-blue rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="<?php echo e($value->icon); ?> text-white text-2xl"></i>
                        </div>
                    <?php else: ?>
                        <div class="w-16 h-16 bg-gov-blue rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    <?php endif; ?>
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        <?php echo e($value->title); ?>

                    </h3>
                    <?php if($value->subtitle): ?>
                        <h4 class="text-lg text-gov-orange mb-2"><?php echo e($value->subtitle); ?></h4>
                    <?php endif; ?>
                    <p class="text-gray-600">
                        <?php echo nl2br(e($value->content)); ?>

                    </p>
                    <?php if($value->button_text && $value->button_url): ?>
                        <div class="mt-4">
                            <a href="<?php echo e($value->button_url); ?>" class="text-gov-blue hover:text-gov-orange font-semibold">
                                <?php echo e($value->button_text); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Team Section -->
    <?php if($teamSections->count() > 0): ?>
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gov-blue mb-4 font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'हमारी टीम' : 'Our Team'); ?>

                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    <?php echo e(app()->getLocale() === 'hi'
                        ? 'समर्पित व्यक्तियों का समूह जो समाज सेवा के लिए प्रतिबद्ध है।'
                        : 'A group of dedicated individuals committed to social service.'); ?>

                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <?php $__currentLoopData = $teamSections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="text-center">
                    <?php if($member->image_url): ?>
                        <div class="w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden">
                            <img src="<?php echo e($member->image_url); ?>" alt="<?php echo e($member->title); ?>" class="w-full h-full object-cover">
                        </div>
                    <?php else: ?>
                        <div class="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <svg class="w-16 h-16 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    <?php endif; ?>
                    <h3 class="text-xl font-bold text-gov-blue mb-2 font-hindi">
                        <?php echo e($member->title); ?>

                    </h3>
                    <?php if($member->extra_data && isset($member->extra_data['position'])): ?>
                        <p class="text-gov-orange font-semibold mb-2">
                            <?php echo e($member->extra_data['position']); ?>

                        </p>
                    <?php elseif($member->subtitle): ?>
                        <p class="text-gov-orange font-semibold mb-2">
                            <?php echo e($member->subtitle); ?>

                        </p>
                    <?php endif; ?>
                    <?php if($member->content): ?>
                        <p class="text-gray-600 text-sm mb-2">
                            <?php echo nl2br(e($member->content)); ?>

                        </p>
                    <?php endif; ?>
                    <?php if($member->extra_data && isset($member->extra_data['experience'])): ?>
                        <p class="text-gray-600 text-sm mb-2">
                            <?php echo e($member->extra_data['experience']); ?>

                        </p>
                    <?php endif; ?>
                    <?php if($member->extra_data && (isset($member->extra_data['email']) || isset($member->extra_data['phone']))): ?>
                        <div class="mt-3 space-y-1">
                            <?php if(isset($member->extra_data['email'])): ?>
                                <p class="text-sm">
                                    <a href="mailto:<?php echo e($member->extra_data['email']); ?>" class="text-gov-blue hover:text-gov-orange">
                                        <i class="fas fa-envelope mr-1"></i><?php echo e($member->extra_data['email']); ?>

                                    </a>
                                </p>
                            <?php endif; ?>
                            <?php if(isset($member->extra_data['phone'])): ?>
                                <p class="text-sm">
                                    <a href="tel:<?php echo e($member->extra_data['phone']); ?>" class="text-gov-blue hover:text-gov-orange">
                                        <i class="fas fa-phone mr-1"></i><?php echo e($member->extra_data['phone']); ?>

                                    </a>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Additional Sections (History, Achievements, Custom) -->
    <?php if($historySections->count() > 0 || $achievementsSections->count() > 0 || $customSections->count() > 0): ?>
        <?php $__currentLoopData = [$historySections, $achievementsSections, $customSections]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sections): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($sections->count() > 0): ?>
                <section class="py-16 <?php echo e($loop->even ? 'bg-gray-50' : 'bg-white'); ?>">
                    <div class="container mx-auto px-4">
                        <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="mb-12 <?php echo e(!$loop->last ? 'border-b border-gray-200 pb-12' : ''); ?>">
                                <div class="text-center mb-8">
                                    <?php if($section->icon): ?>
                                        <div class="w-16 h-16 bg-gov-blue rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i class="<?php echo e($section->icon); ?> text-white text-2xl"></i>
                                        </div>
                                    <?php endif; ?>
                                    <h2 class="text-3xl md:text-4xl font-bold text-gov-blue mb-4 font-hindi">
                                        <?php echo e($section->title); ?>

                                    </h2>
                                    <?php if($section->subtitle): ?>
                                        <h3 class="text-xl text-gov-orange mb-4"><?php echo e($section->subtitle); ?></h3>
                                    <?php endif; ?>
                                </div>

                                <?php if($section->image_url): ?>
                                    <div class="text-center mb-8">
                                        <img src="<?php echo e($section->image_url); ?>" alt="<?php echo e($section->title); ?>" class="mx-auto rounded-lg shadow-lg max-w-full h-auto">
                                    </div>
                                <?php endif; ?>

                                <?php if($section->content): ?>
                                    <div class="prose prose-lg mx-auto text-gray-600">
                                        <?php echo nl2br(e($section->content)); ?>

                                    </div>
                                <?php endif; ?>

                                <?php if($section->button_text && $section->button_url): ?>
                                    <div class="text-center mt-8">
                                        <a href="<?php echo e($section->button_url); ?>" class="bg-gov-blue hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                                            <?php echo e($section->button_text); ?>

                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </section>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>


 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $attributes = $__attributesOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__attributesOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $component = $__componentOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__componentOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/about.blade.php ENDPATH**/ ?>