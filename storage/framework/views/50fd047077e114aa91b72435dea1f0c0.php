<?php $__env->startSection('title', 'Causes'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Fundraising Causes</h3>
                    <div class="card-tools">
                        <!-- Per Page Selector -->
                        <div class="d-inline-block mr-2">
                            <select class="form-control form-control-sm" onchange="changePerPage(this.value)">
                                <option value="10" <?php echo e(request('per_page', 10) == 10 ? 'selected' : ''); ?>>10 per page</option>
                                <option value="20" <?php echo e(request('per_page', 10) == 20 ? 'selected' : ''); ?>>20 per page</option>
                                <option value="50" <?php echo e(request('per_page', 10) == 50 ? 'selected' : ''); ?>>50 per page</option>
                                <option value="100" <?php echo e(request('per_page', 10) == 100 ? 'selected' : ''); ?>>100 per page</option>
                            </select>
                        </div>
                        <a href="<?php echo e(route('admin.causes.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <?php if($causes->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">#</th>
                                        <th>Image</th>
                                        <th>Title</th>
                                        <th>Goal Amount</th>
                                        <th>Raised</th>
                                        <th>Status</th>
                                        <th>Featured</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $causes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $cause): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($causes->firstItem() + $index); ?></td>
                                            <td>
                                                <?php if($cause->featured_image): ?>
                                                    <img src="<?php echo e(asset('storage/' . $cause->featured_image)); ?>"
                                                         alt="<?php echo e($cause->title); ?>"
                                                         class="img-thumbnail"
                                                         style="width: 80px; height: 50px; object-fit: cover;">
                                                <?php else: ?>
                                                    <span class="text-muted">No Image</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo e($cause->title); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo e(Str::limit($cause->short_description, 50)); ?></small>
                                            </td>
                                            <td>₹<?php echo e(number_format($cause->goal_amount, 0)); ?></td>
                                            <td>
                                                ₹<?php echo e(number_format($cause->raised_amount, 0)); ?>

                                                <br>
                                                <small class="text-muted"><?php echo e(number_format(($cause->raised_amount / $cause->goal_amount) * 100, 1)); ?>%</small>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?php echo e($cause->status === 'active' ? 'success' : ($cause->status === 'completed' ? 'primary' : 'warning')); ?>">
                                                    <?php echo e(ucfirst($cause->status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php if($cause->is_featured): ?>
                                                    <span class="badge badge-info">Featured</span>
                                                <?php else: ?>
                                                    <span class="text-muted">No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.causes.show', $cause->id)); ?>"
                                                       class="btn btn-info btn-sm" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.causes.edit', $cause->id)); ?>"
                                                       class="btn btn-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.causes.destroy', $cause->id)); ?>"
                                                          method="POST"
                                                          style="display: inline-block;"
                                                          onsubmit="return confirm('Are you sure you want to delete this cause?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No causes found</h5>
                            <p class="text-muted">Create your first fundraising cause to get started.</p>
                            <a href="<?php echo e(route('admin.causes.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Cause
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if($causes->hasPages()): ?>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-sm-12 col-md-5">
                                <div class="dataTables_info">
                                    Showing <?php echo e($causes->firstItem()); ?> to <?php echo e($causes->lastItem()); ?> of <?php echo e($causes->total()); ?> results
                                </div>
                            </div>
                            <div class="col-sm-12 col-md-7">
                                <div class="dataTables_paginate paging_simple_numbers float-right">
                                    <?php echo e($causes->links()); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xampp8.2.3\htdocs\ngonew\resources\views/admin/causes/index.blade.php ENDPATH**/ ?>