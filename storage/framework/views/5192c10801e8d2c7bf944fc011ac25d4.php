<?php $__env->startSection('title', 'Contacts'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Contact Messages</h3>
                    <div class="card-tools">
                        <!-- Per Page Selector -->
                        <div class="d-inline-block mr-2">
                            <select class="form-control form-control-sm" onchange="changePerPage(this.value)">
                                <option value="10" <?php echo e(request('per_page', 10) == 10 ? 'selected' : ''); ?>>10 per page</option>
                                <option value="20" <?php echo e(request('per_page', 10) == 20 ? 'selected' : ''); ?>>20 per page</option>
                                <option value="50" <?php echo e(request('per_page', 10) == 50 ? 'selected' : ''); ?>>50 per page</option>
                                <option value="100" <?php echo e(request('per_page', 10) == 100 ? 'selected' : ''); ?>>100 per page</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if($contacts->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">#</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Subject</th>
                                        <th>Message</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="contacts-table-body">
                                    <?php $__currentLoopData = $contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="<?php echo e($contact->status === 'new' ? 'table-warning' : ''); ?>">
                                            <td><?php echo e($contacts->firstItem() + $index); ?></td>
                                            <td><?php echo e($contact->name); ?></td>
                                            <td><?php echo e($contact->email); ?></td>
                                            <td><?php echo e($contact->subject); ?></td>
                                            <td><?php echo e(Str::limit($contact->message, 50)); ?></td>
                                            <td><?php echo e($contact->created_at->format('M d, Y')); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo e($contact->status !== 'new' ? 'success' : 'warning'); ?>">
                                                    <?php echo e($contact->status !== 'new' ? 'Read' : 'Unread'); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.contacts.show', $contact)); ?>" 
                                                       class="btn btn-info btn-sm" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.contacts.destroy', $contact)); ?>" 
                                                          method="POST" 
                                                          style="display: inline-block;"
                                                          onsubmit="return confirm('Are you sure you want to delete this message?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Record Count -->
                        <div class="mt-3">
                            <p class="text-muted" id="record-count">
                                Showing <?php echo e($contacts->count()); ?> of <?php echo e($contacts->total()); ?> records
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No contact messages</h5>
                            <p class="text-muted">Contact messages will appear here when visitors submit the contact form.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/infinite-scroll.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if($contacts->count() > 0): ?>
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card-body'),
        tableBody: document.getElementById('contacts-table-body'),
        url: '<?php echo e(route("admin.contacts.index")); ?>',
        renderRow: function(contact, rowNumber) {
            const row = document.createElement('tr');

            // Add unread styling
            if (!contact.is_read) {
                row.className = 'table-warning';
            }

            // Status badge
            const statusClass = contact.is_read ? 'success' : 'warning';
            const statusText = contact.is_read ? 'Read' : 'Unread';

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>${contact.name}</td>
                <td>${contact.email}</td>
                <td>${contact.subject}</td>
                <td>${contact.message ? contact.message.substring(0, 50) + (contact.message.length > 50 ? '...' : '') : ''}</td>
                <td>${new Date(contact.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}</td>
                <td><span class="badge badge-${statusClass}">${statusText}</span></td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="<?php echo e(route('admin.contacts.show', '')); ?>/${contact.id}"
                           class="btn btn-info btn-sm" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        <form action="<?php echo e(route('admin.contacts.destroy', '')); ?>/${contact.id}"
                              method="POST"
                              style="display: inline-block;"
                              onsubmit="return confirm('Are you sure you want to delete this message?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Set the correct hasMore value based on pagination
    infiniteScroll.hasMore = <?php echo e($contacts->hasMorePages() ? 'true' : 'false'); ?>;
    infiniteScroll.currentPage = <?php echo e($contacts->currentPage()); ?>;
    infiniteScroll.totalRecords = <?php echo e($contacts->total()); ?>;

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/contacts/index.blade.php ENDPATH**/ ?>