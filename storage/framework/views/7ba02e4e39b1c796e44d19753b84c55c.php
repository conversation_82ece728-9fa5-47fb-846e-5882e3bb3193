<?php if (isset($component)) { $__componentOriginal4619374cef299e94fd7263111d0abc69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4619374cef299e94fd7263111d0abc69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-16">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4 font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'हमारे कार्यक्रम' : 'Our Causes'); ?>

                </h1>
                <p class="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
                    <?php echo e(app()->getLocale() === 'hi' 
                        ? 'समाज सेवा के लिए हमारे विभिन्न कार्यक्रमों में योगदान दें' 
                        : 'Contribute to our various programs for social service'); ?>

                </p>
            </div>
        </div>
    </section>



    <!-- Filters and Search -->
    <section class="py-8 bg-gray-50">
        <div class="container mx-auto px-4">
            <form method="GET" action="<?php echo e(route('causes.index')); ?>" class="flex flex-wrap gap-4 items-center justify-between">
                <!-- Search -->
                <div class="flex-1 min-w-64">
                    <input type="text" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>"
                           placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'कार्यक्रम खोजें...' : 'Search causes...'); ?>"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gov-blue focus:border-transparent">
                </div>

                <!-- Sort -->
                <div class="flex gap-2">
                    <select name="sort" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gov-blue">
                        <option value="featured" <?php echo e(request('sort') === 'featured' ? 'selected' : ''); ?>>
                            <?php echo e(app()->getLocale() === 'hi' ? 'फीचर्ड' : 'Featured'); ?>

                        </option>
                        <option value="newest" <?php echo e(request('sort') === 'newest' ? 'selected' : ''); ?>>
                            <?php echo e(app()->getLocale() === 'hi' ? 'नवीनतम' : 'Newest'); ?>

                        </option>
                        <option value="progress" <?php echo e(request('sort') === 'progress' ? 'selected' : ''); ?>>
                            <?php echo e(app()->getLocale() === 'hi' ? 'प्रगति के अनुसार' : 'By Progress'); ?>

                        </option>
                        <option value="goal_high" <?php echo e(request('sort') === 'goal_high' ? 'selected' : ''); ?>>
                            <?php echo e(app()->getLocale() === 'hi' ? 'उच्च लक्ष्य' : 'Highest Goal'); ?>

                        </option>
                    </select>
                    
                    <button type="submit" class="bg-gov-blue hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        <?php echo e(app()->getLocale() === 'hi' ? 'खोजें' : 'Search'); ?>

                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- Causes Grid -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <?php if($causes && $causes->count() > 0): ?>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php $__currentLoopData = $causes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cause): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow flex flex-col h-full">
                            <!-- Image -->
                            <div class="relative h-48 bg-gray-200 flex-shrink-0">
                                <?php if($cause->featured_image): ?>
                                    <img src="<?php echo e($cause->featured_image_url); ?>" 
                                         alt="<?php echo e($cause->title); ?>"
                                         class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="w-full h-full flex items-center justify-center bg-gray-300">
                                        <svg class="w-16 h-16 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($cause->is_featured): ?>
                                    <div class="absolute top-2 left-2 bg-gov-orange text-white px-2 py-1 rounded text-sm font-semibold">
                                        <?php echo e(app()->getLocale() === 'hi' ? 'फीचर्ड' : 'Featured'); ?>

                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Content -->
                            <div class="p-6 flex flex-col flex-grow">
                                <!-- Title - Fixed Height -->
                                <h3 class="text-xl font-bold text-gov-blue mb-3 font-hindi min-h-[3.5rem] line-clamp-2 flex-shrink-0" lang="hi">
                                    <?php echo e($cause->title); ?>

                                </h3>

                                <!-- Description - Fixed Height -->
                                <p class="text-gray-600 mb-6 line-clamp-3 min-h-[4.5rem] flex-shrink-0" lang="hi">
                                    <?php echo e($cause->short_description); ?>

                                </p>

                                <!-- Progress Section - Consistent Spacing -->
                                <div class="mb-6 flex-shrink-0">
                                    <!-- Progress Bar -->
                                    <div class="mb-4">
                                        <div class="flex justify-between text-sm text-gray-600 mb-2">
                                            <span><?php echo e(app()->getLocale() === 'hi' ? 'प्रगति' : 'Progress'); ?></span>
                                            <span><?php echo e(number_format($cause->progress_percentage, 1)); ?>%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-gov-green h-2 rounded-full transition-all duration-300"
                                                 style="width: <?php echo e(min(100, $cause->progress_percentage)); ?>%"></div>
                                        </div>
                                    </div>

                                    <!-- Amount Info -->
                                    <div class="flex justify-between text-sm">
                                        <div>
                                            <div class="text-gray-500 mb-1">
                                                <?php echo e(app()->getLocale() === 'hi' ? 'संग्रहित' : 'Raised'); ?>

                                            </div>
                                            <div class="font-semibold text-gov-blue">
                                                <?php echo e($cause->formatted_raised_amount); ?>

                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-gray-500 mb-1">
                                                <?php echo e(app()->getLocale() === 'hi' ? 'लक्ष्य' : 'Goal'); ?>

                                            </div>
                                            <div class="font-semibold text-gov-blue">
                                                <?php echo e($cause->formatted_goal_amount); ?>

                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Spacer to push buttons to bottom -->
                                <div class="flex-grow"></div>

                                <!-- Action Buttons - Fixed at Bottom -->
                                <div class="flex gap-2 flex-shrink-0">
                                    <a href="<?php echo e(route('causes.show', $cause->slug)); ?>"
                                       class="flex-1 bg-gov-blue hover:bg-blue-700 text-white text-center py-2.5 px-4 rounded-lg transition-colors font-medium">
                                        <?php echo e(app()->getLocale() === 'hi' ? 'विवरण देखें' : 'View Details'); ?>

                                    </a>
                                    <a href="<?php echo e(route('donate.cause', $cause->slug)); ?>"
                                       class="flex-1 bg-gov-orange hover:bg-orange-600 text-white text-center py-2.5 px-4 rounded-lg transition-colors font-medium">
                                        <?php echo e(app()->getLocale() === 'hi' ? 'दान करें' : 'Donate Now'); ?>

                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <?php if($causes->hasPages()): ?>
                    <div class="mt-12 flex justify-center">
                        <?php echo e($causes->links()); ?>

                    </div>
                <?php endif; ?>
            <?php else: ?>
                <!-- No Causes Found -->
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-600 mb-4 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'कोई कार्यक्रम नहीं मिला' : 'No Causes Found'); ?>

                    </h3>
                    <p class="text-gray-500 mb-6">
                        <?php echo e(app()->getLocale() === 'hi' 
                            ? 'वर्तमान में कोई सक्रिय कार्यक्रम उपलब्ध नहीं है। कृपया बाद में पुनः जांचें।'
                            : 'Currently no active causes are available. Please check back later.'); ?>

                    </p>
                    <a href="<?php echo e(route('home')); ?>" 
                       class="bg-gov-blue hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                        <?php echo e(app()->getLocale() === 'hi' ? 'होम पर वापस जाएं' : 'Back to Home'); ?>

                    </a>
                </div>
            <?php endif; ?>
        </div>
    </section>


 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $attributes = $__attributesOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__attributesOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $component = $__componentOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__componentOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/causes.blade.php ENDPATH**/ ?>