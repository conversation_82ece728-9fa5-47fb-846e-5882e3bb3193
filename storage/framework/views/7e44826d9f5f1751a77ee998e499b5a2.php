<?php $__env->startSection('title', 'Blogs'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Blog Posts</h3>
                    <div class="card-tools">
                        <!-- Per Page Selector -->
                        <div class="d-inline-block mr-2">
                            <select class="form-control form-control-sm" onchange="changePerPage(this.value)">
                                <option value="10" <?php echo e(request('per_page', 10) == 10 ? 'selected' : ''); ?>>10 per page</option>
                                <option value="20" <?php echo e(request('per_page', 10) == 20 ? 'selected' : ''); ?>>20 per page</option>
                                <option value="50" <?php echo e(request('per_page', 10) == 50 ? 'selected' : ''); ?>>50 per page</option>
                                <option value="100" <?php echo e(request('per_page', 10) == 100 ? 'selected' : ''); ?>>100 per page</option>
                            </select>
                        </div>
                        <a href="<?php echo e(route('admin.blogs.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if($blogs->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">#</th>
                                        <th>Image</th>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Published</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="blogs-table-body">
                                    <?php $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($blogs->firstItem() + $index); ?></td>
                                            <td>
                                                <?php if($blog->featured_image): ?>
                                                    <img src="<?php echo e(asset('storage/' . $blog->featured_image)); ?>" 
                                                         alt="<?php echo e($blog->title); ?>" 
                                                         class="img-thumbnail" 
                                                         style="width: 80px; height: 50px; object-fit: cover;">
                                                <?php else: ?>
                                                    <span class="text-muted">No Image</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($blog->title); ?></td>
                                            <td><?php echo e($blog->category->name ?? 'Uncategorized'); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo e($blog->status == 'published' ? 'success' : 'warning'); ?>">
                                                    <?php echo e(ucfirst($blog->status)); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($blog->published_at ? $blog->published_at->format('M d, Y') : '-'); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.blogs.show', $blog)); ?>" 
                                                       class="btn btn-info btn-sm" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.blogs.edit', $blog)); ?>" 
                                                       class="btn btn-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.blogs.destroy', $blog)); ?>" 
                                                          method="POST" 
                                                          style="display: inline-block;"
                                                          onsubmit="return confirm('Are you sure you want to delete this blog post?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Record Count -->
                        <div class="mt-3">
                            <p class="text-muted" id="record-count">
                                Showing <?php echo e($blogs->count()); ?> of <?php echo e($blogs->total()); ?> records
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No blog posts found</h5>
                            <p class="text-muted">Create your first blog post to get started.</p>
                            <a href="<?php echo e(route('admin.blogs.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Blog Post
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/infinite-scroll.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if($blogs->count() > 0): ?>
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card-body'),
        tableBody: document.getElementById('blogs-table-body'),
        url: '<?php echo e(route("admin.blogs.index")); ?>',
        renderRow: function(blog, rowNumber) {
            const row = document.createElement('tr');

            // Image column
            let imageHtml = '<span class="text-muted">No Image</span>';
            if (blog.featured_image) {
                imageHtml = `<img src="<?php echo e(asset('storage/')); ?>/${blog.featured_image}"
                            alt="${blog.title}"
                            class="img-thumbnail"
                            style="width: 80px; height: 50px; object-fit: cover;">`;
            }

            // Status badge
            const statusClass = blog.status === 'published' ? 'success' : 'warning';

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>${imageHtml}</td>
                <td>${blog.title}</td>
                <td>${blog.category ? blog.category.name : 'No Category'}</td>
                <td><span class="badge badge-${statusClass}">${blog.status.charAt(0).toUpperCase() + blog.status.slice(1)}</span></td>
                <td>${blog.published_at ? new Date(blog.published_at).toLocaleDateString() : 'Not Published'}</td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="<?php echo e(route('admin.blogs.show', '')); ?>/${blog.id}"
                           class="btn btn-info btn-sm" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="<?php echo e(route('admin.blogs.edit', '')); ?>/${blog.id}"
                           class="btn btn-warning btn-sm" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="<?php echo e(route('admin.blogs.destroy', '')); ?>/${blog.id}"
                              method="POST"
                              style="display: inline-block;"
                              onsubmit="return confirm('Are you sure you want to delete this blog?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/blogs/index.blade.php ENDPATH**/ ?>