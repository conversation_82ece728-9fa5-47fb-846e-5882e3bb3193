<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(config('app.supported_locales')[app()->getLocale()]['dir'] ?? 'ltr'); ?>" translate="no">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="google" content="notranslate">
    <meta http-equiv="Content-Language" content="hi">

    <!-- SEO Meta Tags -->
    <?php if(isset($seoMeta)): ?>
        <title><?php echo e($seoMeta->meta_title); ?></title>
        <meta name="description" content="<?php echo e($seoMeta->meta_description); ?>">
        <?php if($seoMeta->meta_keywords): ?>
            <meta name="keywords" content="<?php echo e($seoMeta->meta_keywords); ?>">
        <?php endif; ?>
        <?php if($seoMeta->canonical_url): ?>
            <link rel="canonical" href="<?php echo e($seoMeta->canonical_url); ?>">
        <?php endif; ?>
        
        <!-- Open Graph Meta Tags -->
        <meta property="og:title" content="<?php echo e($seoMeta->og_title ?? $seoMeta->meta_title); ?>">
        <meta property="og:description" content="<?php echo e($seoMeta->og_description ?? $seoMeta->meta_description); ?>">
        <meta property="og:type" content="<?php echo e($seoMeta->og_type ?? 'website'); ?>">
        <meta property="og:url" content="<?php echo e($seoMeta->canonical_url ?? request()->url()); ?>">
        <?php if($seoMeta->og_image): ?>
            <meta property="og:image" content="<?php echo e(asset('storage/' . $seoMeta->og_image)); ?>">
        <?php endif; ?>
        
        <!-- Twitter Card Meta Tags -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="<?php echo e($seoMeta->twitter_title ?? $seoMeta->meta_title); ?>">
        <meta name="twitter:description" content="<?php echo e($seoMeta->twitter_description ?? $seoMeta->meta_description); ?>">
        <?php if($seoMeta->twitter_image): ?>
            <meta name="twitter:image" content="<?php echo e(asset('storage/' . $seoMeta->twitter_image)); ?>">
        <?php endif; ?>
        
        <!-- Additional Meta Tags -->
        <?php if($seoMeta->meta_robots): ?>
            <meta name="robots" content="<?php echo e($seoMeta->meta_robots); ?>">
        <?php endif; ?>
        <?php if($seoMeta->meta_author): ?>
            <meta name="author" content="<?php echo e($seoMeta->meta_author); ?>">
        <?php endif; ?>
    <?php else: ?>
        <title><?php echo e(\App\Models\Setting::get('seo_title', config('app.name', 'NGO Website'))); ?></title>
        <meta name="description" content="<?php echo e(\App\Models\Setting::get('seo_description', 'A comprehensive NGO website with donation management and multilingual support')); ?>">
        <meta name="keywords" content="<?php echo e(\App\Models\Setting::get('seo_keywords', 'NGO, charity, donation, social service')); ?>">
        <meta name="author" content="<?php echo e(\App\Models\Setting::get('site_name', 'NGO Portal')); ?>">
        <meta name="robots" content="index, follow">
    <?php endif; ?>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gov-blue': '<?php echo e(\App\Models\Setting::get("primary_color", "#1e3a8a")); ?>',
                        'gov-navy': '<?php echo e(\App\Models\Setting::get("primary_color", "#1e40af")); ?>',
                        'gov-orange': '<?php echo e(\App\Models\Setting::get("secondary_color", "#ea580c")); ?>',
                        'gov-green': '#16a34a',
                        'gov-saffron': '#ff9933',
                        'gov-white': '#ffffff',
                        'tricolor-saffron': '#ff9933',
                        'tricolor-white': '#ffffff',
                        'tricolor-green': '#138808',
                    },
                    fontFamily: {
                        'hindi': ['Noto Sans Devanagari', 'sans-serif'],
                        'english': ['Inter', 'sans-serif'],
                        'sans': ['Noto Sans Devanagari', 'Inter', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Dynamic CSS Variables -->
    <style>
        :root {
            --primary-color: <?php echo e(\App\Models\Setting::get('primary_color', '#1e3a8a')); ?>;
            --secondary-color: <?php echo e(\App\Models\Setting::get('secondary_color', '#ea580c')); ?>;
        }

        .bg-primary { background-color: var(--primary-color) !important; }
        .text-primary { color: var(--primary-color) !important; }
        .border-primary { border-color: var(--primary-color) !important; }
        .bg-secondary { background-color: var(--secondary-color) !important; }
        .text-secondary { color: var(--secondary-color) !important; }
        .border-secondary { border-color: var(--secondary-color) !important; }
        .hover\:bg-primary:hover { background-color: var(--primary-color) !important; }
        .hover\:text-primary:hover { color: var(--primary-color) !important; }
        .hover\:bg-secondary:hover { background-color: var(--secondary-color) !important; }
        .hover\:text-secondary:hover { color: var(--secondary-color) !important; }
    </style>
    
    <!-- Google Translate handled in header component -->






    
    <!-- Additional Styles -->
    <?php echo $__env->yieldPushContent('styles'); ?>
    
    <style>
        /* Custom styles for Hindi/Devanagari text */
        .font-hindi {
            font-family: 'Noto Sans Devanagari', sans-serif;
        }
        
        /* Government theme colors */
        .bg-tricolor-gradient {
            background: linear-gradient(to bottom, #ff9933 33.33%, #ffffff 33.33%, #ffffff 66.66%, #138808 66.66%);
        }

        /* Footer column alignment */
        @media (min-width: 1024px) {
            .footer-grid {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 3rem;
                align-items: start;
            }

            .footer-column {
                min-height: 300px;
            }

            .footer-column:first-child {
                padding-right: 1rem;
            }

            .footer-column:last-child {
                padding-left: 1rem;
            }
        }
        


        /* Responsive text sizes for Hindi */
        @media (max-width: 640px) {
            .text-hindi-responsive {
                font-size: 0.9rem;
                line-height: 1.4;
            }
        }

        /* Custom Amount Section Animations */
        .custom-amount-section {
            transition: all 0.3s ease-in-out;
            transform: translateY(-10px);
            opacity: 0;
        }

        .custom-amount-section.show {
            transform: translateY(0);
            opacity: 1;
        }

        /* Amount Button Active States */
        .amount-btn {
            transition: all 0.2s ease-in-out;
            position: relative;
            overflow: hidden;
        }

        .amount-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .amount-btn.active {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Custom Amount Input Focus */
        .custom-amount-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: var(--primary-color);
        }

        /* Pulse animation for Others button */
        .amount-btn.others-btn:hover {
            animation: pulse-orange 1s infinite;
        }

        @keyframes pulse-orange {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(234, 88, 12, 0.4);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(234, 88, 12, 0);
            }
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-gov-blue text-white px-4 py-2 rounded-md z-50">
        <?php echo e(app()->getLocale() === 'hi' ? 'मुख्य सामग्री पर जाएं' : 'Skip to main content'); ?>

    </a>

    <!-- Header -->
    <?php echo $__env->make('components.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        <?php echo e($slot); ?>

    </main>

    <!-- Footer -->
    <?php echo $__env->make('components.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
    
    <!-- Toast Notifications -->
    <?php if(session('success')): ?>
        <div id="toast-success" class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <?php echo e(session('success')); ?>

            </div>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div id="toast-error" class="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <?php echo e(session('error')); ?>

            </div>
        </div>
    <?php endif; ?>

    <script>
        // Show toast notifications
        document.addEventListener('DOMContentLoaded', function() {
            const toasts = document.querySelectorAll('[id^="toast-"]');
            toasts.forEach(toast => {
                setTimeout(() => {
                    toast.classList.remove('translate-x-full');
                }, 100);

                setTimeout(() => {
                    toast.classList.add('translate-x-full');
                }, 5000);
            });
        });
    </script>


</body>
</html>
<?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/components/app-layout.blade.php ENDPATH**/ ?>