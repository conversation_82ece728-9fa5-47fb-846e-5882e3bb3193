<!-- Footer -->
<footer class="bg-gov-navy text-white">
    <!-- Main Footer -->
    <div class="container mx-auto px-4 py-12">
        <div class="footer-grid grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
            <!-- About Section -->
            <div class="footer-column lg:pr-4">
                <div class="flex items-center mb-4">
                    <img src="<?php echo e(\App\Models\Setting::getImageUrl('logo')); ?>" alt="<?php echo e(\App\Models\Setting::get('site_name', 'NGO Portal')); ?>" class="h-10 w-10 rounded-full mr-3">
                    <h3 class="text-xl font-bold font-hindi">
                        <?php echo e(\App\Models\Setting::get('site_name', app()->getLocale() === 'hi' ? 'एनजीओ पोर्टल' : 'NGO Portal')); ?>

                    </h3>
                </div>
                <p class="text-gray-300 mb-4 font-hindi text-sm leading-relaxed">
                    <?php echo e(app()->getLocale() === 'hi'
                        ? 'हमारा मिशन समाज के वंचित वर्गों की सेवा करना और उनके जीवन में सकारात्मक बदलाव लाना है।'
                        : 'Our mission is to serve the underprivileged sections of society and bring positive change to their lives.'); ?>

                </p>
                <div class="flex space-x-4">
                    <?php if(\App\Models\Setting::get('facebook_url')): ?>
                        <a href="<?php echo e(\App\Models\Setting::get('facebook_url')); ?>" target="_blank" class="text-gray-300 hover:text-gov-orange transition-colors" title="Facebook">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                    <?php endif; ?>

                    <?php if(\App\Models\Setting::get('twitter_url')): ?>
                        <a href="<?php echo e(\App\Models\Setting::get('twitter_url')); ?>" target="_blank" class="text-gray-300 hover:text-gov-orange transition-colors" title="Twitter">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                    <?php endif; ?>

                    <?php if(\App\Models\Setting::get('instagram_url')): ?>
                        <a href="<?php echo e(\App\Models\Setting::get('instagram_url')); ?>" target="_blank" class="text-gray-300 hover:text-gov-orange transition-colors" title="Instagram">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                        </a>
                    <?php endif; ?>

                    <?php if(\App\Models\Setting::get('youtube_url')): ?>
                        <a href="<?php echo e(\App\Models\Setting::get('youtube_url')); ?>" target="_blank" class="text-gray-300 hover:text-gov-orange transition-colors" title="YouTube">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="footer-column lg:px-4">
                <h3 class="text-lg font-semibold mb-4 font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'त्वरित लिंक' : 'Quick Links'); ?>

                </h3>
                <ul class="space-y-3">
                    <li>
                        <a href="<?php echo e(route('home')); ?>" class="text-gray-300 hover:text-gov-orange transition-colors font-hindi text-sm">
                            <?php echo e(app()->getLocale() === 'hi' ? 'होम' : 'Home'); ?>

                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('about')); ?>" class="text-gray-300 hover:text-gov-orange transition-colors font-hindi text-sm">
                            <?php echo e(app()->getLocale() === 'hi' ? 'हमारे बारे में' : 'About Us'); ?>

                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('causes.index')); ?>" class="text-gray-300 hover:text-gov-orange transition-colors font-hindi text-sm">
                            <?php echo e(app()->getLocale() === 'hi' ? 'कारण' : 'Causes'); ?>

                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('blog.index')); ?>" class="text-gray-300 hover:text-gov-orange transition-colors font-hindi text-sm">
                            <?php echo e(app()->getLocale() === 'hi' ? 'ब्लॉग' : 'Blogs'); ?>

                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('gallery.index')); ?>" class="text-gray-300 hover:text-gov-orange transition-colors font-hindi text-sm">
                            <?php echo e(app()->getLocale() === 'hi' ? 'गैलरी' : 'Gallery'); ?>

                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('contact.index')); ?>" class="text-gray-300 hover:text-gov-orange transition-colors font-hindi text-sm">
                            <?php echo e(app()->getLocale() === 'hi' ? 'संपर्क' : 'Contact'); ?>

                        </a>
                    </li>
                </ul>
            </div>


            <!-- Contact Info -->
            <div class="footer-column lg:pl-4">
                <h3 class="text-lg font-semibold mb-4 font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'संपर्क जानकारी' : 'Contact Info'); ?>

                </h3>
                <div class="space-y-4">
                    <div class="flex items-start">
                        <svg class="w-4 h-4 mr-3 mt-1 text-gov-orange flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-300 text-sm font-hindi leading-relaxed">
                            <?php echo e(\App\Models\Setting::get('contact_address', '123 मुख्य मार्ग, नई दिल्ली, भारत - 110001')); ?>

                        </span>
                    </div>

                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-3 text-gov-orange flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                        <span class="text-gray-300 text-sm"><?php echo e(\App\Models\Setting::get('contact_phone', '+91 98765 43210')); ?></span>
                    </div>

                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-3 text-gov-orange flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                        <span class="text-gray-300 text-sm"><?php echo e(\App\Models\Setting::get('contact_email', '<EMAIL>')); ?></span>
                    </div>

                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-3 text-gov-orange flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-300 text-sm font-hindi">
                            <?php if(app()->getLocale() === 'hi'): ?>
                                सोमवार - शुक्रवार: 9:00 - 18:00
                            <?php else: ?>
                                <?php echo e(\App\Models\Setting::get('office_hours_weekdays', 'Mon - Fri: 9:00 - 18:00')); ?>

                            <?php endif; ?>
                        </span>
                    </div>
                </div>

                <!-- Newsletter Signup -->
                <div class="mt-6">
                    <h4 class="text-sm font-semibold mb-3 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'न्यूज़लेटर सब्सक्राइब करें' : 'Subscribe Newsletter'); ?>

                    </h4>
                    <form class="flex max-w-sm">
                        <input type="email" placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'आपका ईमेल' : 'Your email'); ?>" class="flex-1 px-3 py-2 text-sm text-gray-900 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-gov-orange">
                        <button type="submit" class="bg-gov-orange hover:bg-orange-600 px-3 py-2 rounded-r-lg transition-colors">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Footer -->
    <div class="border-t border-gray-700">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="text-gray-300 text-sm mb-4 md:mb-0 font-hindi">
                    © <?php echo e(date('Y')); ?> <?php echo e(\App\Models\Setting::get('site_name', 'NGO Portal')); ?><?php echo e(app()->getLocale() === 'hi' ? '। सभी अधिकार सुरक्षित।' : '. All rights reserved.'); ?>

                </div>
                
                <div class="flex items-center space-x-6 text-sm">
                    <a href="#" class="text-gray-300 hover:text-gov-orange transition-colors font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'गोपनीयता नीति' : 'Privacy Policy'); ?>

                    </a>
                    <a href="#" class="text-gray-300 hover:text-gov-orange transition-colors font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'नियम और शर्तें' : 'Terms & Conditions'); ?>

                    </a>
                    <a href="#" class="text-gray-300 hover:text-gov-orange transition-colors font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'कुकी नीति' : 'Cookie Policy'); ?>

                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Tricolor Bottom Border -->
    <div class="bg-tricolor-gradient h-1"></div>
</footer>

<!-- Back to Top Button -->
<button id="back-to-top" class="fixed bottom-6 right-6 bg-gov-orange hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 invisible">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
</button>

<script>
    // Back to top functionality
    window.addEventListener('scroll', function() {
        const backToTop = document.getElementById('back-to-top');
        if (window.pageYOffset > 300) {
            backToTop.classList.remove('opacity-0', 'invisible');
            backToTop.classList.add('opacity-100', 'visible');
        } else {
            backToTop.classList.add('opacity-0', 'invisible');
            backToTop.classList.remove('opacity-100', 'visible');
        }
    });

    document.getElementById('back-to-top').addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
</script>
<?php /**PATH D:\xampp8.2.3\htdocs\ngonew\resources\views/components/footer.blade.php ENDPATH**/ ?>