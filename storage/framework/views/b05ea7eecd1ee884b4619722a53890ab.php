<?php $__env->startSection('title', 'Donation Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Donation Management</h1>
        <a href="<?php echo e(route('admin.donations.dashboard')); ?>" class="btn btn-primary">
            <i class="fas fa-chart-bar mr-2"></i>Dashboard
        </a>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.donations.index')); ?>">
                <div class="row">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-control">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                            <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Completed</option>
                            <option value="failed" <?php echo e(request('status') === 'failed' ? 'selected' : ''); ?>>Failed</option>
                            <option value="refunded" <?php echo e(request('status') === 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select name="payment_method" id="payment_method" class="form-control">
                            <option value="">All Methods</option>
                            <option value="qr_code" <?php echo e(request('payment_method') === 'qr_code' ? 'selected' : ''); ?>>QR Code</option>
                            <option value="gateway" <?php echo e(request('payment_method') === 'gateway' ? 'selected' : ''); ?>>Gateway</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="cause_id" class="form-label">Cause</label>
                        <select name="cause_id" id="cause_id" class="form-control">
                            <option value="">All Causes</option>
                            <?php $__currentLoopData = $causes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cause): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($cause->id); ?>" <?php echo e(request('cause_id') == $cause->id ? 'selected' : ''); ?>>
                                    <?php echo e($cause->title); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <div class="input-group">
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Name, Email, Transaction ID" value="<?php echo e(request('search')); ?>">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-secondary">Clear Filters</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Donations Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title mb-0">Donations (<?php echo e($donations->total()); ?>)</h3>
        </div>
        <div class="card-body p-0">
            <?php if($donations->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Donor</th>
                                <th>Amount</th>
                                <th>Cause</th>
                                <th>Payment Method</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $donations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $donation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($donation->id); ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($donation->is_anonymous ? 'Anonymous' : $donation->donor_name); ?></strong>
                                            <?php if(!$donation->is_anonymous): ?>
                                                <br><small class="text-muted"><?php echo e($donation->donor_email); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>₹<?php echo e(number_format($donation->amount, 2)); ?></strong>
                                    </td>
                                    <td>
                                        <?php if($donation->cause): ?>
                                            <span class="badge badge-info"><?php echo e($donation->cause->title); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">General</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($donation->payment_method === 'qr_code'): ?>
                                            <span class="badge badge-warning">QR Code</span>
                                        <?php else: ?>
                                            <span class="badge badge-primary"><?php echo e(ucfirst($donation->payment_gateway ?? 'Gateway')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php switch($donation->status):
                                            case ('pending'): ?>
                                                <span class="badge badge-warning">Pending</span>
                                                <?php break; ?>
                                            <?php case ('completed'): ?>
                                                <span class="badge badge-success">Completed</span>
                                                <?php break; ?>
                                            <?php case ('failed'): ?>
                                                <span class="badge badge-danger">Failed</span>
                                                <?php break; ?>
                                            <?php case ('refunded'): ?>
                                                <span class="badge badge-secondary">Refunded</span>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </td>
                                    <td>
                                        <small><?php echo e($donation->created_at->format('M d, Y H:i')); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.donations.show', $donation)); ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if($donation->status === 'pending'): ?>
                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                        onclick="updateStatus(<?php echo e($donation->id); ?>, 'completed')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="updateStatus(<?php echo e($donation->id); ?>, 'failed')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="card-footer">
                    <?php echo e($donations->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <p class="text-muted">No donations found.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function updateStatus(donationId, status) {
    if (confirm('Are you sure you want to update this donation status?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/donations/${donationId}/status`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'PUT';
        
        const statusField = document.createElement('input');
        statusField.type = 'hidden';
        statusField.name = 'status';
        statusField.value = status;
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        form.appendChild(statusField);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/donations/index.blade.php ENDPATH**/ ?>