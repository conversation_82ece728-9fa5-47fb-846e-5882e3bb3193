<?php $__env->startSection('title', 'Donation Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Donation Management</h1>
        <a href="<?php echo e(route('admin.donations.dashboard')); ?>" class="btn btn-primary">
            <i class="fas fa-chart-bar mr-2"></i>Dashboard
        </a>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.donations.index')); ?>">
                <div class="row">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-control">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                            <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Completed</option>
                            <option value="failed" <?php echo e(request('status') === 'failed' ? 'selected' : ''); ?>>Failed</option>
                            <option value="refunded" <?php echo e(request('status') === 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select name="payment_method" id="payment_method" class="form-control">
                            <option value="">All Methods</option>
                            <option value="qr_code" <?php echo e(request('payment_method') === 'qr_code' ? 'selected' : ''); ?>>QR Code</option>
                            <option value="gateway" <?php echo e(request('payment_method') === 'gateway' ? 'selected' : ''); ?>>Gateway</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="cause_id" class="form-label">Cause</label>
                        <select name="cause_id" id="cause_id" class="form-control">
                            <option value="">All Causes</option>
                            <?php $__currentLoopData = $causes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cause): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($cause->id); ?>" <?php echo e(request('cause_id') == $cause->id ? 'selected' : ''); ?>>
                                    <?php echo e($cause->title); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <div class="input-group">
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Name, Email, Transaction ID" value="<?php echo e(request('search')); ?>">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-secondary">Clear Filters</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Donations Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title mb-0">Donations (<?php echo e($donations->total()); ?>)</h3>
        </div>
        <div class="card-body p-0">
            <?php if($donations->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped mb-0">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>Donor</th>
                                <th>Amount</th>
                                <th>Cause</th>
                                <th>Payment Method</th>
                                <th>Transaction Photo</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="donations-table-body">
                            <?php $__currentLoopData = $donations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $donation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e(($donations->currentPage() - 1) * $donations->perPage() + $index + 1); ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($donation->is_anonymous ? 'Anonymous' : $donation->donor_name); ?></strong>
                                            <?php if(!$donation->is_anonymous && $donation->donor_email): ?>
                                                <br><small class="text-muted"><?php echo e($donation->donor_email); ?></small>
                                            <?php endif; ?>
                                            <?php if($donation->donor_phone): ?>
                                                <br><small class="text-muted"><i class="fas fa-phone"></i> <?php echo e($donation->donor_phone); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <strong class="text-success">₹<?php echo e(number_format($donation->amount, 2)); ?></strong>
                                    </td>
                                    <td>
                                        <?php if($donation->cause): ?>
                                            <span class="badge badge-info"><?php echo e(Str::limit($donation->cause->title, 20)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">General</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php switch($donation->payment_method):
                                            case ('qr_code'): ?>
                                                <span class="badge badge-warning"><i class="fas fa-qrcode"></i> QR Code</span>
                                                <?php break; ?>
                                            <?php case ('gateway'): ?>
                                                <span class="badge badge-primary"><i class="fas fa-credit-card"></i> Gateway</span>
                                                <?php break; ?>
                                            <?php case ('bank_transfer'): ?>
                                                <span class="badge badge-info"><i class="fas fa-university"></i> Bank Transfer</span>
                                                <?php break; ?>
                                            <?php case ('cash'): ?>
                                                <span class="badge badge-success"><i class="fas fa-money-bill"></i> Cash</span>
                                                <?php break; ?>
                                            <?php default: ?>
                                                <span class="badge badge-secondary"><?php echo e(ucfirst($donation->payment_method)); ?></span>
                                        <?php endswitch; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if($donation->payment_screenshot): ?>
                                            <img src="<?php echo e($donation->payment_screenshot_url); ?>"
                                                 alt="Transaction Screenshot"
                                                 class="img-thumbnail"
                                                 style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
                                                 onclick="openImageModal('<?php echo e($donation->payment_screenshot_url); ?>', '<?php echo e($donation->donor_name); ?>')">
                                        <?php else: ?>
                                            <span class="text-muted"><i class="fas fa-image-slash"></i> No Image</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php switch($donation->status):
                                            case ('pending'): ?>
                                                <span class="badge badge-warning"><i class="fas fa-clock"></i> Pending</span>
                                                <?php break; ?>
                                            <?php case ('completed'): ?>
                                                <span class="badge badge-success"><i class="fas fa-check"></i> Completed</span>
                                                <?php break; ?>
                                            <?php case ('failed'): ?>
                                                <span class="badge badge-danger"><i class="fas fa-times"></i> Failed</span>
                                                <?php break; ?>
                                            <?php case ('refunded'): ?>
                                                <span class="badge badge-secondary"><i class="fas fa-undo"></i> Refunded</span>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </td>
                                    <td>
                                        <small><?php echo e($donation->created_at->format('M d, Y')); ?><br><?php echo e($donation->created_at->format('H:i')); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.donations.show', $donation)); ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if($donation->status === 'pending'): ?>
                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                        onclick="updateStatus(<?php echo e($donation->id); ?>, 'completed')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="updateStatus(<?php echo e($donation->id); ?>, 'failed')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <p class="text-muted">No donations found.</p>
                </div>
            <?php endif; ?>

            <?php if($donations->count() > 0): ?>
                <!-- Record Count -->
                <div class="card-footer">
                    <p class="text-muted mb-0" id="record-count">
                        Showing <?php echo e($donations->count()); ?> of <?php echo e($donations->total()); ?> records
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function updateStatus(donationId, status) {
    if (confirm('Are you sure you want to update this donation status?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/donations/${donationId}/status`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'PUT';
        
        const statusField = document.createElement('input');
        statusField.type = 'hidden';
        statusField.name = 'status';
        statusField.value = status;
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        form.appendChild(statusField);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function openImageModal(src, donorName) {
    document.getElementById('modalImage').src = src;
    document.getElementById('modalTitle').textContent = 'Transaction Screenshot - ' + donorName;
    $('#imageModal').modal('show');
}
</script>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Transaction Screenshot</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Transaction Screenshot" class="img-fluid">
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/infinite-scroll.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if($donations->count() > 0): ?>
    // Get current URL with filters
    const currentUrl = new URL(window.location.href);
    const baseUrl = '<?php echo e(route("admin.donations.index")); ?>';
    const queryParams = currentUrl.search;

    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card'),
        tableBody: document.getElementById('donations-table-body'),
        url: baseUrl + queryParams,
        renderRow: function(donation, rowNumber) {
            const row = document.createElement('tr');

            // Format donor info
            let donorInfo = donation.is_anonymous ? 'Anonymous' : donation.donor_name;
            if (!donation.is_anonymous && donation.donor_email) {
                donorInfo += `<br><small class="text-muted">${donation.donor_email}</small>`;
            }
            if (donation.donor_phone) {
                donorInfo += `<br><small class="text-muted"><i class="fas fa-phone"></i> ${donation.donor_phone}</small>`;
            }

            // Format cause
            let causeInfo = donation.cause ?
                `<span class="badge badge-info">${donation.cause.title.substring(0, 20)}${donation.cause.title.length > 20 ? '...' : ''}</span>` :
                '<span class="text-muted">General</span>';

            // Format payment method
            let paymentMethod = '';
            switch(donation.payment_method) {
                case 'qr_code':
                    paymentMethod = '<span class="badge badge-warning"><i class="fas fa-qrcode"></i> QR Code</span>';
                    break;
                case 'gateway':
                    paymentMethod = '<span class="badge badge-primary"><i class="fas fa-credit-card"></i> Gateway</span>';
                    break;
                case 'bank_transfer':
                    paymentMethod = '<span class="badge badge-info"><i class="fas fa-university"></i> Bank Transfer</span>';
                    break;
                case 'cash':
                    paymentMethod = '<span class="badge badge-success"><i class="fas fa-money-bill"></i> Cash</span>';
                    break;
                default:
                    paymentMethod = `<span class="badge badge-secondary">${donation.payment_method.charAt(0).toUpperCase() + donation.payment_method.slice(1)}</span>`;
            }

            // Format transaction photo
            let transactionPhoto = '<span class="text-muted"><i class="fas fa-image-slash"></i> No Image</span>';
            if (donation.payment_screenshot) {
                const imageUrl = `<?php echo e(asset('storage/')); ?>/${donation.payment_screenshot}`;
                transactionPhoto = `<img src="${imageUrl}"
                                   alt="Transaction Screenshot"
                                   class="img-thumbnail"
                                   style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
                                   onclick="openImageModal('${imageUrl}', '${donation.donor_name}')">`;
            }

            // Format status
            let statusBadge = '';
            switch(donation.status) {
                case 'pending':
                    statusBadge = '<span class="badge badge-warning"><i class="fas fa-clock"></i> Pending</span>';
                    break;
                case 'completed':
                    statusBadge = '<span class="badge badge-success"><i class="fas fa-check"></i> Completed</span>';
                    break;
                case 'failed':
                    statusBadge = '<span class="badge badge-danger"><i class="fas fa-times"></i> Failed</span>';
                    break;
                case 'refunded':
                    statusBadge = '<span class="badge badge-secondary"><i class="fas fa-undo"></i> Refunded</span>';
                    break;
            }

            // Format date
            const createdAt = new Date(donation.created_at);
            const dateStr = createdAt.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
            const timeStr = createdAt.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            // Action buttons
            let actionButtons = `<a href="<?php echo e(route('admin.donations.show', '')); ?>/${donation.id}"
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>`;

            if (donation.status === 'pending') {
                actionButtons += `
                    <button type="button" class="btn btn-sm btn-outline-success"
                            onclick="updateStatus(${donation.id}, 'completed')">
                        <i class="fas fa-check"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="updateStatus(${donation.id}, 'failed')">
                        <i class="fas fa-times"></i>
                    </button>`;
            }

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>
                    <div>${donorInfo}</div>
                </td>
                <td>
                    <strong class="text-success">₹${parseFloat(donation.amount).toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</strong>
                </td>
                <td>${causeInfo}</td>
                <td>${paymentMethod}</td>
                <td class="text-center">${transactionPhoto}</td>
                <td>${statusBadge}</td>
                <td>
                    <small>${dateStr}<br>${timeStr}</small>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        ${actionButtons}
                    </div>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/donations/index.blade.php ENDPATH**/ ?>