<?php if (isset($component)) { $__componentOriginal4619374cef299e94fd7263111d0abc69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4619374cef299e94fd7263111d0abc69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php $__env->startSection('title', app()->getLocale() === 'hi' ? 'दान करें - ' . $cause->title : 'Donate - ' . $cause->title); ?>
    <?php $__env->startSection('meta_description', Str::limit($cause->description, 160)); ?>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="relative container mx-auto px-4 text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 font-hindi">
                <?php echo e(app()->getLocale() === 'hi' ? 'दान करें' : 'Donate Now'); ?>

            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                <?php echo e($cause->title); ?>

            </p>
        </div>
    </section>

    <!-- Donation Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Cause Details -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <?php if($cause->image): ?>
                        <img src="<?php echo e(asset('storage/' . $cause->image)); ?>" alt="<?php echo e($cause->title); ?>" class="w-full h-64 object-cover">
                        <?php endif; ?>
                        
                        <div class="p-6">
                            <h2 class="text-2xl font-bold mb-4 text-gov-navy font-hindi"><?php echo e($cause->title); ?></h2>
                            <p class="text-gray-600 mb-6"><?php echo e($cause->description); ?></p>
                            
                            <!-- Progress Bar -->
                            <?php
                                $percentage = $cause->goal_amount > 0 ? min(($cause->raised_amount / $cause->goal_amount) * 100, 100) : 0;
                            ?>
                            <div class="mb-6">
                                <div class="flex justify-between text-sm text-gray-600 mb-2">
                                    <span class="font-hindi"><?php echo e(app()->getLocale() === 'hi' ? 'जुटाई गई राशि' : 'Raised'); ?></span>
                                    <span><?php echo e(number_format($percentage, 1)); ?>%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="bg-gov-orange h-3 rounded-full transition-all duration-300" style="width: <?php echo e($percentage); ?>%"></div>
                                </div>
                                <div class="flex justify-between text-lg font-semibold mt-2">
                                    <span class="text-gov-orange">₹<?php echo e(number_format($cause->raised_amount)); ?></span>
                                    <span class="text-gray-600">₹<?php echo e(number_format($cause->goal_amount)); ?></span>
                                </div>
                            </div>
                            
                            <?php if($cause->content): ?>
                            <div class="prose max-w-none">
                                <?php echo $cause->content; ?>

                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Donation Form -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="bg-gov-blue text-white p-6">
                            <h3 class="text-2xl font-bold font-hindi">
                                <?php echo e(app()->getLocale() === 'hi' ? 'दान का विवरण' : 'Donation Details'); ?>

                            </h3>
                        </div>
                        
                        <form action="<?php echo e(route('donate.process')); ?>" method="POST" class="p-6">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="cause_id" value="<?php echo e($cause->id); ?>">
                            
                            <!-- Amount Selection -->
                            <div class="mb-6">
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'दान की राशि (₹)' : 'Donation Amount (₹)'); ?>

                                </label>
                                
                                <!-- Quick Amount Buttons -->
                                <div class="grid grid-cols-2 gap-3 mb-4">
                                    <button type="button" onclick="setAmount(500)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                        ₹500
                                    </button>
                                    <button type="button" onclick="setAmount(1000)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                        ₹1,000
                                    </button>
                                    <button type="button" onclick="setAmount(2500)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                        ₹2,500
                                    </button>
                                    <button type="button" onclick="setAmount(5000)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                        ₹5,000
                                    </button>
                                </div>
                                
                                <input type="number" name="amount" id="amount" min="1" step="0.01" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'कस्टम राशि दर्ज करें' : 'Enter custom amount'); ?>">
                            </div>

                            <!-- Donor Information -->
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'पूरा नाम' : 'Full Name'); ?> *
                                </label>
                                <input type="text" name="donor_name" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'आपका नाम' : 'Your Name'); ?>">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'ईमेल पता' : 'Email Address'); ?> *
                                </label>
                                <input type="email" name="donor_email" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'आपका ईमेल' : 'Your Email'); ?>">
                            </div>

                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'फोन नंबर' : 'Phone Number'); ?>

                                </label>
                                <input type="tel" name="donor_phone"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                       placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'आपका फोन नंबर' : 'Your Phone Number'); ?>">
                            </div>

                            <!-- Message -->
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'संदेश (वैकल्पिक)' : 'Message (Optional)'); ?>

                                </label>
                                <textarea name="message" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                                          placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'आपका संदेश' : 'Your message'); ?>"></textarea>
                            </div>

                            <!-- Anonymous Donation -->
                            <div class="mb-6">
                                <label class="flex items-center">
                                    <input type="checkbox" name="is_anonymous" value="1" class="mr-2">
                                    <span class="text-gray-700 font-hindi">
                                        <?php echo e(app()->getLocale() === 'hi' ? 'गुमनाम दान' : 'Anonymous donation'); ?>

                                    </span>
                                </label>
                            </div>

                            <!-- QR Code Payment Option -->
                            <?php
                                $paymentQrCode = \App\Models\Setting::get('payment_qr_code');
                            ?>
                            <?php if($paymentQrCode): ?>
                            <div class="mb-6 p-4 bg-gray-50 rounded-lg border">
                                <h4 class="text-lg font-bold mb-3 text-gov-navy font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'QR कोड से भुगतान करें' : 'Pay with QR Code'); ?>

                                </h4>
                                <div class="text-center">
                                    <img src="<?php echo e(\App\Models\Setting::getImageUrl('payment_qr_code')); ?>"
                                         alt="<?php echo e(app()->getLocale() === 'hi' ? 'भुगतान QR कोड' : 'Payment QR Code'); ?>"
                                         class="mx-auto mb-3 max-w-xs border rounded-lg shadow-sm">
                                    <p class="text-sm text-gray-600 font-hindi">
                                        <?php echo e(app()->getLocale() === 'hi' ? 'अपने UPI ऐप से QR कोड स्कैन करें और भुगतान करें' : 'Scan the QR code with your UPI app to make payment'); ?>

                                    </p>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Submit Button -->
                            <button type="submit" class="w-full bg-gov-orange hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                                <?php echo e(app()->getLocale() === 'hi' ? 'दान की जानकारी सबमिट करें' : 'Submit Donation Details'); ?>

                            </button>

                            <p class="text-center text-sm text-gray-600 mt-3 font-hindi">
                                <?php echo e(app()->getLocale() === 'hi' ? 'सबमिट करने के बाद QR कोड से भुगतान करें' : 'After submitting, pay using the QR code above'); ?>

                            </p>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
    function setAmount(amount) {
        document.querySelector('input[name="amount"]').value = amount;
    }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $attributes = $__attributesOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__attributesOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $component = $__componentOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__componentOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/donate-cause.blade.php ENDPATH**/ ?>