<?php if (isset($component)) { $__componentOriginal4619374cef299e94fd7263111d0abc69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4619374cef299e94fd7263111d0abc69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php $__env->startSection('title', app()->getLocale() === 'hi' ? 'दान करें - ' . $cause->title : 'Donate - ' . $cause->title); ?>
    <?php $__env->startSection('meta_description', Str::limit($cause->description, 160)); ?>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="relative container mx-auto px-4 text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 font-hindi">
                <?php echo e(app()->getLocale() === 'hi' ? 'दान करें' : 'Donate Now'); ?>

            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                <?php echo e($cause->title); ?>

            </p>
        </div>
    </section>

    <!-- Donation Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Cause Details -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <?php if($cause->image): ?>
                        <img src="<?php echo e(asset('storage/' . $cause->image)); ?>" alt="<?php echo e($cause->title); ?>" class="w-full h-64 object-cover">
                        <?php endif; ?>
                        
                        <div class="p-6">
                            <h2 class="text-2xl font-bold mb-4 text-gov-navy font-hindi"><?php echo e($cause->title); ?></h2>
                            <p class="text-gray-600 mb-6"><?php echo e($cause->description); ?></p>
                            
                            <!-- Progress Bar -->
                            <?php
                                $percentage = $cause->goal_amount > 0 ? min(($cause->raised_amount / $cause->goal_amount) * 100, 100) : 0;
                            ?>
                            <div class="mb-6">
                                <div class="flex justify-between text-sm text-gray-600 mb-2">
                                    <span class="font-hindi"><?php echo e(app()->getLocale() === 'hi' ? 'जुटाई गई राशि' : 'Raised'); ?></span>
                                    <span><?php echo e(number_format($percentage, 1)); ?>%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="bg-gov-orange h-3 rounded-full transition-all duration-300" style="width: <?php echo e($percentage); ?>%"></div>
                                </div>
                                <div class="flex justify-between text-lg font-semibold mt-2">
                                    <span class="text-gov-orange">₹<?php echo e(number_format($cause->raised_amount)); ?></span>
                                    <span class="text-gray-600">₹<?php echo e(number_format($cause->goal_amount)); ?></span>
                                </div>
                            </div>
                            
                            <?php if($cause->content): ?>
                            <div class="prose max-w-none">
                                <?php echo $cause->content; ?>

                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Donation Form -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="bg-gov-blue text-white p-6">
                            <h3 class="text-2xl font-bold font-hindi">
                                <?php echo e(app()->getLocale() === 'hi' ? 'दान का विवरण' : 'Donation Details'); ?>

                            </h3>
                        </div>
                        
                        <div class="p-6 text-center">
                            <div class="mb-6">
                                <i class="fas fa-heart fa-3x text-gov-orange mb-4"></i>
                                <h3 class="text-xl font-bold text-gov-navy mb-2 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'इस कारण के लिए दान करें' : 'Support This Cause'); ?>

                                </h3>
                                <p class="text-gray-600 font-hindi">
                                    <?php echo e(app()->getLocale() === 'hi' ? 'आपका योगदान इस महत्वपूर्ण कारण के लिए बहुत मायने रखता है।' : 'Your contribution makes a significant difference to this important cause.'); ?>

                                </p>
                            </div>

                            <!-- Quick Amount Buttons -->
                            <div class="grid grid-cols-2 gap-3 mb-4">
                                <button type="button" onclick="openDonationModalWithAmount(<?php echo e($cause->id); ?>, 500)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹500
                                </button>
                                <button type="button" onclick="openDonationModalWithAmount(<?php echo e($cause->id); ?>, 1000)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹1,000
                                </button>
                                <button type="button" onclick="openDonationModalWithAmount(<?php echo e($cause->id); ?>, 2500)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹2,500
                                </button>
                                <button type="button" onclick="openDonationModalWithAmount(<?php echo e($cause->id); ?>, 5000)" class="amount-btn bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    ₹5,000
                                </button>
                                <button type="button" onclick="openCustomAmountModal(<?php echo e($cause->id); ?>)" class="amount-btn others-btn bg-gray-100 hover:bg-gov-orange hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-edit mr-1"></i>
                                    <?php echo e(app()->getLocale() === 'hi' ? 'अन्य राशि' : 'Others'); ?>

                                </button>
                            </div>

                            <!-- Custom Amount Input (Initially Hidden) -->
                            <div id="customAmountSection" class="custom-amount-section mb-4 hidden">
                                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                    <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                                        <i class="fas fa-rupee-sign text-gov-orange mr-1"></i>
                                        <?php echo e(app()->getLocale() === 'hi' ? 'कस्टम राशि दर्ज करें' : 'Enter Custom Amount'); ?>

                                    </label>
                                    <div class="flex gap-2">
                                        <input type="number" id="customAmount" min="1" step="0.01"
                                               class="custom-amount-input flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-orange"
                                               placeholder="<?php echo e(app()->getLocale() === 'hi' ? '₹ राशि दर्ज करें' : '₹ Enter amount'); ?>">
                                        <button type="button" onclick="proceedWithCustomAmount(<?php echo e($cause->id); ?>)"
                                                class="bg-gov-orange hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors font-bold">
                                            <i class="fas fa-arrow-right mr-1"></i>
                                            <?php echo e(app()->getLocale() === 'hi' ? 'आगे बढ़ें' : 'Proceed'); ?>

                                        </button>
                                    </div>
                                    <small class="text-gray-600 mt-1 block font-hindi">
                                        <?php echo e(app()->getLocale() === 'hi' ? 'न्यूनतम राशि ₹1' : 'Minimum amount ₹1'); ?>

                                    </small>
                                </div>
                            </div>

                            <!-- Main Donate Button -->
                            <button type="button" onclick="openDonationModal(<?php echo e($cause->id); ?>)" class="w-full bg-gov-orange hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                                <?php echo e(app()->getLocale() === 'hi' ? 'अभी दान करें' : 'Donate Now'); ?>

                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Include Donation Modal -->
    <?php echo $__env->make('components.donation-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <script>
    function setAmount(amount) {
        document.querySelector('input[name="amount"]').value = amount;
    }

    function openDonationModalWithAmount(causeId, amount) {
        // Update button states
        updateAmountButtonStates(amount);
        openDonationModal(causeId);
        // Wait for modal to open, then set amount
        setTimeout(() => {
            document.getElementById('modal_amount').value = amount;
        }, 100);
    }

    function openCustomAmountModal(causeId) {
        const customSection = document.getElementById('customAmountSection');
        const customInput = document.getElementById('customAmount');

        // Show custom amount section with animation
        customSection.classList.remove('hidden');
        setTimeout(() => {
            customSection.classList.add('show');
        }, 10);

        // Focus on input after animation
        setTimeout(() => {
            customInput.focus();
        }, 300);

        // Update button states
        updateAmountButtonStates('custom');

        // Hide custom amount section when clicking other amount buttons
        const amountButtons = document.querySelectorAll('.amount-btn');
        amountButtons.forEach((btn, index) => {
            if (index < amountButtons.length - 1) { // Not the "Others" button
                btn.addEventListener('click', () => {
                    hideCustomAmountSection();
                });
            }
        });
    }

    function hideCustomAmountSection() {
        const customSection = document.getElementById('customAmountSection');
        const customInput = document.getElementById('customAmount');

        customSection.classList.remove('show');
        setTimeout(() => {
            customSection.classList.add('hidden');
            customInput.value = '';
        }, 300);
    }

    function proceedWithCustomAmount(causeId) {
        const customAmount = document.getElementById('customAmount').value;

        if (!customAmount || parseFloat(customAmount) <= 0) {
            alert('<?php echo e(app()->getLocale() === 'hi' ? 'कृपया एक वैध राशि दर्ज करें' : 'Please enter a valid amount'); ?>');
            document.getElementById('customAmount').focus();
            return;
        }

        openDonationModal(causeId, customAmount);
        // Wait for modal to open, then set amount
        setTimeout(() => {
            document.getElementById('modal_amount').value = customAmount;
        }, 100);
    }

    function updateAmountButtonStates(selectedAmount) {
        const buttons = document.querySelectorAll('.amount-btn');
        buttons.forEach(button => {
            button.classList.remove('bg-gov-blue', 'text-white', 'bg-gov-orange', 'active');
            button.classList.add('bg-gray-100');
        });

        if (selectedAmount === 'custom') {
            const othersBtn = buttons[buttons.length - 1]; // Last button is "Others"
            othersBtn.classList.remove('bg-gray-100');
            othersBtn.classList.add('bg-gov-orange', 'text-white', 'active');
        } else {
            buttons.forEach(button => {
                const buttonText = button.textContent.trim();
                if (buttonText.includes(selectedAmount.toString())) {
                    button.classList.remove('bg-gray-100');
                    button.classList.add('bg-gov-blue', 'text-white', 'active');
                }
            });
        }
    }

    // Allow Enter key to proceed with custom amount
    document.addEventListener('DOMContentLoaded', function() {
        const customAmountInput = document.getElementById('customAmount');
        if (customAmountInput) {
            customAmountInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const causeId = <?php echo e($cause->id); ?>;
                    proceedWithCustomAmount(causeId);
                }
            });
        }
    });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $attributes = $__attributesOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__attributesOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $component = $__componentOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__componentOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/donate-cause.blade.php ENDPATH**/ ?>