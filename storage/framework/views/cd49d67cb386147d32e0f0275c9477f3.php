<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo $__env->yieldContent('title', 'Admin Panel'); ?> | <?php echo e(config('app.name')); ?></title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Bootstrap 4 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.2/css/bootstrap.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/css/adminlte.min.css">

    <!-- Custom Elegant Admin Styles -->
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #2563eb;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: var(--text-primary);
        }

        /* Elegant Sidebar */
        .main-sidebar {
            background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
            border-right: 1px solid var(--border-color);
            box-shadow: var(--shadow-lg);
        }

        .sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link {
            color: var(--text-primary);
            border-radius: 12px;
            margin: 2px 8px;
            padding: 12px 16px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
        }

        .sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .brand-link {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-bottom: 1px solid var(--border-color);
            padding: 1rem;
            color: white !important;
        }

        .brand-text {
            color: white !important;
            font-weight: 600;
        }

        /* Elegant Header */
        .main-header.navbar {
            background: white;
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .navbar-light .navbar-nav .nav-link {
            color: var(--text-primary);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-light .navbar-nav .nav-link:hover {
            color: var(--primary-color);
        }

        /* Content Area */
        .content-wrapper {
            background-color: #f8fafc;
            min-height: calc(100vh - 57px);
        }

        /* Elegant Cards */
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            background: white;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc, #ffffff);
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0 !important;
            padding: 1.25rem;
        }

        .card-title {
            color: var(--text-primary);
            font-weight: 600;
            margin: 0;
        }

        /* Modern Stats Cards */
        .small-box {
            border-radius: 16px;
            border: none;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
        }

        .small-box:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .small-box .inner {
            padding: 1.5rem;
        }

        .small-box .inner h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .small-box .inner p {
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.9;
        }

        .small-box .icon {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 3rem;
            opacity: 0.3;
        }

        /* Color Schemes */
        .bg-info {
            background: linear-gradient(135deg, var(--info-color), #0891b2) !important;
        }

        .bg-success {
            background: linear-gradient(135deg, var(--success-color), #059669) !important;
        }

        .bg-warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706) !important;
        }

        .bg-secondary {
            background: linear-gradient(135deg, var(--dark-color), #374151) !important;
        }

        /* Footer */
        .main-footer {
            background: white;
            border-top: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 1rem;
        }

        /* Breadcrumb */
        .content-header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 1rem;
        }

        .breadcrumb {
            background: transparent;
            margin: 0;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            color: var(--text-secondary);
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), #1d4ed8);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-sidebar {
                box-shadow: none;
            }

            .small-box .inner h3 {
                font-size: 2rem;
            }

            .small-box .icon {
                font-size: 2.5rem;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content-wrapper {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

    <!-- Preloader -->
    <div class="preloader flex-column justify-content-center align-items-center">
        <?php
            $siteLogo = \App\Models\Setting::getImageUrl('logo');
        ?>
        <?php if($siteLogo): ?>
            <img class="animation__shake" src="<?php echo e($siteLogo); ?>" alt="Logo" height="60" width="60" style="object-fit: contain;">
        <?php else: ?>
            <i class="fas fa-cogs animation__shake fa-3x text-primary"></i>
        <?php endif; ?>
    </div>

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="<?php echo e(route('admin.dashboard')); ?>" class="nav-link">Home</a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="<?php echo e(url('/')); ?>" class="nav-link" target="_blank">Visit Site</a>
            </li>
        </ul>

        <!-- Right navbar links -->
        <ul class="navbar-nav ml-auto">
            <!-- User Dropdown Menu -->
            <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#">
                    <i class="far fa-user"></i>
                    <?php echo e(Auth::user()->name); ?>

                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-user mr-2"></i> Profile
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="<?php echo e(route('logout')); ?>" class="dropdown-item"
                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                    </a>
                    <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                        <?php echo csrf_field(); ?>
                    </form>
                </div>
            </li>
        </ul>
    </nav>

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary">
        <!-- Brand Logo -->
        <a href="<?php echo e(route('admin.dashboard')); ?>" class="brand-link">
            <?php
                $siteLogo = \App\Models\Setting::getImageUrl('logo');
                $siteName = \App\Models\Setting::get('site_name', 'Admin Panel');
            ?>
            <?php if($siteLogo): ?>
                <img src="<?php echo e($siteLogo); ?>" alt="<?php echo e($siteName); ?>" class="brand-image img-circle elevation-3" style="opacity: .8; object-fit: contain;">
            <?php else: ?>
                <i class="fas fa-cogs brand-image fa-2x text-white" style="opacity: .8; margin-left: 10px;"></i>
            <?php endif; ?>
            <span class="brand-text font-weight-light"><?php echo e(Str::limit($siteName, 15)); ?></span>
        </a>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Sidebar Menu -->
            <nav class="mt-2">
                <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                    <!-- Dashboard -->
                    <li class="nav-item">
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <p>Dashboard</p>
                        </a>
                    </li>

                    <!-- Settings -->
                    <li class="nav-item">
                        <a href="<?php echo e(route('admin.settings.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.settings.*') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-cogs"></i>
                            <p>Website Configuration</p>
                        </a>
                    </li>

                    <!-- Content Management Header -->
                    <li class="nav-header">CONTENT MANAGEMENT</li>

                    <!-- Slider -->
                    <li class="nav-item">
                        <a href="<?php echo e(route('admin.sliders.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.sliders.*') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-images"></i>
                            <p>Slider</p>
                        </a>
                    </li>

                    <!-- Causes -->
                    <li class="nav-item">
                        <a href="<?php echo e(route('admin.causes.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.causes.*') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-heart"></i>
                            <p>Causes</p>
                        </a>
                    </li>

                    <!-- Blogs -->
                    <li class="nav-item">
                        <a href="<?php echo e(route('admin.blogs.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.blogs.*') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-blog"></i>
                            <p>Blogs</p>
                        </a>
                    </li>

                    <!-- About Page -->
                    <li class="nav-item">
                        <a href="<?php echo e(route('admin.about.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.about.*') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-info-circle"></i>
                            <p>About Page</p>
                        </a>
                    </li>

                    <!-- Gallery -->
                    <li class="nav-item">
                        <a href="<?php echo e(route('admin.gallery.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.gallery.*') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-camera"></i>
                            <p>Gallery</p>
                        </a>
                    </li>

                    <!-- Contacts -->
                    <li class="nav-item">
                        <a href="<?php echo e(route('admin.contacts.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.contacts.*') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-envelope"></i>
                            <p>Contacts</p>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </aside>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0"><?php echo $__env->yieldContent('title', 'Dashboard'); ?></h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <?php echo $__env->yieldContent('breadcrumb'); ?>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <h5><i class="icon fas fa-check"></i> Success!</h5>
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <h5><i class="icon fas fa-ban"></i> Error!</h5>
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <?php echo $__env->yieldContent('content'); ?>
        </section>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
        <strong>Copyright &copy; <?php echo e(date('Y')); ?> <a href="<?php echo e(url('/')); ?>"><?php echo e(config('app.name')); ?></a>.</strong>
        All rights reserved.
        <div class="float-right d-none d-sm-inline-block">
            <b>Version</b> 1.0.0
        </div>
    </footer>
</div>

<!-- jQuery -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.2/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>

<?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/layouts/app.blade.php ENDPATH**/ ?>