<?php if (isset($component)) { $__componentOriginal4619374cef299e94fd7263111d0abc69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4619374cef299e94fd7263111d0abc69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gov-blue to-gov-navy text-white py-16">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4 font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'ब्लॉग' : 'Blog'); ?>

                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                    <?php echo e(app()->getLocale() === 'hi' ? 'हमारे कार्यों और समुदायिक गतिविधियों के बारे में नवीनतम ब्लॉग और अपडेट पढ़ें।' : 'Read the latest blogs and updates about our work and community activities.'); ?>

                </p>
            </div>
        </div>
    </section>

    <!-- Filters and Search -->
    <section class="py-8 bg-gray-50">
        <div class="container mx-auto px-4">
            <form method="GET" action="<?php echo e(route('blog.index')); ?>" class="flex flex-wrap gap-4 items-center justify-between">
                <!-- Search -->
                <div class="flex-1 min-w-64">
                    <input type="text" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>"
                           placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'ब्लॉग खोजें...' : 'Search blogs...'); ?>"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gov-orange focus:border-transparent">
                </div>

                <!-- Category Filter -->
                <div class="min-w-48">
                    <select name="category" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gov-orange focus:border-transparent">
                        <option value=""><?php echo e(app()->getLocale() === 'hi' ? 'सभी श्रेणियां' : 'All Categories'); ?></option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->slug); ?>" <?php echo e(request('category') === $category->slug ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Sort -->
                <div class="min-w-48">
                    <select name="sort" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gov-orange focus:border-transparent">
                        <option value="latest" <?php echo e(request('sort') === 'latest' ? 'selected' : ''); ?>>
                            <?php echo e(app()->getLocale() === 'hi' ? 'नवीनतम' : 'Latest'); ?>

                        </option>
                        <option value="oldest" <?php echo e(request('sort') === 'oldest' ? 'selected' : ''); ?>>
                            <?php echo e(app()->getLocale() === 'hi' ? 'पुराने' : 'Oldest'); ?>

                        </option>
                        <option value="title" <?php echo e(request('sort') === 'title' ? 'selected' : ''); ?>>
                            <?php echo e(app()->getLocale() === 'hi' ? 'शीर्षक' : 'Title'); ?>

                        </option>
                    </select>
                </div>

                <button type="submit" class="bg-gov-orange text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    <?php echo e(app()->getLocale() === 'hi' ? 'खोजें' : 'Search'); ?>

                </button>
            </form>
        </div>
    </section>

    <!-- Blog Posts -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <?php if($blogs->count() > 0): ?>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                            <div class="aspect-video bg-gray-200 relative">
                                <?php if($blog->featured_image): ?>
                                    <img src="<?php echo e(asset('storage/' . $blog->featured_image)); ?>" 
                                         alt="<?php echo e($blog->title); ?>"
                                         class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="w-full h-full flex items-center justify-center text-gray-400">
                                        <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($blog->category): ?>
                                    <span class="absolute top-4 left-4 bg-gov-orange text-white px-3 py-1 rounded-full text-sm font-medium">
                                        <?php echo e($blog->category->name); ?>

                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                                    <a href="<?php echo e(route('blog.show', $blog->slug)); ?>" class="hover:text-gov-orange transition-colors">
                                        <?php echo e($blog->title); ?>

                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 mb-4 line-clamp-3">
                                    <?php echo e($blog->excerpt); ?>

                                </p>
                                
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    <span><?php echo e($blog->published_at ? $blog->published_at->format('M d, Y') : 'Draft'); ?></span>
                                    <span><?php echo e($blog->views); ?> <?php echo e(app()->getLocale() === 'hi' ? 'बार देखा गया' : 'views'); ?></span>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    <?php echo e($blogs->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                        <?php echo e(app()->getLocale() === 'hi' ? 'कोई ब्लॉग पोस्ट नहीं मिला' : 'No blog posts found'); ?>

                    </h3>
                    <p class="text-gray-600">
                        <?php echo e(app()->getLocale() === 'hi' ? 'कृपया अपने खोज मानदंड को समायोजित करें या बाद में वापस जांचें।' : 'Please adjust your search criteria or check back later.'); ?>

                    </p>
                </div>
            <?php endif; ?>
        </div>
    </section>


 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $attributes = $__attributesOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__attributesOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $component = $__componentOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__componentOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/blog.blade.php ENDPATH**/ ?>