<?php $__env->startSection('title', 'Donation Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Donation Dashboard</h1>
        <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-primary">
            <i class="fas fa-list mr-2"></i>View All Donations
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Donations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['total_donations']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-heart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Amount Raised
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹<?php echo e(number_format($stats['total_amount'], 2)); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Completed Donations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['completed_donations']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Donations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['pending_donations']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Statistics -->
    <div class="row mb-4">
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 text-center">
                            <div class="mb-2">
                                <i class="fas fa-qrcode fa-3x text-warning"></i>
                            </div>
                            <h4 class="font-weight-bold"><?php echo e($stats['qr_donations']); ?></h4>
                            <p class="text-muted">QR Code Payments</p>
                        </div>
                        <div class="col-6 text-center">
                            <div class="mb-2">
                                <i class="fas fa-credit-card fa-3x text-primary"></i>
                            </div>
                            <h4 class="font-weight-bold"><?php echo e($stats['gateway_donations']); ?></h4>
                            <p class="text-muted">Gateway Payments</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Donations (<?php echo e(date('Y')); ?>)</h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Donations -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Recent Donations</h6>
        </div>
        <div class="card-body">
            <?php if($recent_donations->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Donor</th>
                                <th>Amount</th>
                                <th>Cause</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $recent_donations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $donation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($donation->id); ?></td>
                                    <td><?php echo e($donation->is_anonymous ? 'Anonymous' : $donation->donor_name); ?></td>
                                    <td>₹<?php echo e(number_format($donation->amount, 2)); ?></td>
                                    <td>
                                        <?php if($donation->cause): ?>
                                            <span class="badge badge-info"><?php echo e(Str::limit($donation->cause->title, 20)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">General</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($donation->payment_method === 'qr_code'): ?>
                                            <span class="badge badge-warning">QR Code</span>
                                        <?php else: ?>
                                            <span class="badge badge-primary">Gateway</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php switch($donation->status):
                                            case ('pending'): ?>
                                                <span class="badge badge-warning">Pending</span>
                                                <?php break; ?>
                                            <?php case ('completed'): ?>
                                                <span class="badge badge-success">Completed</span>
                                                <?php break; ?>
                                            <?php case ('failed'): ?>
                                                <span class="badge badge-danger">Failed</span>
                                                <?php break; ?>
                                            <?php case ('refunded'): ?>
                                                <span class="badge badge-secondary">Refunded</span>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </td>
                                    <td><?php echo e($donation->created_at->format('M d, H:i')); ?></td>
                                    <td>
                                        <a href="<?php echo e(route('admin.donations.show', $donation)); ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No donations yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly donations chart
const ctx = document.getElementById('monthlyChart').getContext('2d');
const monthlyData = <?php echo json_encode($monthly_stats, 15, 512) ?>;

const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const data = new Array(12).fill(0);
const amounts = new Array(12).fill(0);

monthlyData.forEach(item => {
    data[item.month - 1] = item.count;
    amounts[item.month - 1] = item.total;
});

new Chart(ctx, {
    type: 'bar',
    data: {
        labels: months,
        datasets: [{
            label: 'Number of Donations',
            data: data,
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    afterLabel: function(context) {
                        const monthIndex = context.dataIndex;
                        return 'Amount: ₹' + amounts[monthIndex].toLocaleString();
                    }
                }
            }
        }
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/donations/dashboard.blade.php ENDPATH**/ ?>