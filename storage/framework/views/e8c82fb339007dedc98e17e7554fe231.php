<?php $__env->startSection('title', 'Donation Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h4 class="h3 mb-0 text-gray-800">Donation Dashboard</h4>
        <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-primary">
            <i class="fas fa-list mr-2"></i>View All Donations
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Donations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['total_donations']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-heart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Amount Raised
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹<?php echo e(number_format($stats['total_amount'], 2)); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Completed Donations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['completed_donations']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Donations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['pending_donations']); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Statistics -->
    <div class="row mb-4">
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 text-center">
                            <div class="mb-2">
                                <i class="fas fa-qrcode fa-3x text-warning"></i>
                            </div>
                            <h4 class="font-weight-bold"><?php echo e($stats['qr_donations']); ?></h4>
                            <p class="text-muted">QR Code Payments</p>
                        </div>
                        <div class="col-6 text-center">
                            <div class="mb-2">
                                <i class="fas fa-credit-card fa-3x text-primary"></i>
                            </div>
                            <h4 class="font-weight-bold"><?php echo e($stats['gateway_donations']); ?></h4>
                            <p class="text-muted">Gateway Payments</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Donations (<?php echo e(date('Y')); ?>)</h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Donations -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Recent Donations</h6>
                <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-list mr-1"></i>View All
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if($recent_donations->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>Donor</th>
                                <th>Amount</th>
                                <th>Cause</th>
                                <th>Method</th>
                                <th>Transaction Photo</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="donations-table-body">
                            <?php $__currentLoopData = $recent_donations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $donation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($donation->is_anonymous ? 'Anonymous' : $donation->donor_name); ?></strong>
                                            <?php if(!$donation->is_anonymous && $donation->donor_email): ?>
                                                <br><small class="text-muted"><?php echo e($donation->donor_email); ?></small>
                                            <?php endif; ?>
                                            <?php if($donation->donor_phone): ?>
                                                <br><small class="text-muted"><i class="fas fa-phone"></i> <?php echo e($donation->donor_phone); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <strong class="text-success">₹<?php echo e(number_format($donation->amount, 2)); ?></strong>
                                    </td>
                                    <td>
                                        <?php if($donation->cause): ?>
                                            <span class="badge badge-info"><?php echo e(Str::limit($donation->cause->title, 20)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">General</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php switch($donation->payment_method):
                                            case ('qr_code'): ?>
                                                <span class="badge badge-warning"><i class="fas fa-qrcode"></i> QR Code</span>
                                                <?php break; ?>
                                            <?php case ('gateway'): ?>
                                                <span class="badge badge-primary"><i class="fas fa-credit-card"></i> Gateway</span>
                                                <?php break; ?>
                                            <?php case ('bank_transfer'): ?>
                                                <span class="badge badge-info"><i class="fas fa-university"></i> Bank Transfer</span>
                                                <?php break; ?>
                                            <?php case ('cash'): ?>
                                                <span class="badge badge-success"><i class="fas fa-money-bill"></i> Cash</span>
                                                <?php break; ?>
                                            <?php default: ?>
                                                <span class="badge badge-secondary"><?php echo e(ucfirst($donation->payment_method)); ?></span>
                                        <?php endswitch; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if($donation->payment_screenshot): ?>
                                            <img src="<?php echo e($donation->payment_screenshot_url); ?>"
                                                 alt="Transaction Screenshot"
                                                 class="img-thumbnail"
                                                 style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
                                                 onclick="openImageModal('<?php echo e($donation->payment_screenshot_url); ?>', '<?php echo e($donation->donor_name); ?>')">
                                        <?php else: ?>
                                            <span class="text-muted"><i class="fas fa-image-slash"></i> No Image</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php switch($donation->status):
                                            case ('pending'): ?>
                                                <span class="badge badge-warning"><i class="fas fa-clock"></i> Pending</span>
                                                <?php break; ?>
                                            <?php case ('completed'): ?>
                                                <span class="badge badge-success"><i class="fas fa-check"></i> Completed</span>
                                                <?php break; ?>
                                            <?php case ('failed'): ?>
                                                <span class="badge badge-danger"><i class="fas fa-times"></i> Failed</span>
                                                <?php break; ?>
                                            <?php case ('refunded'): ?>
                                                <span class="badge badge-secondary"><i class="fas fa-undo"></i> Refunded</span>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </td>
                                    <td>
                                        <small><?php echo e($donation->created_at->format('M d, Y')); ?><br><?php echo e($donation->created_at->format('H:i')); ?></small>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('admin.donations.show', $donation)); ?>" class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No donations yet.</p>
                </div>
            <?php endif; ?>

            <?php if($recent_donations->count() > 0): ?>
                <!-- Record Count -->
                <div class="mt-3">
                    <p class="text-muted" id="record-count">
                        Showing <?php echo e($recent_donations->count()); ?> of <?php echo e($recent_donations->total()); ?> records
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly donations chart
const ctx = document.getElementById('monthlyChart').getContext('2d');
const monthlyData = <?php echo json_encode($monthly_stats, 15, 512) ?>;

const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const data = new Array(12).fill(0);
const amounts = new Array(12).fill(0);

monthlyData.forEach(item => {
    data[item.month - 1] = item.count;
    amounts[item.month - 1] = item.total;
});

new Chart(ctx, {
    type: 'bar',
    data: {
        labels: months,
        datasets: [{
            label: 'Number of Donations',
            data: data,
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    afterLabel: function(context) {
                        const monthIndex = context.dataIndex;
                        return 'Amount: ₹' + amounts[monthIndex].toLocaleString();
                    }
                }
            }
        }
    }
});

// Image modal function
function openImageModal(src, donorName) {
    document.getElementById('modalImage').src = src;
    document.getElementById('modalTitle').textContent = 'Transaction Screenshot - ' + donorName;
    $('#imageModal').modal('show');
}
</script>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Transaction Screenshot</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Transaction Screenshot" class="img-fluid">
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/infinite-scroll.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if($recent_donations->count() > 0): ?>
    const infiniteScroll = new InfiniteScroll({
        container: document.querySelector('.card'),
        tableBody: document.getElementById('donations-table-body'),
        url: '<?php echo e(route("admin.donations.dashboard")); ?>',
        renderRow: function(donation, rowNumber) {
            const row = document.createElement('tr');

            // Format donor info
            let donorInfo = donation.is_anonymous ? 'Anonymous' : donation.donor_name;
            if (!donation.is_anonymous && donation.donor_email) {
                donorInfo += `<br><small class="text-muted">${donation.donor_email}</small>`;
            }
            if (donation.donor_phone) {
                donorInfo += `<br><small class="text-muted"><i class="fas fa-phone"></i> ${donation.donor_phone}</small>`;
            }

            // Format cause
            let causeInfo = donation.cause ?
                `<span class="badge badge-info">${donation.cause.title.substring(0, 20)}${donation.cause.title.length > 20 ? '...' : ''}</span>` :
                '<span class="text-muted">General</span>';

            // Format payment method
            let paymentMethod = '';
            switch(donation.payment_method) {
                case 'qr_code':
                    paymentMethod = '<span class="badge badge-warning"><i class="fas fa-qrcode"></i> QR Code</span>';
                    break;
                case 'gateway':
                    paymentMethod = '<span class="badge badge-primary"><i class="fas fa-credit-card"></i> Gateway</span>';
                    break;
                case 'bank_transfer':
                    paymentMethod = '<span class="badge badge-info"><i class="fas fa-university"></i> Bank Transfer</span>';
                    break;
                case 'cash':
                    paymentMethod = '<span class="badge badge-success"><i class="fas fa-money-bill"></i> Cash</span>';
                    break;
                default:
                    paymentMethod = `<span class="badge badge-secondary">${donation.payment_method.charAt(0).toUpperCase() + donation.payment_method.slice(1)}</span>`;
            }

            // Format transaction photo
            let transactionPhoto = '<span class="text-muted"><i class="fas fa-image-slash"></i> No Image</span>';
            if (donation.payment_screenshot) {
                const imageUrl = `<?php echo e(asset('storage/')); ?>/${donation.payment_screenshot}`;
                transactionPhoto = `<img src="${imageUrl}"
                                   alt="Transaction Screenshot"
                                   class="img-thumbnail"
                                   style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
                                   onclick="openImageModal('${imageUrl}', '${donation.donor_name}')">`;
            }

            // Format status
            let statusBadge = '';
            switch(donation.status) {
                case 'pending':
                    statusBadge = '<span class="badge badge-warning"><i class="fas fa-clock"></i> Pending</span>';
                    break;
                case 'completed':
                    statusBadge = '<span class="badge badge-success"><i class="fas fa-check"></i> Completed</span>';
                    break;
                case 'failed':
                    statusBadge = '<span class="badge badge-danger"><i class="fas fa-times"></i> Failed</span>';
                    break;
                case 'refunded':
                    statusBadge = '<span class="badge badge-secondary"><i class="fas fa-undo"></i> Refunded</span>';
                    break;
            }

            // Format date
            const createdAt = new Date(donation.created_at);
            const dateStr = createdAt.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
            const timeStr = createdAt.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            row.innerHTML = `
                <td>${rowNumber}</td>
                <td>
                    <div>${donorInfo}</div>
                </td>
                <td>
                    <strong class="text-success">₹${parseFloat(donation.amount).toLocaleString('en-IN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</strong>
                </td>
                <td>${causeInfo}</td>
                <td>${paymentMethod}</td>
                <td class="text-center">${transactionPhoto}</td>
                <td>${statusBadge}</td>
                <td>
                    <small>${dateStr}<br>${timeStr}</small>
                </td>
                <td>
                    <a href="<?php echo e(route('admin.donations.show', '')); ?>/${donation.id}" class="btn btn-sm btn-outline-primary" title="View Details">
                        <i class="fas fa-eye"></i>
                    </a>
                </td>
            `;

            return row;
        }
    });

    // Update record count when new data is loaded
    const originalAppendRows = infiniteScroll.appendRows;
    infiniteScroll.appendRows = function(items) {
        originalAppendRows.call(this, items);
        this.updateRecordCount();
    };
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/admin/donations/dashboard.blade.php ENDPATH**/ ?>