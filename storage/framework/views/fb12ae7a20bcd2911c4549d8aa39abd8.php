<!-- Donation Modal -->
<div id="donationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
        <!-- Modal Header -->
        <div class="bg-gov-blue text-white p-6 rounded-t-lg">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-bold font-hindi">
                    <?php echo e(app()->getLocale() === 'hi' ? 'दान करें' : 'Make Donation'); ?>

                </h2>
                <button onclick="closeDonationModal()" class="text-white hover:text-gray-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <form id="donationForm" action="<?php echo e(route('donate.process')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="cause_id" id="modal_cause_id" value="">
                <input type="hidden" name="payment_method" value="qr_code">
                
                <!-- Amount Section -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'दान की राशि (₹)' : 'Donation Amount (₹)'); ?> *
                    </label>
                    
                    <!-- Quick Amount Buttons -->
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <button type="button" onclick="setModalAmount(500)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                            ₹500
                        </button>
                        <button type="button" onclick="setModalAmount(1000)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                            ₹1,000
                        </button>
                        <button type="button" onclick="setModalAmount(2500)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                            ₹2,500
                        </button>
                        <button type="button" onclick="setModalAmount(5000)" class="bg-gray-100 hover:bg-gov-blue hover:text-white border border-gray-300 py-3 px-4 rounded-lg transition-colors">
                            ₹5,000
                        </button>
                    </div>
                    
                    <input type="number" name="amount" id="modal_amount" min="1" step="0.01" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                           placeholder="<?php echo e(app()->getLocale() === 'hi' ? 'कस्टम राशि दर्ज करें' : 'Enter custom amount'); ?>">
                </div>

                <!-- QR Code Section -->
                <?php if(\App\Models\Setting::get('qr_payment_enabled', '1') == '1'): ?>
                <div class="mb-6 text-center">
                    <label class="block text-gray-700 text-sm font-bold mb-4 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'भुगतान QR कोड' : 'Payment QR Code'); ?>

                    </label>
                    
                    <?php
                        $qrCodeUrl = \App\Models\Setting::getImageUrl('payment_qr_code');
                    ?>
                    
                    <?php if($qrCodeUrl): ?>
                        <div class="bg-gray-50 p-4 rounded-lg inline-block">
                            <img src="<?php echo e($qrCodeUrl); ?>" alt="Payment QR Code" class="w-48 h-48 object-contain mx-auto border rounded shadow">
                        </div>
                        <p class="mt-3 text-gray-600 text-sm font-hindi">
                            <?php echo e(\App\Models\Setting::get('payment_instructions', app()->getLocale() === 'hi' ? 'कृपया ऊपर दिए गए QR कोड को स्कैन करके भुगतान करें।' : 'Please scan the QR code above to make your payment.')); ?>

                        </p>
                    <?php else: ?>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <p class="text-yellow-800 font-hindi">
                                <?php echo e(app()->getLocale() === 'hi' ? 'QR कोड उपलब्ध नहीं है। कृपया व्यवस्थापक से संपर्क करें।' : 'QR code not available. Please contact administrator.'); ?>

                            </p>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Hidden fields for donor information (will be populated from previous page) -->
                <input type="hidden" name="donor_name" id="modal_donor_name">
                <input type="hidden" name="donor_email" id="modal_donor_email">
                <input type="hidden" name="donor_phone" id="modal_donor_phone">

                <!-- Payment Screenshot Upload -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'भुगतान स्क्रीनशॉट' : 'Payment Screenshot'); ?> *
                    </label>
                    <input type="file" name="payment_screenshot" accept="image/*" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gov-blue"
                           onchange="previewScreenshot(this)">
                    <small class="text-gray-500 font-hindi">
                        <?php echo e(app()->getLocale() === 'hi' ? 'भुगतान के बाद स्क्रीनशॉट अपलोड करें' : 'Upload screenshot after making payment'); ?>

                    </small>
                    
                    <!-- Screenshot Preview -->
                    <div id="screenshot-preview" class="mt-3 hidden">
                        <img id="screenshot-preview-img" class="w-32 h-32 object-cover border rounded">
                        <small class="block text-gray-500 mt-1"><?php echo e(app()->getLocale() === 'hi' ? 'स्क्रीनशॉट पूर्वावलोकन' : 'Screenshot Preview'); ?></small>
                    </div>
                </div>

                <!-- Hidden fields for optional data -->
                <input type="hidden" name="message" id="modal_message">
                <input type="hidden" name="is_anonymous" id="modal_is_anonymous" value="0">

                <!-- Submit Button -->
                <div class="flex gap-3">
                    <button type="button" onclick="closeDonationModal()" 
                            class="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                        <?php echo e(app()->getLocale() === 'hi' ? 'रद्द करें' : 'Cancel'); ?>

                    </button>
                    <button type="submit" 
                            class="flex-1 bg-gov-orange hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                        <?php echo e(app()->getLocale() === 'hi' ? 'दान जमा करें' : 'Submit Donation'); ?>

                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openDonationModal(causeId = null) {
    document.getElementById('modal_cause_id').value = causeId || '';

    // Populate donor information from the main form if available
    const donorName = document.querySelector('input[name="donor_name"]');
    const donorEmail = document.querySelector('input[name="donor_email"]');
    const donorPhone = document.querySelector('input[name="donor_phone"]');
    const message = document.querySelector('textarea[name="message"]');
    const isAnonymous = document.querySelector('input[name="is_anonymous"]');

    if (donorName) document.getElementById('modal_donor_name').value = donorName.value;
    if (donorEmail) document.getElementById('modal_donor_email').value = donorEmail.value;
    if (donorPhone) document.getElementById('modal_donor_phone').value = donorPhone.value;
    if (message) document.getElementById('modal_message').value = message.value;
    if (isAnonymous) document.getElementById('modal_is_anonymous').value = isAnonymous.checked ? '1' : '0';

    document.getElementById('donationModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeDonationModal() {
    document.getElementById('donationModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    // Reset form
    document.getElementById('donationForm').reset();
    document.getElementById('screenshot-preview').classList.add('hidden');
}

function setModalAmount(amount) {
    document.getElementById('modal_amount').value = amount;
}

function previewScreenshot(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('screenshot-preview');
            const previewImg = document.getElementById('screenshot-preview-img');
            previewImg.src = e.target.result;
            preview.classList.remove('hidden');
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Close modal when clicking outside
document.getElementById('donationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDonationModal();
    }
});
</script>
<?php /**PATH /opt/lampp/htdocs/ngonew/resources/views/components/donation-modal.blade.php ENDPATH**/ ?>