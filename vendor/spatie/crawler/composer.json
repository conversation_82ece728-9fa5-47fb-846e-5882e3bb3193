{"name": "spatie/crawler", "description": "Crawl all internal links found on a website", "keywords": ["spatie", "crawler", "link", "website"], "homepage": "https://github.com/spatie/crawler", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "guzzlehttp/guzzle": "^7.3", "guzzlehttp/psr7": "^2.0", "illuminate/collections": "^10.0|^11.0|^12.0", "nicmart/tree": "^0.9", "spatie/browsershot": "^5.0.5", "spatie/robots-txt": "^2.0", "symfony/dom-crawler": "^6.0|^7.0"}, "require-dev": {"pestphp/pest": "^2.0|^3.0", "spatie/ray": "^1.37"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "phpstan/extension-installer": true}}, "autoload": {"psr-4": {"Spatie\\Crawler\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\Crawler\\Test\\": "tests"}}, "minimum-stability": "dev", "prefer-stable": true}