{"name": "spatie/laravel-sitemap", "description": "Create and generate sitemaps with ease", "keywords": ["spatie", "laravel-sitemap"], "homepage": "https://github.com/spatie/laravel-sitemap", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "require": {"php": "^8.2||^8.3||^8.4", "guzzlehttp/guzzle": "^7.8", "illuminate/support": "^11.0|^12.0", "nesbot/carbon": "^2.71|^3.0", "spatie/crawler": "^8.0.1", "spatie/laravel-package-tools": "^1.16.1", "symfony/dom-crawler": "^6.3.4|^7.0"}, "require-dev": {"mockery/mockery": "^1.6.6", "orchestra/testbench": "^9.0|^10.0", "pestphp/pest": "^3.7.4", "spatie/pest-plugin-snapshots": "^2.1", "spatie/phpunit-snapshot-assertions": "^5.1.2", "spatie/temporary-directory": "^2.2"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"laravel": {"providers": ["Spatie\\Sitemap\\SitemapServiceProvider"]}}, "autoload": {"psr-4": {"Spatie\\Sitemap\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\Sitemap\\Test\\": "tests"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"test": "vendor/bin/pest"}}