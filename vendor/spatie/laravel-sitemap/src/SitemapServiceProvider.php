<?php

namespace Spatie\Sitemap;

use <PERSON><PERSON>\Crawler\Crawler;
use <PERSON><PERSON>\LaravelPackageTools\Package;
use <PERSON>tie\LaravelPackageTools\PackageServiceProvider;

class SitemapServiceProvider extends PackageServiceProvider
{
    public function configurePackage(Package $package): void
    {
        $package
            ->name('laravel-sitemap')
            ->hasConfigFile()
            ->hasViews();
    }

    public function packageRegistered(): void
    {
        $this->app->when(SitemapGenerator::class)
            ->needs(Crawler::class)
            ->give(static fn (): Crawler => Crawler::create(config('sitemap.guzzle_options')));
    }
}
