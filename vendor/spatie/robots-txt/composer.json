{"name": "spatie/robots-txt", "description": "Determine if a page may be crawled from robots.txt and robots meta tags", "keywords": ["spatie", "robots-txt"], "homepage": "https://github.com/spatie/robots-txt", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "require": {"php": "^8.1"}, "require-dev": {"phpunit/phpunit": "^11.5.2"}, "autoload": {"psr-4": {"Spatie\\Robots\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\Robots\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/phpunit", "test-coverage": "phpunit --coverage-html coverage"}, "config": {"sort-packages": true}}